# Supabase Full-Text Search (FTS) Database Modification Queries

These SQL queries are designed to enhance your Supabase database tables with Full-Text Search capabilities, supporting both English and Bulgarian. Each query adds two `tsvector` columns (`fts_document_en` and `fts_document_bg`) that automatically concatenate and convert relevant text fields into a searchable format, optimized for each language. GIN indexes are also created on these columns for fast search performance.

The Supabase documentation confirms that using `to_tsvector()` with generated columns and GIN indexes is the recommended and most performant way to implement full-text search in PostgreSQL (and thus Supabase). This approach ensures accurate, language-aware search results and scales efficiently with your data.

**Important:** After executing these queries, you _must_ regenerate your database type definitions. If you are using the Supabase CLI, you can typically do this with the command:

```bash
supabase gen types typescript --local > types/database.types.ts
```

Or, if you use a different method (e.g., a specific script or Supabase dashboard feature), please follow that to update `types/database.types.ts`.

---

### SQL Queries:

**1. Table: `prescriptions`**
_Relevant for: Prescriptions Tab_
_Searches fields: `name`, `notes`_

```sql
ALTER TABLE public.prescriptions
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX prescriptions_fts_document_en_idx ON public.prescriptions USING gin(fts_document_en);

ALTER TABLE public.prescriptions
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX prescriptions_fts_document_bg_idx ON public.prescriptions USING gin(fts_document_bg);
```

**2. Table: `labresults`**
_Relevant for: Lab Results Tab_
_Searches fields: `lab_name`, `password`, `patient_name`, `phone_number1`, `phone_number2`, `phone_number3`, `website`_
**Note:** The `password` field is included below. Please review and remove `coalesce(password, '') || ' ' ||` if passwords should not be searchable.

```sql
ALTER TABLE public.labresults
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(lab_name, '') || ' ' ||
        coalesce(password, '') || ' ' || -- Review: Remove if password should not be searchable
        coalesce(patient_name, '') || ' ' ||
        coalesce(phone_number1, '') || ' ' ||
        coalesce(phone_number2, '') || ' ' ||
        coalesce(phone_number3, '') || ' ' ||
        coalesce(website, '')
    )
) STORED;

CREATE INDEX labresults_fts_document_en_idx ON public.labresults USING gin(fts_document_en);

ALTER TABLE public.labresults
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(lab_name, '') || ' ' ||
        coalesce(password, '') || ' ' || -- Review: Remove if password should not be searchable
        coalesce(patient_name, '') || ' ' ||
        coalesce(phone_number1, '') || ' ' ||
        coalesce(phone_number2, '') || ' ' ||
        coalesce(phone_number3, '') || ' ' ||
        coalesce(website, '')
    )
) STORED;

CREATE INDEX labresults_fts_document_bg_idx ON public.labresults USING gin(fts_document_bg);
```

**3. Table: `user_meds`**
_Relevant for: User's Medications Tab_
_Searches fields: `name`, `description`, `notes`_

```sql
ALTER TABLE public.user_meds
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(description, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX user_meds_fts_document_en_idx ON public.user_meds USING gin(fts_document_en);

ALTER TABLE public.user_meds
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '') || ' ' ||
        coalesce(description, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX user_meds_fts_document_bg_idx ON public.user_meds USING gin(fts_document_bg);
```

**4. Table: `allmeds`**
_Relevant for: All Medications Tab_
_Searches fields: `name`, `description`_

```sql
ALTER TABLE public.allmeds
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(description, '')
    )
) STORED;

CREATE INDEX allmeds_fts_document_en_idx ON public.allmeds USING gin(fts_document_en);

ALTER TABLE public.allmeds
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '') || ' ' ||
        coalesce(description, '')
    )
) STORED;

CREATE INDEX allmeds_fts_document_bg_idx ON public.allmeds USING gin(fts_document_bg);
```

**5. Table: `user_doctors`**
_Relevant for: User's Doctors Tab_
_Searches fields: `name`, `city`, `specialty`, `workplace`, `workplace_address`, `notes`, `phone_number`_

```sql
ALTER TABLE public.user_doctors
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(notes, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX user_doctors_fts_document_en_idx ON public.user_doctors USING gin(fts_document_en);

ALTER TABLE public.user_doctors
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(notes, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX user_doctors_fts_document_bg_idx ON public.user_doctors USING gin(fts_document_bg);
```

**6. Table: `alldoctors`**
_Relevant for: All Doctors Tab_
_Searches fields: `name`, `city`, `specialty`, `workplace`, `workplace_address`, `phone_number`_

```sql
ALTER TABLE public.alldoctors
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX alldoctors_fts_document_en_idx ON public.alldoctors USING gin(fts_document_en);

ALTER TABLE public.alldoctors
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX alldoctors_fts_document_bg_idx ON public.alldoctors USING gin(fts_document_bg);
```

**7. Table: `patients`**
_Relevant for: Patients Tab_
_Searches fields: `name`_

```sql
ALTER TABLE public.patients
ADD COLUMN fts_document_en tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '')
    )
) STORED;

CREATE INDEX patients_fts_document_en_idx ON public.patients USING gin(fts_document_en);

ALTER TABLE public.patients
ADD COLUMN fts_document_bg tsvector
GENERATED ALWAYS AS (
    to_tsvector('bulgarian',
        coalesce(name, '')
    )
) STORED;

CREATE INDEX patients_fts_document_bg_idx ON public.patients USING gin(fts_document_bg);
```
