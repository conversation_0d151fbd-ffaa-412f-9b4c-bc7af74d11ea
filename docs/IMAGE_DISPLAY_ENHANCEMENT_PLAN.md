# Plan: Enhanced Image Display for Forms

**Objective:** Modify `PrescriptionForm.tsx`, `LabResultForm.tsx`, and `MedicineForm.tsx` to display images more prominently, similar to the provided UI screenshot. `PrescriptionForm.tsx` will use a carousel to display its front and back images.

## Step 1: Redesign the `ImageCapture.tsx` Component (Completed)

The existing `components/common/ImageCapture.tsx` component will be refactored to support a more prominent image display with overlay controls when an image is present.

**Key Changes to `ImageCapture.tsx`:**

1.  **Conditional Rendering Logic:**

    - **If no image is selected (`imageUri` is null):**
      - Display a placeholder view (e.g., an icon with text like "No Image Selected" or "Add Image").
      - Show the "Take Photo" and "Choose from Library" buttons as they are currently. The `captureButtonText` and `libraryButtonText` props will still be used.
    - **If an image is selected (`imageUri` is present):**
      - Display the image in a larger, more prominent view. The `previewHeight` prop (e.g., defaulting to 200-250px, or a prop like `imageHeight`) will define the height of this preview. The image should ideally be displayed with `contentFit="cover"` or `contentFit="contain"` based on desired aesthetics, filling the width of its container.
      - **Overlay Controls:** On top of this prominent image preview, include:
        - **Remove Button:** An icon button (e.g., `trash-outline`) positioned typically at the top-right. This will call the existing `handleRemoveImage` logic (triggering the `onRemove` prop).
        - **Change Button:** An icon button (e.g., `pencil-outline` or `camera-reverse-outline`) positioned, for example, at the top-left.
          - When clicked, this "Change" button should trigger a state (e.g., `isChangingImage = true`).
          - When `isChangingImage` is true, the component could temporarily hide the large image preview and instead show the "Take Photo" and "Choose from Library" buttons, allowing the user to select a new image. Once a new image is captured/selected or the action is canceled, `isChangingImage` resets.
      - The existing `ImageViewing` component for full-screen viewing on tap can remain.

2.  **Props:**

    - Review existing props like `showPreview` (likely always true now for the main view) and `previewHeight`.
    - Consider a new prop like `aspectRatio` (e.g., "16:9", "4:3", "1:1") or specific `imageHeight` and `imageWidth` props to give more control to the parent form over the image display dimensions.

3.  **Styling:**
    - Update class names for Tailwind CSS to ensure the prominent display, proper aspect ratio handling for the image, and correct positioning of overlay buttons.
    - Ensure dark mode compatibility is maintained.

## Step 2: Integrate Updated `ImageCapture.tsx` into `MedicineForm.tsx` and `LabResultForm.tsx` (Completed)

1.  **Placement:** In `components/medications/MedicineForm.tsx` and `components/labresults/LabResultForm.tsx`, ensure the `ImageCapture` component is placed at the top of the form's content, likely directly within the `ScrollView` but before other input fields. This will make the image the first interactive element related to file attachment.
2.  **Layout Adjustment:** The parent views might need styling adjustments (e.g., removing padding around the `ImageCapture` if it handles its own, or adjusting margins) to accommodate the larger image display seamlessly.
3.  **Prop Usage:**
    - Pass the `initialImage` (derived from `initialValues` and `useSupabaseStorageUrl`).
    - Pass `onImageCaptured` and `onRemove` handlers as currently implemented.
    - The `ImageCapture` component will now internally handle how it displays based on whether an image is present.
    - Utilize any new dimension/aspect ratio props defined in Step 1.

## Step 3: Implement Carousel for `PrescriptionForm.tsx` (Completed)

`components/prescriptions/PrescriptionForm.tsx` needs to display two images (front and back) using `react-native-reanimated-carousel`.

1.  **Install Library:**

    - Run `npx expo install react-native-reanimated-carousel react-native-reanimated react-native-gesture-handler`. Ensure `react-native-reanimated` and `react-native-gesture-handler` are properly set up in your `babel.config.js` and at the entry point of your app (`App.tsx` or similar) as per their documentation.

2.  **Structure in `PrescriptionForm.tsx`:**

    - Import `Carousel` from `react-native-reanimated-carousel`.
    - Replace the two separate `ImageCapture` blocks with the `Carousel` component.
    - The carousel will have two "slides," one for the front image and one for the back image. Each slide will contain an instance of the redesigned `ImageCapture` component.

    ```tsx
    // At the top of PrescriptionForm.tsx
    import Carousel from "react-native-reanimated-carousel";
    import { Dimensions } from "react-native"; // For carousel width

    // ... inside the component
    const windowWidth = Dimensions.get("window").width;
    const carouselItemWidth = windowWidth - 32; // Example: full width with some padding (16px on each side)
    const carouselHeight = 250; // Or desired height

    // ... in the JSX for image section
    <View className="my-4">
      <Text className="text-base font-semibold text-foreground dark:text-neutral-100 mb-2">
        Prescription Images
      </Text>
      <Carousel
        loop={false} // Or true, depending on preference
        width={carouselItemWidth}
        height={carouselHeight}
        autoPlay={false}
        data={[
          {
            type: "front",
            uri: currentFrontImageUri,
            initial: initialFrontUrl,
          },
          { type: "back", uri: currentBackImageUri, initial: initialBackUrl },
        ]}
        scrollAnimationDuration={500}
        // onSnapToItem={(index) => console.log('current image:', index)} // Optional: for pagination or state tracking
        renderItem={({ item, index }) => (
          <View
            key={item.type}
            className="flex-1 justify-center items-center bg-muted dark:bg-neutral-800 rounded-lg overflow-hidden"
            style={{ width: carouselItemWidth, height: carouselHeight }}
          >
            <Text className="text-sm font-medium text-foreground dark:text-neutral-300 my-1 absolute top-1 z-10">
              {item.type === "front" ? "Front Side" : "Back Side"}
            </Text>
            <ImageCapture
              initialImage={
                newLocalFrontImageUri && item.type === "front"
                  ? newLocalFrontImageUri
                  : newLocalBackImageUri && item.type === "back"
                  ? newLocalBackImageUri
                  : item.uri ?? undefined
              } // Prioritize new local URIs
              onImageCaptured={(result) =>
                handleImageCaptured(result, item.type as "front" | "back")
              }
              onRemove={() => {
                if (item.type === "front") {
                  setCurrentFrontImageUri(null);
                  setNewLocalFrontImageUri(null);
                  setDeleteFrontImage(true);
                } else {
                  setCurrentBackImageUri(null);
                  setNewLocalBackImageUri(null);
                  setDeleteBackImage(true);
                }
              }}
              onError={(error) => Alert.alert("Image Error", error.message)}
              captureButtonText={
                item.type === "front" ? "Capture Front" : "Capture Back"
              }
              libraryButtonText={
                item.type === "front" ? "Choose Front" : "Choose Back"
              }
              previewHeight={carouselHeight - 30} // Adjust to fit text or remove if ImageCapture handles height fully
            />
          </View>
        )}
        // Consider adding pagination dots if desired, potentially with a separate state and custom component
      />
    </View>;
    ```

3.  **State Management for Carousel Data:**
    - The `data` prop for the `Carousel` will be derived from `currentFrontImageUri`, `currentBackImageUri`, `newLocalFrontImageUri`, and `newLocalBackImageUri`. Ensure this array is updated correctly when images change so the carousel re-renders with the new URIs.
    - The `handleImageCaptured` logic in `PrescriptionForm.tsx` will update the respective state variables (`currentFrontImageUri`, `newLocalFrontImageUri`, etc.), which in turn will cause the `Carousel`'s `data` prop to update and re-render the correct `ImageCapture` instance.

## Step 4: Styling and Final Touches (Completed)

1.  **Consistency:** Ensure the image display and overlay controls have a consistent look and feel across all three forms. The prominent image should be the focal point of the attachment section.
2.  **Header Icons (Clarification):** The back arrow, share, and favorite icons from the user's example screenshot are part of the screen's header navigation, not overlays on the image _within the form_. This plan focuses on the form's image presentation. If those icons are needed _as image overlays_, `ImageCapture.tsx` would need further modification, or a new wrapper component, which is out of scope for _this specific plan_.
3.  **Accessibility:** Ensure tap targets for overlay buttons are adequately sized. Add appropriate accessibility labels.
4.  **Testing:** Thoroughly test on both iOS and Android, and in light/dark modes. Test image capture, selection from library, removal, and changing images. Verify carousel behavior in `PrescriptionForm.tsx`, including swipe gestures and image updates.

## Step 5: Documentation (This File) (Completed)

This markdown document (`IMAGE_DISPLAY_ENHANCEMENT_PLAN.md`) outlines the plan and serves as a reference.

This refined plan includes using `react-native-reanimated-carousel` and provides more detailed guidance for its implementation in `PrescriptionForm.tsx`. Remember to follow the installation instructions for `react-native-reanimated-carousel` and its dependencies (`react-native-reanimated`, `react-native-gesture-handler`) carefully.
