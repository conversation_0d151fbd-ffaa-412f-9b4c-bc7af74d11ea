# Medicine OCR Implementation Plan

## Overview

This document outlines the implementation plan for adding medicine OCR functionality to the app. Users will be able to scan pharmacy bills/receipts to automatically extract and add multiple medicines to their collection.

## User Flow

1. **User initiates scan**: User taps "+" button in tabs layout → selects "From Bill" from dropdown
2. **Direct image capture**: Immediately shows camera/gallery selection alert (no extra screen)
3. **Navigate to processing**: After image capture, navigate to OCR screen with image URI
4. **OCR processing**: Screen shows captured image while OCR processes in background
5. **Review extracted data**: User sees list of extracted medicines with editable fields
6. **Assign patients**: User can select patients for each medicine
7. **Save medicines**: User can save all medicines, which appear as normal medicines with receipt image

## Current System Analysis

### Existing Infrastructure
- ✅ **OCR Edge Function**: `supabase/functions/image-ocr-openai/index.ts` exists with OpenAI Vision API integration
- ✅ **Image Capture**: `hooks/useImageCapture.ts` and `components/common/ImageCapture.tsx` handle camera/gallery selection
- ✅ **Medicine Form**: `components/medications/MedicineForm.tsx` handles single medicine creation
- ✅ **Database Schema**: `user_meds` table includes `receipt_image_path` and `receipt_image_bucket_id` columns
- ✅ **UI Components**: HeaderDropDown, ImageActionButtons, and form components are ready

### Current Limitations
- OCR function designed for lab results, not pharmacy receipts
- No bulk medicine creation workflow
- No receipt-specific OCR prompt engineering
- No multi-medicine review/edit interface

## Implementation Plan

### Phase 1: Edge Function for Receipt OCR ✅ COMPLETED

#### 1.1 Create Medicine Receipt OCR Edge Function ✅ COMPLETED

**File: `supabase/functions/medicine-receipt-ocr/index.ts`**

Main features:
- ✅ Accept base64 image data from client
- ✅ Use OpenAI Vision API (GPT-4 Vision) for text extraction
- ✅ Implement medicine-specific prompt engineering
- ✅ Return structured data with extracted medicines
- ✅ Handle authentication and error cases

**File: `supabase/functions/medicine-receipt-ocr/deno.json`** ✅ COMPLETED
- ✅ Deno configuration for edge function dependencies

#### 1.2 OCR Prompt Engineering ✅ COMPLETED

```
You are an expert OCR system specialized in extracting medicine information from pharmacy bills/receipts.

Analyze the provided pharmacy receipt/bill image and extract medicine information in JSON format:

{
  "rawText": "Complete text content from the image",
  "extractedMedicines": [
    {
      "name": "Medicine name",
      "price": 25.50,
      "description": "Brief description if available"
    }
  ],
  "confidence": 0.95
}

Rules:
1. Extract ONLY medicines/drugs, ignore other items
2. Clean medicine names (remove dosage info, keep only the medicine name)
3. Extract prices as numbers (remove currency symbols)
4. Combine similar medicines if they appear multiple times
5. If information is unclear, omit the field
6. Focus on medicine-related items only
7. Return ONLY the JSON object, no additional text
```

### Phase 2: Simple OCR Hook ✅ COMPLETED

#### 2.1 Create Medicine Receipt OCR Hook ✅ COMPLETED

**File: `hooks/useMedicineReceiptOcr.ts`**

✅ Simple hook that follows existing patterns:
- ✅ Uses Zustand OCR store for state management
- ✅ Converts image URI to base64 using expo-file-system
- ✅ Calls medicine-receipt-ocr edge function
- ✅ Handles authentication with Supabase session
- ✅ Proper error handling and loading states
- ✅ Updates OCR store with extracted medicines

### Phase 3: Multi-Medicine Review Interface ✅ COMPLETED

#### 3.1 Create Medicine Review Form Component ✅ COMPLETED

**File: `components/medications/MedicineReviewForm.tsx`**

✅ Simple form component that:
- ✅ Takes `extractedMedicines` from OCR store as initial data
- ✅ Uses react-hook-form for form state (following existing MedicineForm pattern)
- ✅ Displays receipt image from OCR store
- ✅ Uses bulk creation with `useBulkCreateFromReceipt` hook
- ✅ Shows progress indicator during bulk save
- ✅ Uses existing patient selection pattern with RouterSelectInput
- ✅ Follows existing form patterns and validation

**Key Principle: Reuse existing patterns, don't reinvent**
- ✅ Follows the same pattern as `MedicineForm.tsx`
- ✅ Uses efficient bulk DB operations
- ✅ Keeps form state local with react-hook-form
- ✅ Uses Zustand only for OCR data and image URI

### Phase 4: Simplified OCR State Management ✅ COMPLETED

#### 4.1 Create Simple OCR Store ✅ COMPLETED

**File: `store/ocrStore.ts`**

✅ Simple Zustand store with:
- ✅ Image URI handling for captured receipt images
- ✅ OCR processing state (loading, error, extracted medicines)
- ✅ Clean state management functions
- ✅ Reset function for cleanup
- ✅ Follows existing store patterns in codebase

#### 4.2 Enhanced Medicine Creation Hook ✅ COMPLETED

**File: `hooks/entities/useMedicines.ts`**

✅ Added `useBulkCreateFromReceipt` method:
- ✅ Efficient bulk insert using Supabase batch operations
- ✅ Proper receipt image storage in `receipt_image_path` column
- ✅ Shared receipt image across all medicines from same receipt
- ✅ Maintains existing patterns and error handling

### Phase 5: Modal Screen and UI Integration ✅ COMPLETED

#### 5.1 Create Medicine OCR Modal Screen ✅ COMPLETED

**File: `app/(protected)/modals/modal-medicine-ocr.tsx`**

✅ Complete modal screen with multiple states:
- ✅ **Processing state**: Shows captured image + loading indicator with OCR progress
- ✅ **Error state**: Shows error message with retry option
- ✅ **Review state**: Shows MedicineReviewForm with extracted data
- ✅ **No medicines found state**: Handles cases where OCR finds no medicines
- ✅ Proper state management using OCR store
- ✅ Navigation and cleanup handling

#### 5.2 HeaderDropDown Integration ✅ COMPLETED

**File: `app/(protected)/(tabs)/_layout.tsx`**

✅ Enhanced HeaderDropDown integration:
- ✅ "From Bill" option triggers camera/gallery selection alert
- ✅ Direct image capture flow using existing useImageCapture hook
- ✅ Automatic navigation to OCR modal after image capture
- ✅ Proper error handling for image capture failures

#### 5.3 Enhanced Medicine Display ✅ COMPLETED

**File: `components/medications/MedicineCard.tsx`**
- ✅ Receipt icon indicator in medicine cards when `receipt_image_path` exists
- ✅ "From receipt" text with receipt icon

**File: `components/medications/MedicineForm.tsx`**
- ✅ Receipt image section displays when `receipt_image_path` exists
- ✅ Dedicated receipt image display with proper styling
- ✅ Loading states for receipt image
- ✅ Updated types to include receipt fields

**File: `types/database.types.ts`** ✅ COMPLETED
- ✅ Generated updated TypeScript types from Supabase database
- ✅ Includes `receipt_image_path` and `receipt_image_bucket_id` fields

## Current Status ✅ IMPLEMENTATION COMPLETE 

### ✅ All Components Implemented:
1. **Edge Function**: `supabase/functions/medicine-receipt-ocr/` - Medicine-specific OCR with OpenAI Vision
2. **OCR Hook**: `hooks/useMedicineReceiptOcr.ts` - Image processing and API calls
3. **OCR Store**: `store/ocrStore.ts` - Simple state management
4. **Review Form**: `components/medications/MedicineReviewForm.tsx` - Multi-medicine editing interface
5. **Bulk Creation**: Enhanced `hooks/entities/useMedicines.ts` with efficient bulk operations
6. **Modal Screen**: `app/(protected)/modals/modal-medicine-ocr.tsx` - Complete OCR workflow
7. **UI Integration**: Enhanced HeaderDropDown with "From Bill" functionality
8. **Enhanced Display**: Receipt indicators in cards and image sections in forms
9. **Database Types**: Updated TypeScript types with receipt fields

### 🎯 **FEATURE READY FOR TESTING**

The complete medicine OCR workflow is now implemented:

1. **User Flow**: ✅ Complete end-to-end flow from "+" button to saved medicines
2. **OCR Processing**: ✅ OpenAI Vision API integration for pharmacy receipt analysis
3. **Review Interface**: ✅ Multi-medicine editing with patient assignment
4. **Bulk Operations**: ✅ Efficient database operations with shared receipt images
5. **Visual Indicators**: ✅ Receipt icons and image displays throughout the app
6. **Error Handling**: ✅ Comprehensive error states and retry mechanisms

### 🚀 **Ready for User Testing**
All planned features have been implemented following the simplified, efficient approach. The system reuses existing patterns and provides a seamless user experience for scanning pharmacy receipts and adding multiple medicines.
- **saving**: Show loading indicator while saving medicines

The screen retrieves image URI from store and automatically starts OCR processing.

### Phase 5: Integration with Existing UI

#### 5.1 Update TabsLayout HeaderDropDown

Update `app/(protected)/(tabs)/_layout.tsx`:
- Add 'bill' option handler to directly trigger camera/gallery selection
- Use existing ImagePicker functions (similar to existing camera/gallery handlers)
- Store captured image URI in Zustand store
- Navigate to `/modals/modal-medicine-ocr` (no parameters needed)
- Show 'From Bill' option only on medicine tabs

```typescript
const { setCapturedImageUri } = useOcrStore();

const handleBillScan = async () => {
  // Show camera/gallery selection alert
  const result = await showImageSourceAlert();
  if (result && !result.cancelled) {
    setCapturedImageUri(result.uri);
    router.navigate('/modals/modal-medicine-ocr');
  }
};
```

#### 5.2 Update Protected Layout

Add new modal screen to `app/(protected)/_layout.tsx`:
```typescript
<Stack.Screen 
  name="modals/modal-medicine-ocr" 
  options={{
    headerShown: false,
    presentation: 'modal',
  }}
/>
```

### Phase 6: Enhanced Medicine Display

#### 6.1 Update Medicine Card Component

Add receipt indicator to `components/medications/MedicineCard.tsx`:
- Show small receipt icon if `receipt_image_path` exists (not `image_path`)
- Add receipt icon next to existing info (like dosage/duration)
- Do NOT display receipt image (card shows main medicine image from `image_path`)
- Receipt icon indicates "this medicine was created from a receipt scan"

#### 6.2 Update Medicine Form Component

Add new receipt image section to `components/medications/MedicineForm.tsx`:
- Add new section after the main medicine image section
- Show receipt image if `receipt_image_path` exists (check this field, not `image_path`)
- Use `useSupabaseStorageUrl` hook with `receipt_image_bucket_id` and `receipt_image_path`
- Read-only display with proper styling (no editing of receipt image)
- Include header like "Receipt Image" with receipt icon
- Keep existing main image functionality completely separate

### Phase 7: Simple Conditional UI

#### 7.1 Update HeaderDropDown Items Logic

Simple conditional logic without additional store:
- Check current route/pathname to determine if we're on medicine tabs
- Show 'From Bill' only on medicine tabs (`(meds-tabs)`, `all-meds`, `meds`)
- Use existing patterns from the codebase for conditional UI

## Database Schema

The `user_meds` table already includes the necessary columns:
- `receipt_image_path` (text, nullable) - Path to the receipt/bill image
- `receipt_image_bucket_id` (text, nullable) - Storage bucket for receipt image
- `image_path` (text, nullable) - Path to the main medicine photo (separate)
- `image_bucket_id` (text, nullable) - Storage bucket for main medicine photo

**Key Separation**:
- **Receipt image**: `receipt_image_path` + `receipt_image_bucket_id` (shared by medicines from same receipt)
- **Medicine photo**: `image_path` + `image_bucket_id` (individual medicine photos)

No additional schema changes required.

## Technical Implementation Details

### Edge Function Structure
```typescript
interface MedicineExtraction {
  name: string;
  price?: number;
  description?: string;
}

interface ReceiptOcrResponse {
  success: boolean;
  data?: {
    rawText: string;
    extractedMedicines: MedicineExtraction[];
    pharmacyInfo?: {
      name?: string;
      address?: string;
      phone?: string;
      date?: string;
    };
    confidence: number;
    processingTime: number;
  };
  error?: string;
}
```

### Client Hook Interface
```typescript
export interface UseMedicineReceiptOcrResult {
  performOcr: (uri: string) => Promise<ReceiptOcrResults | null>;
  isProcessing: boolean;
  error: Error | null;
}
```

### Medicine Review Form Data
```typescript
const medicineReviewSchema = z.object({
  medicines: z.array(z.object({
    name: z.string().min(1, 'Medicine name is required'),
    price: z.coerce.number().nonnegative().optional().nullable(),
    description: z.string().optional().nullable(),
    opened_on_date: z.string().optional().nullable(),
    patient_id: z.string().uuid().optional().nullable(),
    notes: z.string().optional().nullable(),
  }))
});
```

## Error Handling Strategy

### OCR Processing Errors
- Network failures: Retry logic with exponential backoff
- Invalid images: Clear error messages to user
- No medicines found: Option to retry or manual entry
- Low confidence: Warning to user with manual review option

### User Experience
- Loading indicators during OCR processing
- Progress messages ("Extracting medicines from receipt...")
- Clear error messages with recovery options
- Confirmation dialogs for cancellation

## Performance Considerations

### Image Optimization
- Compress images before sending to OCR
- Optimize image size for OpenAI API limits
- Cache OCR results to avoid re-processing

### Network Efficiency
- Implement retry logic for failed requests
- Show detailed progress indicators
- Handle offline scenarios gracefully

## Security & Privacy

### Data Protection
- Secure transmission of receipt images
- Temporary storage only (no permanent receipt storage in cloud)
- Proper user authentication for all operations

### API Security
- Rate limiting on OCR endpoint
- Input validation for image data
- Secure handling of OpenAI API keys

## Testing Strategy

### Unit Tests
- OCR hook functionality
- Form validation logic
- Edge function with mock data

### Integration Tests
- Complete OCR flow from capture to save
- Error handling scenarios
- UI state transitions

### User Testing
- Various receipt formats and quality
- Medicine extraction accuracy
- User interface usability

## Deployment Checklist

### Pre-deployment
- [ ] Create and test medicine-receipt-ocr edge function
- [ ] Implement useMedicineReceiptOcr hook
- [ ] Create MedicineReviewForm component
- [ ] Create modal-medicine-ocr screen
- [ ] Update existing UI components
- [ ] Test complete user flow

### Deployment
- [ ] Deploy edge function to Supabase
- [ ] Set OpenAI API key in Supabase secrets
- [ ] Deploy app with new functionality
- [ ] Monitor OCR performance

### Post-deployment
- [ ] Monitor OCR accuracy and user feedback
- [ ] Track API usage and costs
- [ ] Collect user feedback for improvements

## Future Enhancements

1. **Advanced OCR Features**
   - Multi-language support
   - Prescription format recognition
   - Batch receipt processing

2. **UI/UX Improvements**
   - OCR preview with highlighted text
   - Manual correction interface
   - Receipt management library

3. **Analytics & Insights**
   - OCR accuracy metrics
   - Medicine spending analysis
   - User behavior analytics

## Success Metrics

### Technical Metrics
- OCR accuracy rate > 90%
- Processing time < 15 seconds
- Error rate < 5%

### User Experience Metrics
- User adoption of OCR feature
- Time saved vs manual entry
- User satisfaction scores

## Database Update Strategy

### Bulk Medicine Creation with Receipt Image

Create a new efficient bulk creation method in `useMedicines.ts`:

```typescript
// Add to useMedicines.ts
const bulkCreateFromReceipt = () => {
  return useMutation<Medicine[], Error, BulkReceiptMedicineData>({
    mutationFn: async ({ medicines, receiptImageUri }) => {
      return executeQuery(async (supabase, userId) => {
        // Upload receipt image once (shared by all medicines)
        let receiptImagePath: string | null = null;
        if (receiptImageUri) {
          const fileExt = receiptImageUri.split('.').pop()?.toLowerCase() || 'jpg';
          const receiptPath = `${userId}/receipts/${Date.now()}.${fileExt}`;
          receiptImagePath = await uploadFile({ 
            fileUri: receiptImageUri, 
            bucketId: 'user-meds', 
            path: receiptPath, 
            fileType: 'image/jpeg' 
          });
        }

        // Prepare bulk insert data
        const medicineInserts: TablesInsert<'user_meds'>[] = medicines.map(med => ({
          name: med.name,
          price: med.price || null,
          description: med.description || null,
          opened_on_date: new Date().toISOString(),
          patient_id: med.patient_id || null,
          notes: med.notes || null,
          is_custom: true,
          user_id: userId,
          receipt_image_path: receiptImagePath, // Receipt image in dedicated column
          receipt_image_bucket_id: receiptImagePath ? 'user-meds' : null,
          // Main image fields remain null (separate from receipt)
          image_path: null,
          image_bucket_id: null,
        }));

        // Bulk insert all medicines
        const { data: newMedicines, error } = await supabase
          .from('user_meds')
          .insert(medicineInserts)
          .select();

        if (error) {
          // Clean up receipt image if bulk insert fails
          if (receiptImagePath) {
            await supabase.storage.from('user-meds').remove([receiptImagePath]);
          }
          throw new Error(`Error creating medicines: ${error.message}`);
        }

        return newMedicines as Medicine[];
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['medicines'] });
    }
  });
};
```

### Key Points:
1. **Bulk insert**: Efficient single database operation for all medicines
2. **Receipt image**: Uses dedicated `receipt_image_path` & `receipt_image_bucket_id` columns
3. **Shared receipt**: One receipt image shared by all medicines from same scan
4. **Main image separate**: Keeps existing `image_path` for individual medicine photos
5. **Proper cleanup**: Receipt image deleted if bulk insert fails
6. **Leverages Supabase**: Uses bulk operations efficiently

### Why This Approach is Not Over-Engineered:
- ✅ **Single Zustand store**: Only for OCR state (image + extracted data)
- ✅ **Reuse existing patterns**: Follow `MedicineForm.tsx` and `useMedicines.ts`
- ✅ **Standard form handling**: react-hook-form for medicine review form
- ✅ **Existing DB operations**: No new mutation patterns
- ✅ **Simple conditional UI**: Basic route checking, no complex state management

**Total new code**: ~3 small files (OCR hook, review form, edge function) + minor UI updates

---

This implementation plan provides a comprehensive roadmap for adding medicine OCR functionality while leveraging existing infrastructure and maintaining code quality standards. 