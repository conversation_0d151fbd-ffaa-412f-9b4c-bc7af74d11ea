# Plan: Implement "Check Results" Feature for Lab Results

This plan outlines the steps to add a "Check Results" button to lab result cards, which will open the lab's website in a custom WebView with options to copy patient ID and password.

## Phase 1: Enhance `LabResultCard.tsx`

1.  **Modify Component Logic & Props:**

    - Ensure the `result` prop (of type `LabResultWithPatient`) includes `website`, `patient_id` (the ID from the lab document), and `password`.
      - **Action**: Verify the `LabResultWithPatient` type and the data fetching logic in the `useLabResults` hook (specifically `useFetchLabResults`). If `password` or the correct `patient_id` (identifier from the lab document, not the patient table FK) are not being fetched, update the Supabase query in the hook and any related TypeScript types (e.g., in `hooks/entities/useLabResults.ts` and potentially `types/database.types.ts` if it originates there).
    - Conditionally render a new button (e.g., "Check Results") within the card if `result.website` is present and non-empty.

2.  **Implement Button Action:**
    - Import `useAppAlerts` from `@/hooks/useAppAlerts`.
    - Import `router` from `expo-router`.
    - When the "Check Results" button is pressed:
      - Call the `confirm` function from `useAppAlerts`:
        - `title`: "Open External Website"
        - `message`: "You are about to open the lab's website. Patient ID and password (if available on the lab result) will be passed to the next screen for your convenience. Do you want to proceed?"
        - `confirmText`: "Proceed"
        - `cancelText`: "Cancel"
      - On user confirmation:
        - Navigate to a new modal route, for example: `/modals/lab-result-webview`.
        - Pass the following as route parameters:
          - `websiteUrl: result.website`
          - `patientId: result.patient_id` (ensure this is the ID from the lab document)
          - `password: result.password`

## Phase 2: Create Lab Result WebView Screen

1.  **Create New File:**

    - Path: `app/(protected)/modals/lab-result-webview.tsx` (This structure makes it a modal screen within the protected routes).

2.  **Component Implementation (`LabResultWebViewScreen`):**
    - **Retrieve Parameters**: Use `useLocalSearchParams` from `expo-router` to get `websiteUrl`, `patientId`, and `password`.
    - **Dependencies Installation Check**:
      - Verify `react-native-webview` is installed. If not, run: `npx expo install react-native-webview`
      - Verify `expo-clipboard` is installed. If not, run: `npx expo install expo-clipboard`
    - **UI Structure**:
      - Use `Stack.Screen` options from `expo-router` to set an appropriate screen title (e.g., "Lab Website" or derive from `websiteUrl`).
      - A main `View` that includes:
        - A section (e.g., a small header or footer within the WebView screen, but outside the WebView itself) containing two buttons:
          - "Copy Patient ID" (visible if `patientId` is present).
          - "Copy Password" (visible if `password` is present).
        - The `WebView` component from `react-native-webview`, configured with `source={{ uri: websiteUrl }}`.
    - **Button Functionality**:
      - Import `setStringAsync` from `expo-clipboard`.
      - Import `useAppAlerts` for feedback (optional, or use a simple toast).
      - **Copy Patient ID Button**: On press, call `setStringAsync(patientId)`. Provide user feedback (e.g., `showSuccess("Patient ID copied!")`).
      - **Copy Password Button**: On press, call `setStringAsync(password)`. Provide user feedback (e.g., `showSuccess("Password copied!")`).
    - **WebView Enhancements**:
      - Implement a loading indicator for the WebView (e.g., using the `onLoadStart` and `onLoadEnd` props of the `WebView`).
      - Consider basic error handling for the WebView (e.g., using the `onError` prop to display a message if the URL fails to load).
    - **Styling**: Ensure the screen, buttons, and any text are styled clearly and are consistent with the app's existing UI/UX (including dark mode).

## Phase 3: State Management

- **Initial Approach**: Navigation parameters (via `expo-router`) are the primary method for passing `websiteUrl`, `patientId`, and `password` to the WebView screen. This is generally sufficient for this focused use case.
- **Zustand**: Not identified as a requirement for this specific feature. If future enhancements necessitate broader state sharing of this information, Zustand could be considered then.

## Phase 4: Testing and Refinements

1.  **Data Integrity**:
    - Confirm that `result.website`, `result.patient_id` (the lab document ID), and `result.password` are correctly populated in `LabResultCard.tsx`.
    - Verify these values are accurately passed to and received by the `LabResultWebViewScreen`.
2.  **Functional Testing**:
    - Test the "Check Results" button appearance (only when `result.website` exists).
    - Test the alert dialog (confirmation and cancellation).
    - Test navigation to the WebView screen upon confirmation.
    - Test the "Copy Patient ID" and "Copy Password" buttons:
      - Ensure they copy the correct values.
      - Verify user feedback (e.g., toast/alert).
      - Ensure buttons are only visible if the corresponding data is present.
    - Test the WebView:
      - Ensure the correct website loads.
      - Check behavior with different types of websites (if possible).
      - Test loading indicator and error handling.
3.  **User Experience (UX)**:
    - Review the clarity of alert messages and button labels.
    - Assess the ease of use of the copy functions.
    - Ensure the WebView screen is intuitive and provides necessary controls (e.g., Expo Router's default modal header for dismissal).
4.  **Platform Testing**: Test thoroughly on both iOS and Android.

## Key Considerations & Best Practices:

- **Security**: While the password is being passed for user convenience, ensure it's not logged unnecessarily. It's being passed from one client-side component to another.
- **Patient ID**: Double-check that the `patient_id` being used is the one explicitly stated on the lab result document, not a database foreign key to a `patients` table, as per the existing form logic (`LabResultForm.tsx`).
- **Error Handling**: Robust error handling for network requests (WebView loading) and clipboard operations.
- **Accessibility**: Ensure new UI elements (buttons, etc.) are accessible.
- **Code Conventions**: Follow existing project code style and the guidelines in `.cursor/rules/expo-best-practices.mdc`.
