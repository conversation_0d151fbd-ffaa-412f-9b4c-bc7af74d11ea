# Plan: Save WebView Page as PDF to Existing Lab Result

**Goal**: Allow users to capture the current page in the `LabResultWebViewScreen` as a PDF and attach it to the _currently viewed/edited_ lab result, replacing any existing attachment.

**Exclusions**: This plan does not cover saving a PDF for a lab result that has not yet been created.

---

## Phase 1: Setup and UI Modifications

1.  **Install Dependency**:

    - Add the `expo-print` package to the project. This package will be used to convert HTML content to a PDF.
    - **Action**: Run the command `npx expo install expo-print`.

2.  **Modify `LabResultWebViewScreen.tsx` - UI**:

    - **Add "Save Page as PDF" Button**:
      - In the bottom navigation controls bar (alongside "Back", "Forward", "Refresh"), add a new `Pressable` component for "Save Page as PDF".
      - Use an appropriate `Ionicons` (e.g., `document-outline` or `download-outline`).
      - This button will be responsible for initiating the PDF capture process.
    - **Ensure `labResultId` is Available**:
      - Verify that `labResultId` is being passed as a route parameter to `LabResultWebViewScreen` and is retrievable using `useLocalSearchParams`.
      - If `labResultId` is not already being passed when navigating from `LabResultCard.tsx` (via the "Check Results" button), update the `router.push` call in `LabResultCard.tsx` to include `labResultId: result.id` in the `params`.

3.  **Modify `LabResultWebViewScreen.tsx` - State**:
    - Add a new state variable, for example, `isCapturingPdf` (boolean), to manage the loading state during the PDF capture and saving process. Initialize it to `false`.
    - The "Save Page as PDF" button should be disabled if `isCapturingPdf` is `true`.

---

## Phase 2: PDF Capture and Navigation Back

1.  **Implement PDF Capture Logic in `LabResultWebViewScreen.tsx`**:

    - **Create `handleSaveAsPdf` Function**: This asynchronous function will orchestrate the capture.
      - Set `isCapturingPdf(true)`.
      - **Get HTML from WebView**:
        - Use `webViewRef.current?.injectJavaScript('document.documentElement.outerHTML');` to request the HTML.
        - The `WebView`'s `onMessage` prop will be used to receive the HTML string back from the injected JavaScript. You'll need to ensure your `onMessage` handler can distinguish this message (e.g., by checking a prefix or structure of the message data) and then proceeds with PDF generation.
      - **Generate PDF**: Once the HTML string is received:
        - Call `Print.printToFileAsync({ html: receivedHtmlString })`. This returns a promise resolving to an object like `{ uri: string, numberOfPages: number, base64: string | undefined }`. We primarily need the `uri`.
      - Handle potential errors during HTML retrieval or PDF generation (e.g., using a try-catch block, showing an alert via `useAppAlerts`).
      - Set `isCapturingPdf(false)` in a `finally` block.
    - **Link Button to Function**: The "Save Page as PDF" button's `onPress` handler will call `handleSaveAsPdf`.

2.  **Navigate Back with PDF URI**:
    - Inside `handleSaveAsPdf`, if PDF generation is successful and `labResultId` (from `useLocalSearchParams`) is present:
      - Use `router.push()` (or `router.replace()` if you prefer the WebView not to be in the back stack after this action) to navigate back to the lab result modal screen (e.g., `'/modals/modal-labresult'`).
      - Pass the following route parameters:
        - `id: labResultId` (matching the parameter name expected by `modal-labresult.tsx`)
        - `newAttachmentUri: generatedPdfUri.uri`
        - `attachmentAction: 'replaceWithPdf'` (a flag to indicate the specific action to be taken on the target screen)
      - Show a success message using `useAppAlerts` (e.g., "PDF captured. Returning to update lab result...").

---

## Phase 3: Handle PDF Attachment in Lab Result Modal

1.  **Modify `app/(protected)/modals/modal-labresult.tsx`**:
    - **Retrieve Parameters**: Use `useLocalSearchParams` to check for `id`, `newAttachmentUri`, and `attachmentAction === 'replaceWithPdf'`.
    - **Import Hooks**: Ensure `useLabResults` (for the update mutation) and `useAppAlerts` are imported.
    - **Implement `useEffect` for Update**:
      - Create a `useEffect` hook that depends on `id`, `newAttachmentUri`, and `attachmentAction`.
      - Inside the `useEffect`:
        - If `newAttachmentUri` and `attachmentAction === 'replaceWithPdf'` are present, and `id` matches the lab result being viewed/edited:
          - Instantiate the update mutation: `const updateLabResultMutation = useLabResults().useUpdateLabResult();` (Assuming the hook structure provides `useUpdateLabResult` or similar, adjust if it's just `useLabResults().update()`).
          - Fetch the current lab result data. The `updateLabResultMutation` requires the complete `LabResultFormData`. You can use `useFetchLabResultById(id)` to get the current data, then transform it into `LabResultFormData` before calling the mutation. _This step is crucial as the mutation expects full form data._
          - Call `updateLabResultMutation.mutateAsync({ id: id, data: /* fetched and transformed LabResultFormData */, fileUri: newAttachmentUri, deleteFile: true })`. Setting `deleteFile: true` ensures any previously existing file in `image_path` is removed from storage before the new one is uploaded.
          - Handle success: Show a success alert. Invalidate relevant queries (`['labResults']`, `['labResult', id]`).
          - Handle error: Show an error alert.
          - **Clear Parameters**: To prevent the effect from re-running with the same parameters if the screen re-renders, either use `router.replace` to the same route but without these specific `newAttachmentUri` and `attachmentAction` params, or manage a local state flag.
    - **Loading State**: Consider showing a loading indicator on the `modal-labresult` screen while this update is in progress.

---

## Phase 4: Refinements and Testing

1.  **Loading Indicators & User Feedback**:

    - Ensure clear loading states in `LabResultWebViewScreen` during PDF capture.
    - Ensure clear loading states in `modal-labresult.tsx` during the attachment update.
    - Provide informative success and error messages using `useAppAlerts`.

2.  **Error Handling**:

    - Test scenarios where HTML retrieval from WebView might fail.
    - Test scenarios where `expo-print` fails to generate a PDF.
    - Test failures during the `updateLabResultMutation`.

3.  **File Replacement and Display**:

    - Verify that the new PDF is correctly uploaded to Supabase Storage.
    - Verify that the `image_path` in the `labresults` table is updated with the path to the new PDF.
    - Verify that any old attachment is deleted from Supabase Storage.
    - **Crucially**: Ensure `LabResultForm.tsx` (when it re-renders within `modal-labresult.tsx` after the update) correctly displays an indication of the new PDF attachment.
      - The `useEffect` in `LabResultForm` that handles `initialAttachmentUrl` and `initialValues.image_path` must be able to recognize a PDF (e.g., by its extension in `image_path`) and set `displayFileType` to `'document'`. This will ensure `DocumentPickerComponent` is shown instead of `ImageCapture`.
      - `DocumentPickerComponent` should be able to display a placeholder or name for the PDF.

4.  **PDF Rendering Quality**:

    - Test with various lab result web pages to check the quality and completeness of the generated PDFs. Note that complex CSS or JavaScript-heavy pages might have limitations.

5.  **Platform Testing**: Test the entire flow thoroughly on both iOS and Android.
