# Detailed Feature Implementation Plan: Recepturko Core Modules (v4)

This plan details the implementation steps for core features, leveraging the existing project structure, the specific schema defined in `types/database.types.ts`, <PERSON> auth, `react-query`, NativeWind, and `FlashList`. It includes explicit handling for relationships, enums, images, files, OCR, and specific field display/editing. **Create/Edit operations will use modal screens located in `app/(protected)/modals/` directory.**

**Core Technologies & Libraries:**

- **UI:** React Native, NativeWind (`global.css`, `tailwind.config.js`), Expo Components
- **Navigation:** `expo-router` (structure in `app/`)
- **Data Fetching/State:** `react-query` (`useQuery`, `useMutation`)
- **Backend:** Supabase (accessed via `hooks/useSupabaseClient.ts`)
- **Authentication:** Clerk (via `useAuth`)
- **Types:** Generated Supabase types (`types/database.types.ts`)
- **Image/File Handling:**
  - Capture/Selection: `expo-image-picker` (Images), `expo-document-picker` (Other files)
  - Upload: Supabase Storage (`supabase.storage`)
  - Display: `expo-image`, `react-native-webview` (for non-image files potentially)
  - **OCR:** `react-native-text-recognition`
- **Lists:** `@shopify/flash-list`
- **Validation:** Zod (recommended for forms)
- **Components:** Reusable UI (`components/ui/`), Common (`components/common/`), Feature-specific (`components/[feature]/`)

**Assumptions:**

- `hooks/useSupabaseClient.ts` hook provides a typed Supabase client.
- `ClerkProvider`, `QueryClientProvider`, `SafeAreaProvider` are set up in `app/_layout.tsx`.
- Base UI (`components/ui/`) and Common (`components/common/`) components will be created as needed.
- `profiles` table links Clerk `userId` to application user data (`id` field in `profiles` matches Clerk `userId`).
- Supabase Storage buckets exist (names confirmed via CLI): `Prescription Images/`, `Medicine Images/`, `Doctor Images/`, `Profile Images/`, `labresults/`. **Note:** The plan will primarily use the `labresults/` bucket for lab result attachments (images and potentially other files).
- RLS policies are configured for Supabase tables and Storage buckets to ensure user-specific access.

---

## 1. Prescriptions (`app/(protected)/(tabs)/prescription.tsx`)

- **Goal:** List prescriptions linked via `prescriptions.user_id`, displaying related doctor/patient info and handling front/back images.
- **Data Fetching Hook (`hooks/useFetchPrescriptions.ts`):**
  - Type: `useQuery<PrescriptionWithDetails[], Error>`
    - `PrescriptionWithDetails`: `Tables<'prescriptions'> & { user_doctors: Pick<Tables<'user_doctors'>, 'id' | 'name' | 'specialty'> | null; patients: Pick<Tables<'patients'>, 'id' | 'name'> | null }`
  - `queryKey: ['prescriptions', userId]` (where `userId` is from `useAuth`)
  - `queryFn`: `supabase.from('prescriptions').select('*, user_doctors(id, name, specialty), patients(id, name)').eq('user_id', userId).order('prescription_date', { ascending: false })`
- **UI Component (`components/prescriptions/PrescriptionCard.tsx`):**
  - Props: `prescription: PrescriptionWithDetails`
  - Display: `prescription.name`, `prescription.prescription_date` (formatted). Display `prescription.user_doctors.name` / `prescription.patients.name` if available.
  - **Image Display:** Small `expo-image` components for `front_image_path` and `back_image_path` if they exist. Fetch signed URLs from `'Prescription Images/'` bucket using `supabase.storage.from('Prescription Images/').createSignedUrl(path, 60)`.
- **Screen (`app/(protected)/(tabs)/prescription.tsx`):**
  - Use `FlashList` with `data={query.data}`, `renderItem={({ item }) => <PrescriptionCard prescription={item} onPress={() => router.push({ pathname: '/modals/modal-prescription', params: { id: item.id } })} />}`.
  - Add loading/error states based on `query.isLoading/isError`.
  - Add FAB triggering `router.push('/modals/modal-prescription')`.
- **CRUD & Image Handling:**
  - **Create/Edit Modal Screen (`app/(protected)/modals/modal-prescription.tsx` - **Create this file**):**
    - Param `id?`. Mode `isCreateMode = id === undefined;`.
    - Fetch data if `!isCreateMode`.
    - Form (`components/prescriptions/PrescriptionForm.tsx` - \*\*Create this component\*\*):
      - Reusable form component accepting `initialValues?`, `onSubmit`, `isLoading?`.
      - Controlled inputs for `name` (required), `prescription_date` (required, date picker). Optional: `notes`.
      - Selectors for `doctor_id` and `patient_id`.
      - Image Picker buttons for "Front Image" and "Back Image".
    - Display the `<PrescriptionForm>` passing fetched data (edit) or empty object (create).
    - Use `useCreatePrescription` or `useUpdatePrescription` based on `isCreateMode`.
    - Set screen title dynamically ("Create Prescription" / "Edit Prescription").
    - Show delete button only if `!isCreateMode`.
    - Dismiss modal on success/delete.
  - **Mutation Hooks (`hooks/useCreatePrescription.ts`, `hooks/useUpdatePrescription.ts`, `hooks/useDeletePrescription.ts` - **Create these hooks**):**
    - Implement logic as previously defined (validation, storage upload/delete, DB insert/update/delete).
    - Ensure update/delete hooks use the `id`.
    - Invalidate relevant queries (`['prescriptions', userId]`, potentially `['prescription', id]` if caching detail separately).
  - **Detail Screen:** _Likely combined into modal._
  - **Layout (`app/(protected)/modals/_layout.tsx` - Exists):** Add `<Stack.Screen name="modal-prescription" />` to this layout.

---

## 2. Medications (`app/(protected)/(tabs)/medicine.tsx`)

- **Goal:** List user-specific medications (`user_meds`), potentially linked to `allmeds` or custom, handling images for custom entries.
- **Data Fetching Hook (`hooks/useFetchUserMeds.ts`):**
  - Type: `useQuery<UserMedWithDetails[], Error>`
    - `UserMedWithDetails`: `Tables<'user_meds'> & { prescriptions: Pick<Tables<'prescriptions'>, 'name' | 'prescription_date'> | null; patients: Pick<Tables<'patients'>, 'name'> | null; allmeds: Pick<Tables<'allmeds'>, 'description'> | null }`
  - `queryKey: ['userMeds', userId]`
  - `queryFn`: `supabase.from('user_meds').select('*, prescriptions(name, prescription_date), patients(name), allmeds(description)').eq('user_id', userId).order('created_at', { ascending: false })`
- **UI Component (`components/medications/UserMedCard.tsx`):**
  - Props: `med: UserMedWithDetails`
  - Display: `med.name`. Dosage: Combine `med.dosage_amount`, `med.dosage_unit`. Frequency: Combine `med.frequency_amount`, `med.frequency_unit`. Duration: Combine `med.duration_amount`, `med.duration_unit`.
  - **Image Display:** If `med.image_path` exists (typically for `is_custom: true`), display thumbnail via `expo-image` using signed URL from bucket `'Medicine Images/'`.
- **Screen (`app/(protected)/(tabs)/medicine.tsx`):**
  - `FlashList` setup. Card `onPress={() => router.push({ pathname: '/modals/modal-medication', params: { id: item.id } })}`.
  - Button/FAB triggering `router.push('/modals/modal-medication')`.
- **CRUD & Image Handling (Custom Meds):**
  - **Create/Edit Modal Screen (`app/(protected)/modals/modal-medication.tsx` - **Create this file**):**
    - Param `id?`. Mode `isCreateMode = id === undefined;`.
    - Fetch if `!isCreateMode`.
    - Form (`components/medications/MedicationForm.tsx` - \*\*Create this component\*\*):
      - Reusable form. Inputs for `name`, dosage, frequency, duration, dates, etc.
      - Image Capture for custom meds.
      - Selectors for `patient_id`, `prescription_id`.
    - Display `<MedicationForm>`.
    - Use `useCreateUserMed` or `useUpdateUserMed`.
    - Dynamic title, conditional delete button.
    - Dismiss modal.
  - **Mutation Hooks (`hooks/useCreateUserMed.ts`, `hooks/useUpdateUserMed.ts`, `hooks/useDeleteUserMed.ts` - **Create these hooks**):**
    - Logic as defined previously (validation, storage for custom, DB ops).
    - Invalidate `['userMeds', userId]`.
  - **Detail Screen:** _Likely combined into modal._
  - **Layout (`app/(protected)/modals/_layout.tsx` - Exists):** Add `<Stack.Screen name="modal-medication" />`.

---

## 3. Lab Results (`app/(protected)/(tabs)/lab-results.tsx`) - UPDATED

- **Goal:** List lab results (`labresults` table) linked via `user_id`, handle attached image/file (including OCR for autofill), and display related patient info.
- **Data Fetching Hook (`hooks/useFetchLabResults.ts`):**
  - Type: `useQuery<LabResultWithDetails[], Error>`
    - `LabResultWithDetails`: `Tables<'labresults'> & { patients: Pick<Tables<'patients'>, 'id' | 'name'> | null }`
  - `queryKey: ['labResults', userId]`
  - `queryFn`: `supabase.from('labresults').select('*, patients(id, name)').eq('user_id', userId).order('result_date', { ascending: false })`
- **UI Component (`components/labresults/LabResultCard.tsx`):**
  - Props: `result: LabResultWithDetails`
  - Display: `result.lab_name`, `result.result_date` (formatted), `result.patients.name` if available.
  - Indicate attachment: Show an icon (e.g., paperclip) if `result.image_path` exists.
  - Card press should trigger navigation to `/(protected)/labresults/${result.id}` using `router.push`.
- **Screen (`app/(protected)/(tabs)/lab-results.tsx`):**
  - `FlashList` setup.
  - `LabResultCard` `onPress={() => router.push({ pathname: '/modals/modal-labresult', params: { id: item.id } })}`.
  - FAB triggering `router.push('/modals/modal-labresult')`.
- **CRUD & Image/File/OCR Handling:**
  - **Create/Edit Modal Screen (`app/(protected)/modals/modal-labresult.tsx` - **Moved & Refactored**):**
    - Handles create/edit logic.
    - Form (`components/labresults/LabResultForm.tsx` - Exists):
      - Already reusable.
      - Includes inputs, attachment buttons, OCR trigger (implicitly via image capture).
    - Display `<LabResultForm>`.
    - Use `useCreateLabResult` or `useUpdateLabResult`.
    - Dynamic title, conditional delete button.
    - Dismiss modal.
  - **Mutation Hooks (`hooks/useCreateLabResult.ts`, `hooks/useUpdateLabResult.ts`, `hooks/useDeleteLabResult.ts` - Exists or Create):**
    - Implement/Confirm logic (validation, storage, OCR, DB ops).
    - Invalidate `['labResults', userId]`.
  - **Detail Screen:** _Combined into modal._
  - **Layout (`app/(protected)/modals/_layout.tsx` - Exists):** Screen `modal-labresult` configured.
  - **Old Layout (`app/(protected)/labresults/_layout.tsx` - **Deleted\*\*).

---

## 4. Doctors (e.g., `app/(protected)/settings/doctors.tsx` or nested tab)

- **Goal:** Manage user's associated doctors (`user_doctors` table), optionally linking to global directory (`alldoctors`).
- **Data Fetching Hook (`hooks/useFetchUserDoctors.ts`):**
  - Type: `useQuery<UserDoctorWithDetails[], Error>`
    - `UserDoctorWithDetails`: `Tables<'user_doctors'> & { alldoctors: Pick<Tables<'alldoctors'>, 'fee' | 'workplace_address'> | null }`
  - `queryKey: ['userDoctors', userId]`
  - `queryFn`: `supabase.from('user_doctors').select('*, alldoctors(fee, workplace_address)').eq('user_id', userId).order('name')`
- **UI Component (`components/doctors/UserDoctorCard.tsx`):**
  - Props: `doctor: UserDoctorWithDetails`
  - Display: `doctor.name` (required), `doctor.specialty`, `doctor.workplace`, `doctor.phone_number`. Prefer `doctor.alldoctors.workplace_address` if available, else `doctor.workplace_address`.
- **Screen (e.g., `app/(protected)/(tabs)/(nested-tabs)/doctors.tsx`):**
  - `FlashList` setup.
  - Card `onPress={() => router.push({ pathname: '/modals/modal-doctor', params: { id: item.id } })}`.
  - Button/FAB triggering `router.push('/modals/modal-doctor')`.
- **CRUD:**
  - **Create/Edit Modal Screen (`app/(protected)/modals/modal-doctor.tsx` - **Create this file**):**
    - Param `id?`. Mode `isCreateMode = id === undefined;`.
    - Fetch if `!isCreateMode`.
    - Form (`components/doctors/DoctorForm.tsx` - \*\*Create this component\*\*):
      - Reusable form. Inputs for name, specialty, contact, etc.
      - Logic to link to `alldoctors` or mark as custom.
    - Display `<DoctorForm>`.
    - Use `useCreateUserDoctor` or `useUpdateUserDoctor`.
    - Dynamic title, conditional delete button.
    - Dismiss modal.
  - **Mutation Hooks (`hooks/useCreateUserDoctor.ts`, `hooks/useUpdateUserDoctor.ts`, `hooks/useDeleteUserDoctor.ts` - **Create these hooks**):**
    - Implement logic.
    - Invalidate `['userDoctors', userId]`.
  - **Detail Screen:** _Likely combined into modal._
  - **Layout (`app/(protected)/modals/_layout.tsx` - Exists):** Add `<Stack.Screen name="modal-doctor" />`.

---

## 5. Patients (User's Patients - `app/(protected)/(tabs)/patients.tsx`)

- **Goal:** Allow users to manage a list of patients (`patients` table) linked to their profile (`user_id`). This is distinct from a Doctor's view of _their_ patients.
- **Data Fetching Hook (`hooks/useFetchPatients.ts`):**
  - Type: `useQuery<Tables<'patients'>[], Error>`
  - `queryKey: ['patients', userId]`
  - `queryFn`: `supabase.from('patients').select('*').eq('user_id', userId).order('name')`
- **UI Component (`components/patients/PatientCard.tsx`):**
  - Props: `patient: Tables<'patients'>`
  - Display: `patient.name`, `patient.age`, `patient.bloodtype`.
- **Screen (`app/(protected)/(tabs)/patients.tsx` - **Create this file\*\* for User's own list):\*\*
  - `FlashList` setup.
  - Card `onPress={() => router.push({ pathname: '/modals/modal-patient', params: { id: item.id } })}`.
  - Button/FAB triggering `router.push('/modals/modal-patient')`.
- **CRUD:**
  - **Create/Edit Modal Screen (`app/(protected)/modals/modal-patient.tsx` - **Create this file**):**
    - Param `id?`. Mode `isCreateMode = id === undefined;`.
    - Fetch if `!isCreateMode`.
    - Form (`components/patients/PatientForm.tsx` - Exists):
      - Reusable form. Inputs for name, age, bloodtype.
    - Display `<PatientForm>`.
    - Use `useCreatePatient` or `useUpdatePatient`.
    - Dynamic title, conditional delete button.
    - Dismiss modal.
  - **Mutation Hooks (`hooks/useCreatePatient.ts`, `hooks/useUpdatePatient.ts`, `hooks/useDeletePatient.ts` - Exists or Create):**
    - Implement logic (including cascade/cleanup for delete).
    - Invalidate `['patients', userId]`.
  - **Detail Screen:** _Likely combined into modal._
  - **Layout (`app/(protected)/modals/_layout.tsx` - Exists):** Add `<Stack.Screen name="modal-patient" />`.

---

## 6. Doctor's View - Patient Management (Conceptual)

- **Goal:** For users identified as Doctors, provide a view to manage patients assigned to them.
- **Requires:**
  - **Role System:** Method to identify 'Doctor' users (e.g., custom claim in Clerk token, separate `roles` table linked to `profiles`).
  - **Schema:** A linking table like `doctor_patient_assignments (doctor_profile_id, patient_profile_id)` or similar mechanism to associate doctors with patient profiles (not just the `patients` table entries created by a user). **This is currently NOT defined in the schema.**
- **Implementation (If Schema Exists):**
  - Conditional rendering based on user role.
  - **Data Fetching:** Query the linking table, joining `patients.*` and `profiles(full_name, email, avatar_url)` based on the _doctor's_ ID.
  - **UI/Screens:** Dedicated screens/tabs for doctors showing assigned patients, allowing access to _their_ (the patient's) relevant data (prescriptions, lab results filtered by the patient's `user_id` or `patient_id`, respecting RLS).

---

## Revised Implementation Order (Focus on Modal Pattern)

1.  **Refactor Lab Results (Section 3 - DONE):**
    - Modal screen `app/(protected)/modals/modal-labresult.tsx` handles create/edit.
    - List screen `app/(protected)/(tabs)/lab-results.tsx` navigates to modal.
    - Layout `app/(protected)/modals/_layout.tsx` created & configured.
    - Old layout `app/(protected)/labresults/_layout.tsx` deleted.
2.  **Implement User's Patients (Section 5):**
    - Create hooks: `useFetchPatients`, `useCreatePatient`, etc.
    - Create Components: `PatientCard.tsx`, `PatientForm.tsx`.
    - Create Screens: List `app/(protected)/(tabs)/patients.tsx`, Modal `app/(protected)/modals/modal-patient.tsx`.
    - Add `modal-patient` screen to `app/(protected)/modals/_layout.tsx`.
    - Add Patients List screen to `app/(protected)/(tabs)/_layout.tsx`.
3.  **Implement Prescriptions (Section 1):**
    - Create hooks.
    - Create Components: `PrescriptionCard.tsx`, `PrescriptionForm.tsx`.
    - Create Screens: List `app/(protected)/(tabs)/prescription.tsx` (Modify?), Modal `app/(protected)/modals/modal-prescription.tsx`.
    - Add `modal-prescription` screen to `app/(protected)/modals/_layout.tsx`.
4.  **Implement Medications (Section 2):**
    - Create hooks.
    - Create Components: `UserMedCard.tsx`, `MedicationForm.tsx`.
    - Create Screens: List `app/(protected)/(tabs)/medicine.tsx` (Modify?), Modal `app/(protected)/modals/modal-medication.tsx`.
    - Add `modal-medication` screen to `app/(protected)/modals/_layout.tsx`.
5.  **Implement Doctors (Section 4):**
    - Create hooks.
    - Create Components: `UserDoctorCard.tsx`, `DoctorForm.tsx`.
    - Create Screens: List (e.g., `.../(nested-tabs)/doctors.tsx` - Modify?), Modal `app/(protected)/modals/modal-doctor.tsx`.
    - Add `modal-doctor` screen to `app/(protected)/modals/_layout.tsx`.
6.  **Base UI/Common Components:** Develop as needed.
7.  **Testing & Refinement.**
