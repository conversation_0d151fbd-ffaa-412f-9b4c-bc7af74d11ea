# Code Review & Next Steps

This document outlines findings from a code review, focusing on `app/(protected)/modals/modal-medicine.tsx` and `components/medications/MedicineForm.tsx`, and provides recommendations for improving the codebase, particularly around centralization of UI patterns and adherence to best practices.

## 1. Review of `modal-medicine.tsx` and `MedicineForm.tsx`

### 1.1. Header Management (Title & Delete Button)

- **Observation:** Currently, `modal-medicine.tsx` uses `Stack.Screen` options to manage the modal's title and the delete button in the header.
- **Suggestion:** Relocate header management (title and delete button logic) to the `MedicineForm.tsx` component.
  - Use the `useNavigation` hook from `expo-router` (or `@react-navigation/native`) within `MedicineForm.tsx`.
  - Employ `useLayoutEffect` to call `navigation.setOptions({ title: '...', headerRight: () => ... })`.
  - The `handleDelete` function and its associated `deleteMutation` (from `useMedicines`) would need to be either:
    - Passed as props from `modal-medicine.tsx` to `MedicineForm.tsx`.
    - Or, `MedicineForm.tsx` could use the `useDeleteMedicine` hook directly if the form is taking on more responsibility for the entire entity lifecycle. This might be cleaner if the form becomes the single source of truth for medicine-related actions in this context.
- **Benefit:** Co-locates UI presentation logic with the component responsible for the content, making `modal-medicine.tsx` a simpler wrapper.

### 1.2. Loading and Error States

- **Observation:** `modal-medicine.tsx` implements its own UI for loading initial data and displaying fetch errors. This pattern is likely repeated in other modal screens.
- **Suggestion:** Create a reusable, centralized component for displaying full-screen status.
  - **New Component:** `components/common/FullScreenStatusIndicator.tsx`
  - **Props:** `isLoading: boolean`, `error?: Error | null`, `loadingMessage?: string`, `errorMessage?: string`, `onRetry?: () => void`, `noData?: boolean`, `noDataMessage?: string`.
  - This component would handle rendering an `ActivityIndicator`, error messages (with an optional retry button), or a "no data" message.
- **Benefit:** Reduces boilerplate in modal screens, ensures consistent UX for loading/error states.

### 1.3. Alerts and User Feedback

- **Observation:** `Alert.alert` is used directly in `modal-medicine.tsx` for success messages, error messages, and confirmations (e.g., delete confirmation).
- **Suggestion:** Implement a centralized hook for showing alerts.

  - **New Hook:** `hooks/useAppAlerts.ts`
  - **Exports:**

    ```typescript
    interface ConfirmationOptions {
      title: string;
      message: string;
      onConfirm: () => void;
      confirmText?: string;
      cancelText?: string;
      style?: 'default' | 'destructive';
    }

    function useAppAlerts() {
      const showSuccess = (message: string, title: string = 'Success') => { ... };
      const showError = (message: string, title: string = 'Error') => { ... };
      const confirm = (options: ConfirmationOptions) => { ... };
      return { showSuccess, showError, confirm };
    }
    ```

  - This hook would internally use `Alert.alert` or could be later upgraded to a custom notification component system without changing the call sites.

- **Benefit:** Standardizes user feedback, simplifies modal logic, and makes it easier to change the alert mechanism globally.

## 2. General Best Practice Recommendations

### 2.1. Centralize Common UI Logic (Beyond Modals)

- The `FullScreenStatusIndicator` and `useAppAlerts` proposed above should be adopted across all relevant parts of the application, not just modals, for consistency.

### 2.2. Error Handling and Logging

- **Observation:** Errors from mutations are caught and displayed via alerts. `console.error` is used for logging.
- **Suggestion:**
  - Integrate a remote logging service like Sentry (as mentioned in `expo-best-practices.mdc`). The `useAppAlerts` hook could also log errors to this service.
  - Ensure all Supabase client calls and mutations have robust `.catch()` blocks or are handled by `react-query`'s error mechanisms.
  - Establish global error boundaries if not already present.

### 2.3. State Management

- **Observation:** `react-query` is used for data fetching and caching (`useFetchMedicine`, `createMutation`, etc.), which is excellent. React Hook Form is used for forms.
- **Suggestion:** Continue leveraging `react-query` for server state. For complex global UI state not tied to server data, ensure consistent use of Context/Reducer, Zustand, or Redux Toolkit as per project guidelines.

### 2.4. Component Granularity and Reusability

- **Observation:** `ControlledInput`, `ControlledSelect`, `DatePicker`, `SearchableSelect`, `ImageCapture` are good examples of reusable components.
- **Suggestion:** Continue identifying and abstracting common UI patterns into reusable components. For instance, the "section" pattern in `MedicineForm.tsx` (e.g., Dosage Section with two fields) might be a candidate if it's frequently repeated.

### 2.5. TypeScript Usage

- **Observation:** Good use of types, `Omit`, and schema validation (`zodResolver`).
- **Suggestion:** Periodically audit for `any` types and replace them with more specific types or interfaces. Ensure all function signatures and component props are strongly typed.

### 2.6. Performance

- **Memoization:** Review components for opportunities to use `React.memo`, `useMemo`, and `useCallback`, especially for components that re-render frequently or perform expensive computations.
- **Image Optimization:** Continue using `expo-image` and best practices for image handling (WebP, lazy loading). The `useSupabaseStorageUrl` hook is a good pattern for handling image URLs.

### 2.7. Adherence to Project Guidelines

- Continuously refer to `expo-best-practices.mdc` and `supabase-context.mdc` for naming conventions, file structure, security, and other established practices.

## 3. Proposed Next Steps

1.  **Implement `useAppAlerts` hook:** - DONE
    - Created `hooks/useAppAlerts.ts`.
    - Refactored `modal-medicine.tsx` to use this hook for success/error alerts.
    - `MedicineForm.tsx` now uses this hook for delete confirmation.
2.  **Create `FullScreenStatusIndicator` component:** - DONE
    - Created `components/common/FullScreenStatusIndicator.tsx`.
    - Refactored the loading and error states in `modal-medicine.tsx` (when `isEditing && (isLoadingData || isFetchError)`) to use this component.
    - Removed specific `Stack.Screen` title settings for loading/error states in `modal-medicine.tsx`.
3.  **Refactor Header Management & Delete Logic in `MedicineForm`:** - DONE
    - Modified `components/medications/MedicineForm.tsx` to:
      - Manage its own header (title, delete button) using `useNavigation().setOptions()` via `useLayoutEffect`.
      - Fully handle the delete medicine lifecycle: initialize `useDeleteMedicine`, trigger delete mutation, show confirmation (`useAppAlerts`), handle success/error alerts, and dismiss router.
    - Removed delete-related props from `MedicineForm` and passthrough from `modal-medicine.tsx`.
    - `modal-medicine.tsx` no longer manages delete mutation or `headerRight`, only `headerLeft` for the close button.
4.  **Apply Abstractions to Other Modals:** - DONE (for complex modals)
    - Refactored `modal-patient.tsx` / `PatientForm.tsx` to use `useAppAlerts`, `FullScreenStatusIndicator`, and form-managed header/delete.
    - Refactored `modal-doctor.tsx` / `DoctorForm.tsx` similarly.
    - Refactored `modal-prescription.tsx` / `PrescriptionForm.tsx` similarly (this one was complex with image handling and type resolution for initial values).
    - Refactored `modal-labresult.tsx` / `LabResultForm.tsx` similarly (also complex with file handling, OCR, and type resolution for initial values).
    - `modal-filter.tsx` re-exports `components/common/Filter.tsx`. Review `Filter.tsx` separately if it requires alert/status indicator refactoring.
5.  **Review and Integrate Sentry (or chosen logging service):** - TODO
    - If not already done, set up Sentry for error logging.
    - Integrate logging calls within `useAppAlerts` (for errors) and critical catch blocks.
6.  **Code Style and Readability Audit:**
    - Perform a pass over the modified files and related components to ensure consistency with Prettier formatting and established code style.

This phased approach should help in systematically improving the codebase. Please review these suggestions.
