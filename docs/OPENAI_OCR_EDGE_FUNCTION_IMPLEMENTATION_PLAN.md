# OpenAI OCR Edge Function Implementation Plan

## Overview

This document outlines the step-by-step plan to replace the current local OCR implementation (using `react-native-text-recognition`) with an OpenAI-powered edge function that provides superior text extraction and structured data parsing for lab result images.

## Current Implementation Analysis

### Current Local OCR Flow

1. **Image Capture**: User takes/selects image via `useImageCapture` hook
2. **Image Processing**: Image is resized and compressed using `expo-image-manipulator`
3. **Local OCR**: `react-native-text-recognition` processes image locally on device
4. **Text Extraction**: Custom regex patterns extract structured data (lab name, dates, patient info, etc.)
5. **Form Population**: Extracted data populates the lab result form fields

### Current Edge Functions

- **`analyze-lab-result`**: Analyzes PDF lab results using OpenAI for medical insights
- **`chat-lab-result`**: Provides AI chat functionality for lab result discussions

### Current Image Upload Flow

1. **Image Selection**: Via `expo-image-picker` (camera/library)
2. **Image Manipulation**: Resize/compress with `expo-image-manipulator`
3. **Storage Upload**: Upload to Supabase Storage using signed URLs via `useSupabaseStorageUpload`
4. **Database Storage**: Store file paths in database tables

## Implementation Plan

### Phase 1: Create OpenAI OCR Edge Function

#### Step 1.1: Create New Edge Function Structure

```bash
# Create new edge function directory
mkdir -p supabase/functions/image-ocr-openai
cd supabase/functions/image-ocr-openai
```

#### Step 1.2: Create Function Configuration

Create `supabase/functions/image-ocr-openai/deno.json`:

```json
{
  "imports": {
    "supabase": "https://esm.sh/@supabase/supabase-js@2",
    "cors": "https://deno.land/x/cors@v1.2.2/mod.ts"
  }
}
```

#### Step 1.3: Implement OpenAI Vision API Integration

Create `supabase/functions/image-ocr-openai/index.ts`:

**Key Features:**

- Accept image file upload or Supabase Storage path
- Use OpenAI Vision API (GPT-4 Vision) for text extraction
- Implement structured data extraction with specific prompts for lab results
- Return both raw text and structured data
- Handle authentication and user verification
- Implement error handling and rate limiting

**Function Interface:**

```typescript
// Request body options:
// Option 1: Direct image upload
{
  imageData: string; // base64 encoded image
  mimeType: string;
}

// Option 2: Supabase Storage reference
{
  bucketId: string;
  imagePath: string;
}

// Response format:
{
  success: boolean;
  data?: {
    rawText: string;
    extractedData: {
      labName?: string;
      resultDate?: string;
      patientName?: string;
      patientId?: string;
      website?: string;
      phoneNumber1?: string;
      phoneNumber2?: string;
      phoneNumber3?: string;
      // Additional fields as needed
    };
    confidence: number; // 0-1 confidence score
    processingTime: number; // milliseconds
  };
  error?: string;
}
```

#### Step 1.4: Implement Structured Prompt Engineering

Design specific prompts for lab result OCR:

```typescript
const LAB_RESULT_OCR_PROMPT = `
You are an expert OCR system specialized in extracting structured data from medical lab result images.

Analyze the provided image and extract the following information in JSON format:

{
  "rawText": "Complete text content from the image",
  "extractedData": {
    "labName": "Name of the laboratory or medical facility",
    "resultDate": "Date of the lab results (ISO format YYYY-MM-DD)",
    "patientName": "Patient's full name",
    "patientId": "Patient ID, MRN, or reference number",
    "website": "Laboratory website URL if present",
    "phoneNumber1": "Primary phone number",
    "phoneNumber2": "Secondary phone number if present",
    "phoneNumber3": "Third phone number if present"
  },
  "confidence": 0.95
}

Rules:
1. Extract text exactly as it appears
2. For dates, convert to ISO format (YYYY-MM-DD)
3. Clean phone numbers to standard format
4. If information is unclear or missing, omit the field
5. Provide confidence score (0-1) based on text clarity
6. Focus on header information, patient details, and contact info
`;
```

### Phase 2: Create Client-Side Integration Hook

#### Step 2.1: Create New OCR Hook

Create `hooks/useOpenAIImageOcr.ts`:

**Features:**

- Replace `useImageOcr` functionality
- Handle image upload to edge function
- Manage loading states and error handling
- Provide same interface as current hook for easy migration
- Add retry logic and timeout handling

```typescript
export interface OpenAIOcrResults {
  labName?: string;
  resultDate?: string;
  patientName?: string;
  patientId?: string;
  website?: string;
  phoneNumber1?: string;
  phoneNumber2?: string;
  phoneNumber3?: string;
}

export interface UseOpenAIImageOcrResult {
  performOcr: (
    uri: string
  ) => Promise<{ rawText: string; extractedData: OpenAIOcrResults } | null>;
  isProcessingOcr: boolean;
  ocrError: Error | null;
}
```

#### Step 2.2: Implement Edge Function Client

Key implementation details:

- Convert image URI to base64 or upload to temporary storage
- Call edge function with proper authentication
- Handle response parsing and error states
- Implement retry logic for network failures
- Add timeout handling (30-60 seconds)

### Phase 3: Update Image Capture Integration

#### Step 3.1: Modify useImageCapture Hook

Update `hooks/useImageCapture.ts`:

- Replace `useImageOcr` import with `useOpenAIImageOcr`
- Maintain same interface for backward compatibility
- Update loading messages to reflect cloud processing
- Add network connectivity checks

#### Step 3.2: Update ImageCapture Component

Update `components/common/ImageCapture.tsx`:

- Update loading messages: "Processing with AI..." instead of "Processing with OCR..."
- Add network status indicators
- Handle longer processing times gracefully

### Phase 4: Environment Configuration

#### Step 4.1: Add Environment Variables

Add to Supabase Edge Function environment:

```bash
# In Supabase dashboard or via CLI
OPENAI_API_KEY=your_openai_api_key_here
```

#### Step 4.2: Update Function Deployment

Update deployment scripts to include new function:

```bash
# Add to existing deployment script
supabase functions deploy image-ocr-openai --no-verify-jwt=false
```

### Phase 5: Direct Replacement

#### Step 5.1: Remove Local OCR Dependencies

```bash
# Remove from package.json immediately
npm uninstall react-native-text-recognition
```

#### Step 5.2: Replace OCR Implementation

- Delete `hooks/useImageOcr.ts` completely
- Update `hooks/useImageCapture.ts` to import `useOpenAIImageOcr`
- Remove all local OCR text extraction helper functions
- Update all components using OCR functionality

### Phase 6: Performance Optimization

#### Step 6.1: Image Optimization

- Optimize image size/quality for OpenAI Vision API
- Implement client-side image preprocessing
- Add image format conversion if needed

#### Step 6.2: Caching Strategy

- Cache OCR results for identical images
- Implement client-side caching
- Add server-side caching in edge function

#### Step 6.3: Error Handling Enhancement

- Add retry logic with exponential backoff
- Provide clear error messages to users
- Implement graceful degradation when OCR fails (manual form entry)

### Phase 7: Cleanup and Deprecation

#### Step 7.1: Final Cleanup

- Update documentation to reflect OpenAI OCR usage
- Remove any remaining references to local OCR
- Update build configuration if needed

#### Step 7.2: Testing and Deployment

- Test OCR functionality thoroughly
- Deploy edge function to production
- Verify all OCR workflows work correctly

## Technical Considerations

### Security

- **Authentication**: Verify user tokens in edge function
- **Rate Limiting**: Implement per-user rate limits
- **Data Privacy**: Ensure images are not stored permanently
- **API Key Security**: Secure OpenAI API key in edge function environment

### Performance

- **Image Size**: Optimize image size for API limits (max 20MB for Vision API)
- **Processing Time**: Expect 5-15 seconds for OCR processing
- **Network Dependency**: Requires internet connection
- **Cost Management**: Monitor OpenAI API usage and costs

### Error Handling

- **Network Failures**: Implement retry logic
- **API Limits**: Handle rate limiting gracefully
- **Invalid Images**: Provide clear error messages
- **Graceful Degradation**: Allow manual form entry when OCR fails

### User Experience

- **Loading States**: Clear progress indicators
- **Offline Handling**: Graceful degradation when offline
- **Error Recovery**: Easy retry mechanisms
- **Performance Feedback**: Show processing time estimates

## Implementation Timeline

### Week 1: Edge Function Development

- Create edge function structure
- Implement OpenAI Vision API integration
- Set up authentication and error handling

### Week 2: Client Integration

- Create `useOpenAIImageOcr` hook
- Remove local OCR dependencies
- Update `useImageCapture` hook

### Week 3: Component Updates

- Update all components using OCR
- Remove local OCR code completely
- Implement proper error handling

### Week 4: Testing & Deployment

- Comprehensive testing of OCR functionality
- Deploy edge function to production
- Final cleanup and documentation

## Success Metrics

### Accuracy Improvements

- **Field Extraction Accuracy**: Target >95% for key fields
- **Text Recognition Quality**: Compare with local OCR baseline
- **Structured Data Parsing**: Measure correct field population

### Performance Metrics

- **Processing Time**: Target <15 seconds average
- **Success Rate**: Target >98% successful processing
- **Error Rate**: Target <2% unrecoverable errors

### User Experience

- **User Satisfaction**: Survey feedback on OCR quality
- **Form Completion Rate**: Measure auto-population success
- **Error Recovery**: Time to successful retry

## Risk Mitigation

### Technical Risks

- **API Downtime**: Implement fallback mechanisms
- **Cost Overruns**: Set up usage monitoring and alerts
- **Performance Issues**: Implement timeout and retry logic

### Business Risks

- **User Adoption**: Gradual rollout with feedback collection
- **Data Privacy**: Ensure compliance with privacy regulations
- **Vendor Lock-in**: Design abstraction layer for future flexibility

## Cost Analysis

### OpenAI Vision API Costs

- **Current Pricing**: ~$0.01-0.02 per image (varies by size)
- **Expected Usage**: Estimate based on current OCR usage
- **Budget Planning**: Set monthly limits and monitoring

### Infrastructure Costs

- **Edge Function Execution**: Supabase edge function costs
- **Storage**: Temporary image storage costs
- **Bandwidth**: Image upload/download costs

### Cost Optimization

- **Image Compression**: Reduce API costs through optimization
- **Caching**: Reduce duplicate processing
- **Usage Monitoring**: Track and optimize high-cost operations

## Conclusion

This implementation plan provides a comprehensive approach to replacing local OCR with OpenAI-powered cloud OCR, offering improved accuracy and structured data extraction while maintaining a smooth user experience. The gradual rollout strategy minimizes risk while allowing for performance monitoring and optimization.
