# Plan: Attach Captured WebView PDF as an Additional Document

**Goal**: Allow users to save a PDF captured from the `LabResultWebViewScreen` as an _additional_ document to an existing lab result, without replacing the primary image/document.

---

## Phase 1: Database and Type Modifications

1.  **Database Schema Change (Supabase)**:

    - Add new columns to the `labresults` table to store the path and bucket for the captured PDF.
      - `captured_pdf_path` (TEXT, nullable)
      - `captured_pdf_bucket_id` (TEXT, nullable)
    - **Action**: Manually add these columns to your `labresults` table in the Supabase dashboard or via a SQL migration script.

2.  **Update Database Types**:
    - Regenerate or manually update `types/database.types.ts` to include `captured_pdf_path` and `captured_pdf_bucket_id` in the `labresults` table definition (Row, Insert, Update).
    - **Action**: Update the type definitions.

---

## Phase 2: Backend Hook (`useLabResults.ts`) Enhancements

1.  **Update `LabResult` and `LabResultWithPatient` Types**:

    - Ensure these types in `hooks/entities/useLabResults.ts` include the new `captured_pdf_path` and `captured_pdf_bucket_id` fields. This should happen automatically if they are derived from `Tables<'labresults'>` after `types/database.types.ts` is updated.

2.  **Modify Fetch Queries**:

    - Ensure `fetchAll` and `fetchById` (or `useFetchLabResult`) select the new `captured_pdf_path` and `captured_pdf_bucket_id` columns. If using `select('*')`, this should be automatic. Verify.

3.  **Enhance `updateLabResult` Mutation**:

    - The existing `updateLabResultMutation` (`useUpdateLabResult`) needs to be adapted.
      - **New Parameters**: The `LabResultUpdateMutationData` interface and the mutation function should accept:
        - `capturedPdfFileUri?: string` (URI of the new captured PDF to upload)
        - `deleteExistingCapturedPdf?: boolean` (flag to indicate if a previously stored captured PDF at `captured_pdf_path` should be deleted)
      - **Upload Logic**:
        - If `capturedPdfFileUri` is provided, upload this file to Supabase Storage (e.g., to the `labresults` bucket, path similar to `userId/captured_pdfs/timestamp.pdf`).
        - Store the new `captured_pdf_path` and `captured_pdf_bucket_id`.
      - **Deletion Logic**:
        - If `deleteExistingCapturedPdf` is true AND there was an old `captured_pdf_path`, delete that old file from storage before saving the new path (or if no new PDF is provided, clear the paths).
      - **Database Update**: The `supabase.from('labresults').update(...)` call must include the new `captured_pdf_path` and `captured_pdf_bucket_id` fields.
      - The existing `fileUri` and `deleteFile` parameters for the _primary_ attachment (`image_path`) should continue to function independently.

4.  **Enhance `createLabResult` Mutation (Optional)**:

    - If a new lab result could have a captured PDF immediately, this mutation would also need to accept `capturedPdfFileUri`.

5.  **Update `remove` (Delete) Mutation**:
    - When a lab result is deleted, ensure it also attempts to delete the file at `captured_pdf_path` from storage.

---

## Phase 3: WebView Screen (`LabResultWebViewScreen.tsx`) Updates

1.  **Modify Navigation Parameters on PDF Capture**:
    - In `handleWebViewMessage`, when navigating back to `/modals/modal-labresult`:
      - Change `attachmentAction` to `'attachCapturedPdf'`.
      - Continue passing `id: labResultId` and `newAttachmentUri: pdfUri`.

---

## Phase 4: Lab Result Modal (`modal-labresult.tsx`) Logic Update

1.  **Retrieve New Parameters**:

    - Update `useLocalSearchParams` to recognize `attachmentAction === 'attachCapturedPdf'`.

2.  **Adapt `useEffect` for PDF Attachment**:
    - The `useEffect` hook should now:
      - Check for `attachmentAction === 'attachCapturedPdf'`.
      - When calling `updateLabResultMutation.mutateAsync`:
        - Pass the `newAttachmentUri` as `capturedPdfFileUri`.
        - Set `deleteExistingCapturedPdf: true`.
        - Ensure primary attachment fields (`fileUri`, `deleteFile`) are not inadvertently affected unless intended by other logic.
        - The `data` payload for the mutation (formDataForUpdate) should be the existing lab result data.

---

## Phase 5: Form Component (`LabResultForm.tsx`) Enhancements

1.  **Update `LabResultWithImage` Type**:

    - Add `captured_pdf_path?: string | null` and `captured_pdf_bucket_id?: string | null`.

2.  **Display Captured PDF**:

    - Add a new section to the form to display the captured PDF if `initialValues.captured_pdf_path` exists.
    - This could use another `DocumentPickerComponent` or a simple display.
    - Consider a "Remove Captured PDF" button, triggering an update with `deleteExistingCapturedPdf: true`.

3.  **Hook for Captured PDF URL**:

    - Use `useSupabaseStorageUrl` for `captured_pdf_path` if direct view/download is needed.

4.  **Initial Values**:
    - Ensure `initialFormValues` in `modal-labresult.tsx` populates new PDF fields.

---

## Phase 6: Card Component (`LabResultCard.tsx`) Enhancements (Optional)

1.  **Indicate Multiple Attachments**:
    - Modify `LabResultCard.tsx` to visually indicate if a lab result has both a primary attachment and a captured PDF.

---

## Phase 7: Testing

1.  **Database & Types**: Verify new columns and types.
2.  **PDF Capture & Attach**: Test capture, upload, database update, and primary `image_path` preservation.
3.  **Replacing Captured PDF**: Test that a new captured PDF replaces the old one (file storage and DB fields).
4.  **Display in Form**: Verify `LabResultForm.tsx` displays both primary and captured PDF; test removal.
5.  **Deleting Lab Result**: Verify deletion of both attachments from storage.
6.  **Overall Flow & Error Handling**: Test thoroughly.
7.  **Platform Testing**: Test on iOS and Android.
