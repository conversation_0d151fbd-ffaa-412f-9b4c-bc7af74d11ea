# Proposed Image Handling Improvements

## Progress Tracking

- [x] 1. Adopt `expo-image` for Displaying Images
  - [x] Modify `components/ui/Card.tsx`
  - [x] Modify `components/common/ImageCapture.tsx`
- [x] 2. Display Thumbnails in `PrescriptionCard.tsx` and `LabResultCard.tsx`
  - [x] Update `PrescriptionCard.tsx`
  - [x] Update `LabResultCard.tsx`
- [x] 3. In-Form Image Management Controls (Preview, Replace, Delete, Full Preview)
  - [x] Use `expo-image` for Inline Previews in `ImageCapture.tsx` (Covered by Item 1)
  - [x] Enhance Visual Feedback for "Delete" (Basic implementation in `ImageCapture.tsx` for initial images)
  - [x] Implement "Tap-to-Zoom" / Full-Screen Preview Modal (Implemented in `ImageCapture.tsx`)
  - [ ] Clearer "Replace" vs. "Remove" Actions (Current `ImageCapture.tsx` UX deemed acceptable, form-level changes deferred)
  - [ ] Visual Indication for New vs. Existing Images (Minor UI polish, deferred)
  - [x] Ensure Backend Deletion on Save (Verified/Corrected for Prescriptions, Lab Results, Medications)
- [x] 4. Image Upload Optimizations (Considerations)
  - [x] Content-Type in `useSupabaseStorageUpload` (Improved by passing known type from entity hooks)
  - [ ] WebP for Uploads (Requires further testing and implementation if adopted)
- [x] 5. Error Handling for Image Loading
  - [x] Implemented `onError` in `components/ui/Card.tsx` to show fallback icon.
  - [x] Implemented `onError` in `components/common/ImageCapture.tsx` for preview image to show error message.

This document outlines proposed improvements for image uploading, fetching, and presentation within the Recepturko application, focusing on Lab Results, Prescriptions, and Medications. The goal is to align with `expo-best-practices.mdc` and enhance user experience and performance.

## Current State Summary

- **Uploads**: Robustly handled by `useSupabaseStorageUpload.ts` (via entity hooks) using signed URLs. This is generally good.
- **Image Picking & Pre-processing**: `components/common/ImageCapture.tsx` uses `expo-image-picker` and `expo-image-manipulator` (for resizing/compression to JPEG). This pre-processing is beneficial.
- **URL Fetching**: `useSupabaseStorageUrl.ts` fetches signed URLs for displaying images.
- **Image Display**:
  - Forms (preview in `ImageCapture.tsx`): Uses standard React Native `<Image>`.
  - `PrescriptionCard.tsx` & `LabResultCard.tsx`: Currently **do not display images**, only an icon.
  - `MedicineCard.tsx` (via `components/ui/Card.tsx`): Uses standard React Native `<Image>`.

## Areas for Improvement & Recommendations

### 1. Adopt `expo-image` for Displaying Images

The `expo-best-practices.mdc` recommends using `expo-image` for its advanced features like caching, placeholder support (e.g., blurhash), lazy loading, and better performance, including potential WebP support.

**Actions:**

- **Modify `components/ui/Card.tsx`**:
  - Replace the React Native `<Image>` component with `expo-image`'s `<Image>`.
  - Implement placeholder support (e.g., a blurhash generated and stored alongside the image path, or a simple loading spinner as part of `expo-image`'s capabilities).
  - Utilize `expo-image`'s caching policies.
  - Consider using the `transition` prop for smooth image loading.
- **Modify `components/common/ImageCapture.tsx`**:
  - Replace the React Native `<Image>` used for previewing the captured/selected image with `expo-image`'s `<Image>`. This will provide a consistent experience and leverage `expo-image` benefits (like caching for network URIs if applicable, or consistent placeholder behavior) even for local URI previews.

**Example (Conceptual for `components/ui/Card.tsx`):**

```tsx
// import { Image } from 'react-native'; // Before
import { Image } from "expo-image"; // After

// ... inside Card component
{
  imageUrl ? (
    <Image
      source={{ uri: imageUrl }}
      className="w-full h-full rounded"
      placeholder={{
        blurhash: /* placeholderBlurhash || */ "LKO2?U%2Tw=w]~RBVZRi};RPxuwH",
      }} // Example blurhash
      transition={500} // Optional transition
      contentFit="cover" // Equivalent to resizeMode="cover"
    />
  ) : (
    <Ionicons name="medkit-outline" size={24} color="#9CA3AF" />
  );
}
```

### 2. Display Thumbnails in `PrescriptionCard.tsx` and `LabResultCard.tsx`

Currently, these cards only show an icon if an image/attachment exists. Displaying a small thumbnail would significantly improve UX by providing a quick visual cue.

**Actions:**

- **Update `PrescriptionCard.tsx`**:
  - If `front_image_path` exists, use `useSupabaseStorageUrl` to fetch its URL.
  - Pass this URL to an `<Image>` component (which would be `expo-image` after recommendation #1 is implemented in `components/ui/Card.tsx` if the card structure is reused, or directly in `PrescriptionCard` if a different layout is needed).
  - Consider which image to prioritize if both front and back exist (e.g., front image).
- **Update `LabResultCard.tsx`**:
  - If `image_path` exists, use `useSupabaseStorageUrl` to fetch its URL.
  - Display it using an `expo-image` component.
- **Styling**: Ensure thumbnails are appropriately sized and styled within the card layout. The existing `components/ui/Card.tsx` already has a section for an image, which could be leveraged or adapted.

**Example (Conceptual for `PrescriptionCard.tsx`):**

```tsx
// ... imports ...
import { Image as ExpoImage } from "expo-image"; // Assuming expo-image
import { useSupabaseStorageUrl } from "@/hooks/useSupabaseStorageUrl";

// ... inside PrescriptionCard component
const { url: thumbnailUrl, isLoading: isLoadingThumbnail } =
  useSupabaseStorageUrl({
    bucketId: prescription.image_bucket_id, // Assuming bucket ID is available
    path: prescription.front_image_path, // Prioritize front image for thumbnail
  });

// ... in JSX, potentially within the Card's structure or a new View
{
  thumbnailUrl && !isLoadingThumbnail && (
    <ExpoImage
      source={{ uri: thumbnailUrl }}
      style={{ width: 50, height: 50, borderRadius: 4, marginRight: 8 }} // Example style
      placeholder={/* ... */}
      contentFit="cover"
    />
  );
}
// Or integrate with the main Card component if it supports an image slot
```

### 3. In-Form Image Management Controls (Preview, Replace, Delete, Full Preview)

This section focuses on the user interactions for managing images directly within forms like `PrescriptionForm.tsx` (which uses `ImageCapture.tsx`), `LabResultForm.tsx`, and `MedicineForm.tsx`.

**Current State (primarily via `ImageCapture.tsx` in `PrescriptionForm.tsx`):**

- **Preview**: `ImageCapture.tsx` shows an inline preview of the selected/captured image using React Native's `<Image>`.
- **Replace**: Achieved by re-triggering the image selection (camera/library) via buttons like "Change Front Image". The new image replaces the old one in the preview and state.
- **Delete/Remove**: `ImageCapture.tsx` calls an `onRemove` prop, which in `PrescriptionForm.tsx` nullifies the image URI in the state and sets a "delete flag" (e.g., `deleteFrontImage = true`).

**Recommendations:**

- **Use `expo-image` for Inline Previews in `ImageCapture.tsx`**:
  - As stated in Recommendation #1, replace the standard `<Image>` in `ImageCapture.tsx` with `expo-image`. This ensures consistency and brings `expo-image` benefits (caching if URI is remote, placeholders, transitions) to the form previews.
- **Enhance Visual Feedback for "Delete"**:
  - When an image is marked for deletion (e.g., user clicks a "Remove" or "Clear" button for an existing image), provide clearer visual feedback in the preview area. Instead of just clearing the preview or showing nothing, consider:
    - Displaying a "Marked for deletion" message.
    - Overlaying a "delete" icon on a dimmed version of the image.
    - This is particularly important for existing images that are being removed, so the user understands the pending action before saving the form.
- **Implement "Tap-to-Zoom" / Full-Screen Preview Modal**:
  - Allow users to tap on the inline preview in `ImageCapture.tsx` (or any form image preview) to open a modal displaying the image in a larger, possibly full-screen view.
  - This modal should support basic zoom/pan if feasible, especially for detailed images like lab results or prescription photos.
  - Libraries like `react-native-image-viewing` or a custom modal with `expo-image` can be used.
- **Clearer "Replace" vs. "Remove" Actions**:
  - Ensure the UI for replacing an existing image versus removing it entirely is distinct and intuitive.
  - If an image exists, buttons could be "Replace Image" and "Remove Image".
  - If no image exists, it would be "Add Image".
- **Visual Indication for New vs. Existing Images**:
  - Subtly differentiate between a newly selected (but not yet uploaded) local image and an already saved/existing image (remote URI) being displayed in the form. This can help users understand the state.

**Affected Forms (to be reviewed for these enhancements):**

- `components/prescriptions/PrescriptionForm.tsx` (and its use of `ImageCapture.tsx`)
- `components/labresults/LabResultForm.tsx`
- `components/medications/MedicineForm.tsx`

- **Ensure Backend Deletion on Save:**
  - When a form is saved after an image has been marked for removal (e.g., `deleteFrontImage` flag is true):
    - The corresponding entity mutation hook (e.g., `useUpdatePrescription`) **must** update the database record to remove the image path (e.g., set it to `null`).
    - Crucially, the hook **must** also make a call to Supabase Storage to delete the actual file from its bucket to prevent orphaned files.
  - This needs to be verified or implemented for all forms managing images (prescriptions, lab results, medications).

### 4. Image Upload Optimizations (Considerations)

- **Content-Type in `useSupabaseStorageUpload`**:
  - The `getContentTypeFromUri` helper in `useSupabaseStorageUpload.ts` has a limited set of extensions. While `expo-image-manipulator` currently forces JPEG output, ensure this helper is robust or consider getting content type from `expo-image-manipulator`'s result if it provides it more reliably, especially if other formats were to be allowed in the future.
  - `expo-image-manipulator` result includes `type` (e.g. `image/jpeg`). This should ideally be used. Currently, the code in `useSupabaseStorageUpload.ts` uses `formData.append('file', { uri: fileUri, name: appendName, type: appendType } as any);` where `appendType` comes from `getContentTypeFromUri`. This could be simplified/made more robust by using the type from the manipulation result.
- **WebP for Uploads**:
  - `expo-image-manipulator` can save to WebP (`ImageManipulator.SaveFormat.WEBP`).
  - If Supabase Storage and `expo-image` (for display) handle WebP well, consider changing the manipulation output to WebP for potentially smaller file sizes. This would require testing compatibility.

### 5. Error Handling for Image Loading

- Ensure that if an image fails to load (e.g., broken URL, network error), a fallback UI (e.g., an icon or placeholder) is gracefully displayed. `expo-image` provides an `onError` prop and better placeholder support that can help with this. The current `components/ui/Card.tsx` shows an icon if `imageUrl` is null, which is a good start.

## Benefits of Proposed Changes

- **Improved Performance**: `expo-image` caching and optimized loading.
- **Enhanced User Experience**: Visual thumbnails in cards, smooth transitions, placeholders, clearer in-form image management, and full-screen previews.
- **Better Alignment with Best Practices**: Explicit use of `expo-image`.
- **Maintainability**: Consistent image handling components.

These changes should provide a more polished and performant experience for users interacting with images in the application.
