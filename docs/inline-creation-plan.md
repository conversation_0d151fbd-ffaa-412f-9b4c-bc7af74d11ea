# Plan: Inline Creation of Patients and Doctors in Forms

This plan outlines the steps to enhance `PrescriptionForm.tsx` and `LabResultForm.tsx` to allow users to create new patients and doctors directly from within the forms using an enhanced `SearchableSelect.tsx` component and Zustand for state coordination.

## I. Zustand Store for Post-Creation Handling - Done

We'll use a Zustand store to communicate the ID of a newly created entity (Patient or Doctor) back to the forms, so it can be auto-selected.

1.  **Create `store/modalCreationStore.ts` (New File)**

    - Define a Zustand store to temporarily hold the ID of a newly created entity.
    - **State**:
      - `newlyCreatedPatientId: string | null`
      - `newlyCreatedDoctorId: string | null`
    - **Actions**:
      - `setNewlyCreatedPatientId: (id: string | null) => void`
      - `setNewlyCreatedDoctorId: (id: string | null) => void`
      - `resetPatientCreation: () => void` (sets `newlyCreatedPatientId` to `null`)
      - `resetDoctorCreation: () => void` (sets `newlyCreatedDoctorId` to `null`)

    ```typescript
    // store/modalCreationStore.ts
    import { create } from "zustand";

    interface ModalCreationState {
      newlyCreatedPatientId: string | null;
      newlyCreatedDoctorId: string | null;
      setNewlyCreatedPatientId: (id: string | null) => void;
      setNewlyCreatedDoctorId: (id: string | null) => void;
      resetPatientCreation: () => void;
      resetDoctorCreation: () => void;
    }

    export const useModalCreationStore = create<ModalCreationState>((set) => ({
      newlyCreatedPatientId: null,
      newlyCreatedDoctorId: null,
      setNewlyCreatedPatientId: (id) => set({ newlyCreatedPatientId: id }),
      setNewlyCreatedDoctorId: (id) => set({ newlyCreatedDoctorId: id }),
      resetPatientCreation: () => set({ newlyCreatedPatientId: null }),
      resetDoctorCreation: () => set({ newlyCreatedDoctorId: null }),
    }));
    ```

## II. Enhance `SearchableSelect.tsx` - Done

Modify the `SearchableSelect` component to include an "Add New" button that triggers a callback.

1.  **File**: `components/common/SearchableSelect.tsx`
2.  **Add New Props**:
    - `showCreateNewButton?: boolean;` (Optional: defaults to `false`)
    - `onCreateNew?: () => void;` (Optional: callback when "Create New" is pressed)
    - `createText?: string;` (Optional: text for the create button, e.g., "Create New Patient")
3.  **UI Modification**:

    - Inside the `<Modal>`, specifically within the `<View className="bg-white rounded-t-xl max-h-3/4">`, add a button. A good place might be below the search input or as a distinct item in/above the `FlatList`.
    - This button should:
      - Be visible only if `showCreateNewButton` is `true`.
      - Display the `createText` (or a default like "Create New").
      - Call `onCreateNew` when pressed.
      - Close the modal after calling `onCreateNew` by also calling `setModalVisible(false)`.

    **Example Snippet for `SearchableSelect.tsx` (within the Modal's content area):**

    ```tsx
    // ... existing imports and props ...
    // Add new props to SearchableSelectProps interface:
    // showCreateNewButton?: boolean;
    // onCreateNew?: () => void;
    // createText?: string;

    // ... in the component ...
    // const { /*...,*/ showCreateNewButton = false, onCreateNew, createText = 'Create New' } = props;

    // Inside the Modal, for example, after the search TextInput:
    {
      showCreateNewButton && onCreateNew && (
        <Pressable
          onPress={() => {
            onCreateNew();
            setModalVisible(false); // Close the select modal
          }}
          className="p-4 border-t border-b border-gray-200 bg-blue-50 hover:bg-blue-100"
        >
          <Text className="text-blue-600 font-medium text-center">
            {createText}
          </Text>
        </Pressable>
      );
    }
    // This can be placed before, after, or as part of the FlatList (e.g. ListHeaderComponent or ListFooterComponent)
    ```

## III. Modify Creation Modal Screens - Done

Update `modal-patient.tsx` and `modal-doctor.tsx` to use the Zustand store after successfully creating an entity.

1.  **Files**:
    - `app/(protected)/modals/modal-patient.tsx`
    - `app/(protected)/modals/modal-doctor.tsx`
2.  **Import Zustand Store**:
    - `import { useModalCreationStore } from '@/store/modalCreationStore';`
3.  **Update `handleSubmit` Logic**:

    - In the `onSuccess` callback of the respective creation mutations (`createPatientMutation.mutate` or `createDoctorMutation.mutate`):
      - Before `router.dismiss()`, get the ID of the newly created entity (ensure your mutation hook provides this in its `onSuccess` callback data).
      - Call `setNewlyCreatedPatientId(newPatient.id)` or `setNewlyCreatedDoctorId(newDoctor.id)`.

    **Example for `modal-patient.tsx`:**

    ```tsx
    // ... existing imports ...
    import { useModalCreationStore } from "@/store/modalCreationStore"; // Add this

    export default function PatientModalScreen() {
      // ... existing setup ...
      const { setNewlyCreatedPatientId } = useModalCreationStore(); // Add this

      const handleSubmit = (formData: PatientFormData) => {
        if (isCreateMode) {
          createPatientMutation.mutate(formData, {
            onSuccess: (newPatient) => {
              // Ensure newPatient (or newPatient.id) is returned
              setNewlyCreatedPatientId(newPatient.id); // Call before dismissing
              router.dismiss();
            },
            // ... onError ...
          });
        } else {
          // ... update logic ...
        }
      };
      // ... rest of the component ...
    }
    ```

    - Apply similar changes to `modal-doctor.tsx` for `setNewlyCreatedDoctorId`.

## IV. Integrate into `PrescriptionForm.tsx` - Done

Modify the prescription form to use the enhanced `SearchableSelect` and react to new entity creations.

1.  **File**: `components/prescriptions/PrescriptionForm.tsx`
2.  **Imports**:
    - `import { useRouter } from 'expo-router';`
    - `import { useModalCreationStore } from '@/store/modalCreationStore';`
3.  **Inside `PrescriptionForm` component**:
    - `const router = useRouter();`
    - `const { newlyCreatedPatientId, resetPatientCreation, newlyCreatedDoctorId, resetDoctorCreation } = useModalCreationStore();`
    - `const { setValue } = useForm<PrescriptionInsert>(...);` (Ensure `setValue` is destructured from `useForm` return).
4.  **`useEffect` for Patient Auto-Selection**:

    - Listen to `newlyCreatedPatientId` and `patientsData`.
    - When `newlyCreatedPatientId` is set:
      - The `useFetchPatients` hook should ideally refetch data automatically due to query invalidation from `useCreatePatient`. If not, you might need to manually trigger `patientsQuery.refetch()`.
      - Once `patientsData` contains the new patient, use `setValue('patient_id', newlyCreatedPatientId)` to update the form.
      - Call `resetPatientCreation()` to clear the store.

    ```tsx
    // Inside PrescriptionForm component
    const { data: patientsData, refetch: refetchPatients } = useFetchPatients(); // Assuming refetch is available or react-query handles it
    const { data: doctorsData, refetch: refetchDoctors } =
      useFetchUserDoctors();

    useEffect(() => {
      if (newlyCreatedPatientId && patientsData) {
        const patientExists = patientsData.some(
          (p) => p.id === newlyCreatedPatientId
        );
        if (patientExists) {
          setValue("patient_id", newlyCreatedPatientId);
          resetPatientCreation();
        } else {
          // Optional: If patient not immediately in list, refetch and then set.
          // This might indicate a slight delay or need for explicit refetch if cache invalidation isn't immediate.
          refetchPatients().then(() => {
            setValue("patient_id", newlyCreatedPatientId);
            resetPatientCreation();
          });
        }
      }
    }, [
      newlyCreatedPatientId,
      patientsData,
      setValue,
      resetPatientCreation,
      refetchPatients,
    ]);

    useEffect(() => {
      if (newlyCreatedDoctorId && doctorsData) {
        const doctorExists = doctorsData.some(
          (d) => d.id === newlyCreatedDoctorId
        );
        if (doctorExists) {
          setValue("doctor_id", newlyCreatedDoctorId);
          resetDoctorCreation();
        } else {
          refetchDoctors().then(() => {
            setValue("doctor_id", newlyCreatedDoctorId);
            resetDoctorCreation();
          });
        }
      }
    }, [
      newlyCreatedDoctorId,
      doctorsData,
      setValue,
      resetDoctorCreation,
      refetchDoctors,
    ]);
    ```

5.  **Update Patient `SearchableSelect`**:
    - Add `showCreateNewButton={true}`.
    - Add `createText="Create New Patient"`.
    - Add `onCreateNew={() => router.push('/modals/modal-patient')}`.
6.  **Update Doctor `SearchableSelect`**:
    - Add `showCreateNewButton={true}`.
    - Add `createText="Create New Doctor"`.
    - Add `onCreateNew={() => router.push('/modals/modal-doctor')}`.

## V. Integrate into `LabResultForm.tsx` - Done

Apply similar changes to the lab result form for patient creation.

1.  **File**: `components/labresults/LabResultForm.tsx`
2.  **Imports**:
    - `import { useRouter } from 'expo-router';`
    - `import { useModalCreationStore } from '@/store/modalCreationStore';`
3.  **Inside `LabResultForm` component**:
    - `const router = useRouter();`
    - `const { newlyCreatedPatientId, resetPatientCreation } = useModalCreationStore();`
    - `const { setValue } = useForm<LabResultFormData>(...);`
4.  **`useEffect` for Patient Auto-Selection**:

    - Similar to `PrescriptionForm`, listen to `newlyCreatedPatientId` and `patientsQuery.data`.
    - When `newlyCreatedPatientId` is set, update `patient_reference_id` using `setValue('patient_reference_id', newlyCreatedPatientId)`.
    - Call `resetPatientCreation()`.

    ```tsx
    // Inside LabResultForm component
    // const patientsQuery = useFetchPatients(); // Existing
    // const { data: patientsData, refetch: refetchPatients } = patientsQuery;

    useEffect(() => {
      if (newlyCreatedPatientId && patientsQuery.data) {
        const patientExists = patientsQuery.data.some(
          (p) => p.id === newlyCreatedPatientId
        );
        if (patientExists) {
          setValue("patient_reference_id", newlyCreatedPatientId);
          resetPatientCreation();
        } else {
          patientsQuery.refetch().then(() => {
            setValue("patient_reference_id", newlyCreatedPatientId);
            resetPatientCreation();
          });
        }
      }
    }, [
      newlyCreatedPatientId,
      patientsQuery.data,
      setValue,
      resetPatientCreation,
      patientsQuery.refetch,
    ]);
    ```

5.  **Update Patient `SearchableSelect`**:
    - Add `showCreateNewButton={true}`.
    - Add `createText="Create New Patient"`.
    - Add `onCreateNew={() => router.push('/modals/modal-patient')}`.

## VI. Verify React Query Cache Invalidation - Done

- Ensure that your `useCreatePatient` and `useCreateUserDoctor` hooks (within `usePatients.ts` and `useUserDoctors.ts` respectively) are configured to invalidate the relevant query keys upon successful mutation. This is crucial for the `SearchableSelect` lists (`patientOptions`, `doctorOptions`) to automatically update and include the newly created entity.
  - Example for a mutation hook using TanStack Query:
    ```typescript
    // In usePatients.ts or useUserDoctors.ts
    // const queryClient = useQueryClient();
    // onSuccess: () => {
    //   queryClient.invalidateQueries({ queryKey: ['patients'] }); // or ['doctors']
    // }
    ```

## VII. Testing and Refinement - To Do

1.  **Test Patient Creation**:
    - From `PrescriptionForm`: Open patient select, click "Create New Patient", fill form, save. Verify modal closes, and new patient is selected in `PrescriptionForm`.
    - From `LabResultForm`: Repeat the process.
2.  **Test Doctor Creation**:
    - From `PrescriptionForm`: Open doctor select, click "Create New Doctor", fill form, save. Verify modal closes, and new doctor is selected.
3.  **Data Integrity**:
    - Confirm the correct IDs are being saved in prescriptions and lab results.
4.  **User Experience**:
    - Ensure the flow is smooth and intuitive.
    - Check loading states and error handling.

This structured approach should help you integrate the desired functionality effectively. Remember to adapt the code snippets to the exact structure and naming conventions of your project.
