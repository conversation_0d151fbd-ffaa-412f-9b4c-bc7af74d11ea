# Query Optimization Guide for Single-User Application

This document outlines strategies to minimize API requests to Supabase in this single-user application, primarily by leveraging and tuning `@tanstack/react-query` caching capabilities.

## Core Principles for Single-User App Optimization

1.  **Aggressive Caching**: Since data integrity issues due to concurrent modifications by other users are not a concern, we can cache data for longer periods.
2.  **User-Driven Updates**: Assume data only changes on the server when the current user performs an action (Create, Update, Delete). Background refetching for external changes is largely unnecessary.
3.  **Minimize Redundant Fetches**: Avoid refetching data that is already available and fresh in the client-side cache.

## Strategies

### 1. Optimize `@tanstack/react-query` Default Options (Most Generic Approach)

- **`staleTime`**: Set to `Infinity` globally. This means data is considered fresh indefinitely and will always be served from the cache unless explicitly invalidated or the query key changes. This is highly effective for single-user apps.
- **`gcTime` (Garbage Collection Time)**: Set to a very long duration (e.g., 24 hours or more) or effectively `Infinity` if `staleTime` is `Infinity`. This ensures that cached data, even if inactive (no subscribers), remains in memory for a long time. The default is 5 minutes, which is too short if `staleTime` is infinite.
- **`refetchOnWindowFocus`**: Disable globally (`false`).
- **`refetchOnMount`**: Generally `true` (default) is fine for the initial data load when a component mounts. Can be overridden to `false` per query if initial fetch isn't always desired.
- **`refetchOnReconnect`**: Disable globally (`false`).

### 2. Rely on Manual Cache Invalidation (Key for `staleTime: Infinity`)

- With `staleTime: Infinity`, manual cache invalidation via `queryClient.invalidateQueries()` in your mutation hooks' `onSuccess` callbacks becomes the **primary mechanism** for refreshing data after user actions. Your current setup already does this well.

### 3. Conditional Fetching

- Continue using the `enabled: !!someDependency` pattern in `useQuery` options to prevent queries from running if their required parameters are not yet available.

### 4. Image URL Fetching Optimization (Specific to Signed URLs)

- **Understanding Current Behavior**: Your `useSupabaseStorageUrl` hook fetches signed URLs which have their own expiry (e.g., 1 hour). The hook itself uses React's `useEffect` and will refetch the URL based on its internal logic and dependencies, not directly controlled by TanStack Query's `staleTime` for the URL string.
- **Potential Optimization**: To reduce calls to Supabase for _generating_ signed URLs, you could modify `useSupabaseStorageUrl` to use `useQuery` internally. The `queryKey` would be `['signedImageUrl', bucketId, path]`, and the `queryFn` would be the Supabase call. You could then set a `staleTime` for this query (e.g., 55 minutes, or `Infinity` if the image path itself rarely changes and you rely on component lifecycle/key changes for new URLs). This caches the _URL string_. The image itself is cached by the `<Image>` component based on HTTP headers.
  - This is an advanced optimization, consider if a high frequency of identical `createSignedUrl` calls is an actual issue.

## Step-by-Step Implementation Plan

### Step 1: Update Global QueryClient Defaults (The Primary Optimization)

**File**: `app/_layout.tsx`

**Action**: Modify the `QueryClient` instantiation for aggressive, single-user app caching.

```typescript
// Before:
// const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       staleTime: 1000 * 60 * 1, // 1 minute
//       gcTime: 1000 * 60 * 5, // 5 minutes
//     },
//   },
// });

// After:
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity, // Data is fresh forever, relies on invalidation
      gcTime: 1000 * 60 * 60 * 24, // Cache inactive queries for 24 hours
      refetchOnWindowFocus: false,
      refetchOnMount: true, // Fetch when component mounts for the first time
      refetchOnReconnect: false,
    },
  },
});
```

**Explanation**:
This is the most impactful change. `staleTime: Infinity` means queries won't refetch due to staleness. `gcTime` is extended significantly. Automatic refetches on focus/reconnect are disabled. Data updates will now primarily be driven by cache invalidations in your mutation hooks.

### Step 2: Review Entity Hooks (Confirmation, Not Changes)

**Files**:

- `hooks/entities/usePrescriptions.ts`
- `hooks/entities/usePatients.ts`
- `hooks/entities/useLabResults.ts`
- `hooks/entities/useMedicines.ts`
- `hooks/entities/useUserDoctors.ts`

**Action**: No direct changes for `staleTime` are needed in these files if the global default is `Infinity`. The individual query configurations within these hooks will inherit the global defaults.

- **Confirm Inheritance**: Understand that all `useQuery` calls within these entity hooks will now use `staleTime: Infinity`, `refetchOnWindowFocus: false`, etc., unless explicitly overridden in the specific query's options.
- **Optional Overrides**: If, for a very specific query, you needed different behavior (e.g., a shorter `staleTime`), you could add it directly to that `useQuery`'s options. This is now the exception rather than the rule.

```typescript
// Example: Inside usePrescriptions.ts - useFetchPrescriptions
// This query will automatically use staleTime: Infinity from global defaults.
// No need to add staleTime: Infinity here unless you were overriding a *different* global default.
const useFetchPrescriptions = () => {
  return useQuery<PrescriptionWithDetails[], Error>({
    queryKey: getListQueryKey(),
    queryFn: () =>
      executeQuery(async (supabase, userId) => {
        // ... fetch logic
      }),
    // No 'staleTime: Infinity' needed here if globally set.
    // Add other options like 'enabled' as needed.
  });
};
```

### Step 3: Ensure Mutation Hooks Invalidate Correctly (Crucial)

**Action**: This is a verification step. Your mutation hooks should already be doing this.

- Confirm that all mutation hooks (`useCreate...`, `useUpdate...`, `useDelete...`) correctly call `queryClient.invalidateQueries()` in their `onSuccess` handlers, targeting the appropriate query keys for data that has changed.
- With `staleTime: Infinity`, this invalidation step is what tells TanStack Query to refetch the data next time it's requested.

### Step 4: Consider Optimizing `useSupabaseStorageUrl` (Optional)

**File**: `hooks/useSupabaseStorageUrl.ts`

**Action (If implementing the advanced optimization for URL strings)**:

1.  Import `useQuery` from `@tanstack/react-query`.
2.  Modify the hook to use `useQuery` to fetch and cache the signed URL string.

```typescript
// Potentially modified hooks/useSupabaseStorageUrl.ts
import { useQuery } from "@tanstack/react-query";
import { useSupabaseClient } from "./useSupabaseClient";

// ... (interfaces remain the same)

export function useSupabaseStorageUrl({
  bucketId,
  path,
  options = { expiresIn: 3600 }, // Default expiry: 1 hour
}: UseSupabaseStorageUrlParams) {
  const { supabase } = useSupabaseClient();

  const fetchSignedUrlFn = async () => {
    if (!supabase || !bucketId || !path) {
      return null; // Or throw error, depending on desired handling by useQuery
    }
    console.log(
      `Fetching new signed URL for path: ${path} in bucket: ${bucketId}`
    );
    const { data, error } = await supabase.storage
      .from(bucketId)
      .createSignedUrl(path, options.expiresIn || 3600);
    if (error) {
      console.error("Error getting signed URL:", error);
      throw error; // Let useQuery handle the error
    }
    return data?.signedUrl || null;
  };

  const {
    data: url,
    isLoading,
    error,
  } = useQuery<string | null, Error>(
    // Query key includes all dependencies that should trigger a new URL fetch
    ["signedImageUrl", bucketId, path, options.expiresIn],
    fetchSignedUrlFn,
    {
      enabled: !!supabase && !!bucketId && !!path,
      // Cache the URL string itself. It will still expire on Supabase's side.
      // Setting staleTime to slightly less than expiresIn ensures TanStack Query
      // considers refetching the URL string before it actually expires.
      staleTime: (options.expiresIn || 3600) * 1000 * 0.9, // e.g., 90% of its server-side validity
      // Or, if image paths are very stable per view & you manage invalidation carefully:
      // staleTime: Infinity,
      refetchOnWindowFocus: false, // Usually not needed for a URL string
      refetchOnMount: true, // Fetch if not already cached and fresh
      refetchOnReconnect: false,
    }
  );

  return { url: url || null, isLoading, error: error ? error : null };
}
```

**Note**: This modification makes `useSupabaseStorageUrl` TanStack Query-aware for the URL string itself. Test this thoroughly if you implement it.

### Step 5: Test Thoroughly

After implementing these changes:

1.  **Data Loading**: Verify data loads correctly on initial app start and screen navigation.
2.  **Mutations**: Ensure that after creating, updating, or deleting items, the relevant lists and detail views update correctly with fresh data (due to cache invalidation).
3.  **Image Loading**: If you modified `useSupabaseStorageUrl`, check that images load correctly and that new signed URLs are fetched when expected (e.g., after expiry or if the path changes).
4.  **App Behavior**: Monitor for any unexpected behavior related to data freshness or caching.

## Expected Outcome

- **Drastic Reduction in `SELECT` Queries**: Especially after the initial load, API calls for data fetching should be minimal.
- **Faster Perceived Performance**: Data will be served almost instantly from the cache.
- **Lower Supabase Resource Usage**: Fewer database reads.

This refined approach strongly leverages `staleTime: Infinity` globally, simplifying per-query configuration and maximizing caching for a single-user application while relying on robust mutation-driven cache invalidation.
