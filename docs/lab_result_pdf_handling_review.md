# Lab Result PDF Handling Review

Date: 2024-03-08

## Overview

This review covers the implementation of PDF attachment handling for Lab Results, particularly focusing on the flow originating from the webview (`lab-result-webview.tsx`) and its integration with existing form logic (`LabResultForm.tsx`), data hooks (`useLabResults.ts`), and modals (`modal-labresult.tsx`).

## Files Reviewed

- `hooks/entities/useLabResults.ts`
- `components/labresults/LabResultForm.tsx`
- `schema/lab-result.ts`
- `types/database.types.ts`
- `app/(protected)/modals/modal-labresult.tsx`
- `app/(protected)/modals/lab-result-webview.tsx`

## Findings

### 1. Centralized Logic & Adherence to Practices

- **Good Centralization**: Core file upload and deletion logic for both primary attachments and captured PDFs is well-centralized within the `useLabResults.ts` hook. This includes handling Supabase Storage operations.
- **Hook Usage**: Good use of custom hooks (`useSupabaseQuery`, `useSupabaseStorageUpload`, `useAppAlerts`) and React Query for state management and mutations.
- **Schema Validation**: Zod schema (`labResultFormSchema`) is used effectively for validating form data before database operations.
- **Database Schema**: The `labresults` table in `database.types.ts` correctly includes fields for `image_path`, `image_bucket_id`, `captured_pdf_path`, and `captured_pdf_bucket_id`, supporting the implemented features.

### 2. PDF Handling from Webview (`lab-result-webview.tsx` -> `modal-labresult.tsx`)

- **Clear Flow**: The process of capturing a PDF from the webview and passing it to the `modal-labresult.tsx` screen via navigation parameters (`newAttachmentUri`, `attachmentAction: 'attachCapturedPdf'`) is clear and functional.
- **Background Update**: In `modal-labresult.tsx`, a `useEffect` hook correctly processes these parameters and triggers an update mutation.
- **Replacement Behavior**: The `attachCapturedPdf` action, when initiated from the webview, results in the **replacement** of any existing `captured_pdf_path` for the given lab result. This is due to `deleteExistingCapturedPdf: true` being passed in the `useEffect` of `modal-labresult.tsx` when a new `capturedPdfFileUri` is present. Given the single `captured_pdf_path` field in the database, this is logical.

### 3. `LabResultForm.tsx` State and UI

- **Primary Attachment**: The form manages state for a primary attachment (`fileUri`, `newLocalFileUri`, `fileName`, `deleteFile`, `displayFileType`). The `displayFileType` state toggles the UI to accept _either_ an image (via `ImageCapture`) _or_ a document (via `DocumentPickerComponent`) for this single primary slot. It does not allow selecting both simultaneously for the primary slot. **This behavior will need to change based on new proposals (see Recommendation 6).**
- **Existing Captured PDF Display**: The form displays information about an existing `captured_pdf_path` (the secondary PDF captured from webview) and provides an option to mark it for deletion (`deleteCapturedPdf` state, `handleRemoveCapturedPdfPress` function).
- **No New Captured PDF Upload via Form UI**: The form UI itself (e.g., `DocumentPickerComponent`) is not set up to upload a _new_ file specifically into the `captured_pdf_path` field. This role is handled by the webview flow. This separation is clear.
- **Unused Code**:
  - State variables `showRemoveCapturedPdfConfirmation`, `setShowRemoveCapturedPdfConfirmation`.
  - Handler functions `handleDeleteCapturedPdf`, `handleCancelDeleteCapturedPdf`.
  - The `capturedPdfUrl` fetched via `useSupabaseStorageUrl` for an existing captured PDF is not actively used for viewing (the "View PDF" button is commented out).
- **Complex `useEffect`**: The `useEffect` (around lines 240-283) managing the initial display state for the primary attachment is complex and has several dependencies, making it potentially fragile.

### 4. `useLabResults.ts` Hook

- **Comprehensive CRUD**: The hook provides comprehensive functions for fetching, creating, updating, and deleting lab results.
- **Attachment Logic in Mutations**:
  - `create`: Handles new primary attachment upload.
  - `update`: Handles:
    - Updating/replacing the primary attachment (`fileUri`, `deleteFile`).
    - Uploading a new captured PDF (`capturedPdfFileUri`), which also deletes any old one.
    - Deleting an existing captured PDF without uploading a new one (`deleteExistingCapturedPdf`).
  - `remove`: Correctly attempts to delete both primary and captured PDF files from storage.
- **Bucket for Captured PDFs**: When a new `capturedPdfFileUri` is uploaded via the `update` mutation, the code sets `capturedPdfBucketIdValue = 'labresults';`. A comment nearby (`// Or a specific bucket like 'captured_pdfs'`) suggests a potential for a different bucket, creating slight ambiguity if a separate bucket strategy was intended. The current implementation uses the `labresults` bucket for both primary and webview-captured PDF attachments.

### 5. Attachment Slot Clarification & Desired State

- **Two Distinct Attachment Fields**: The system utilizes two main fields in the `labresults` database table for file attachments:
  - **`image_path` (Primary Attachment)**: This slot is intended for the main file associated with the lab result. It can be an image file (e.g., a photo of the lab result, typically handled by `ImageCapture.tsx`) or a document file (e.g., a PDF picked from local storage, typically handled by `DocumentPicker.tsx`). The `LabResultForm.tsx` UI manages input for this single primary slot. **The role of `DocumentPicker.tsx` for this slot is under review based on new proposals (see Recommendation 6).**
  - **`captured_pdf_path` (Secondary/Webview PDF Attachment)**: This slot is specifically for PDF files generated by capturing content from `lab-result-webview.tsx`. **This slot will also be targeted by locally picked PDFs according to new proposals (see Recommendation 6).**
- **Support for Desired State**: The current database schema and backend logic _do support_ the desired state of having both a primary attachment (which could be an image or a PDF) and a secondary webview-captured PDF associated with a single lab result entry. For instance, a user can upload an image as the primary attachment and then capture a PDF from the webview. These will correctly populate `image_path` and `captured_pdf_path` respectively.
- **Local File Flow for Primary Slot**: The behavior where the local file flow (using `DocumentPickerComponent` or `ImageCaptureComponent` in `LabResultForm.tsx`) populates only the primary `image_path` slot is by design for that UI section. It allows choosing one file for that primary role. **This is proposed to change for PDFs picked via `DocumentPickerComponent` (see Recommendation 6).**

## Recommendations

1.  **Clean Up `LabResultForm.tsx`**:

    - **Remove Unused Code**: Delete the state variables `showRemoveCapturedPdfConfirmation`, `setShowRemoveCapturedPdfConfirmation` and the handler functions `handleDeleteCapturedPdf`, `handleCancelDeleteCapturedPdf`.
    - **Simplify `useEffect`**: Review and refactor the complex `useEffect` (lines 240-283) responsible for the primary attachment's initial display state. Aim for simpler logic or more targeted effects to improve readability and reduce potential issues.
    - **Decide on "View Captured PDF"**: If viewing an existing captured PDF directly from the form is a desired feature, uncomment and implement the button using the `capturedPdfUrl` (see new recommendation below on document viewing).

2.  **Clarify Bucket Strategy in `useLabResults.ts`**:

    - **Consistency**: Decide on the definitive bucket for storing captured PDFs.
      - If `'labresults'` is the intended bucket for all lab result related files (primary and captured PDFs), then remove the potentially confusing comment: `// Or a specific bucket like 'captured_pdfs'`.
      - If a separate bucket (e.g., `'captured_pdfs'`) is preferred for these files, update the line `capturedPdfBucketIdValue = 'labresults';` to use the correct bucket name when `capturedPdfFileUri` is provided in the `update` mutation. Ensure this new bucket exists in Supabase storage with appropriate policies.
    - **Accuracy**: Ensure all comments in `useLabResults.ts` related to file paths, bucket names, and attachment logic are accurate and reflect the current implementation.

3.  **User Experience for Background PDF Updates (`modal-labresult.tsx`)**:

    - **Review**: Manually walk through the user experience. When a PDF is attached via the webview flow, the `useEffect` in `modal-labresult.tsx` updates the lab result in the background.
    - **Ensure UI Reflection**: Confirm that if the `LabResultForm` is still active or re-rendered, it clearly and correctly reflects the newly attached/updated PDF information. The current dependency of `initialFormValues` on the fetched `labResult` (which is refetched) should theoretically handle this, but a visual confirmation is advised. Consider if any additional visual cues to the user about the background update are necessary.

4.  **Documentation and Comments**:

    - **Clarify Replacement Behavior**: Add a comment in `modal-labresult.tsx` or `useLabResults.ts` to explicitly state that the `attachCapturedPdf` action (originating from the webview) _replaces_ any existing captured PDF.
    - **Accuracy**: Ensure all comments in `useLabResults.ts` related to file paths, bucket names, and attachment logic are accurate and reflect the current implementation.

5.  **Enhance Document Viewing Capabilities**:

    - **View Primary Attachment (PDFs)**: Implement functionality to allow users to view the primary attachment if it's a PDF (i.e., when `image_path` points to a PDF file). This might involve checking the file type/extension and using `Linking.openURL()` or an in-app PDF viewing library. **This is especially relevant if `image_path` can still hold non-image documents.**
    - **Enable "View Captured PDF"**: Uncomment and fully implement the "View Captured PDF" button in `LabResultForm.tsx`. This should use the `capturedPdfUrl` (obtained via `useSupabaseStorageUrl` for `captured_pdf_path`) and likely `Linking.openURL()` or an in-app PDF viewer. This will now apply to PDFs from webview _and_ locally picked PDFs.
    - **Consistent Viewing Experience**: Consider a unified or clear UI approach for viewing any attached file (image or PDF) associated with a lab result. For example, display "View Image" or "View PDF" buttons based on the nature of the file in `image_path` and similarly for `captured_pdf_path`.

6.  **Redirect Locally Picked PDFs to `captured_pdf_path` (NEW PROPOSAL)**:
    - **Objective**: Align the handling of PDFs picked locally via `DocumentPickerComponent` with PDFs captured from the webview, storing them in `captured_pdf_path` instead of `image_path` (primary attachment slot).
    - **Changes Required**:
      - **`LabResultForm.tsx`**:
        - Modify the behavior of `DocumentPickerComponent`. When a PDF is selected, its URI should be routed to a new state variable (e.g., `localCapturedPdfUri`) distinct from the primary attachment's `newLocalFileUri`.
        - The UI for attachments will need redesign. Instead of a single togglable slot, consider:
          - A dedicated section for "Primary Image" (using `ImageCaptureComponent`, targeting `image_path`).
          - A dedicated section for "Attach PDF Document" (using `DocumentPickerComponent`, now targeting `captured_pdf_path`).
        - The `onFormSubmit` callback from `modal-labresult.tsx` will need to receive this `localCapturedPdfUri` in addition to other form data and the primary image URI.
      - **`modal-labresult.tsx`**:
        - The `handleSubmit` function must be updated to accept `localCapturedPdfUri` (or a similar parameter) from `LabResultForm`.
        - This URI should then be passed as the `capturedPdfFileUri` argument to `createLabResultMutation.mutateAsync` or `updateLabResultMutation.mutateAsync`.
      - **`hooks/entities/useLabResults.ts`**:
        - **`create` mutation**: Modify to accept an optional `capturedPdfFileUri` parameter. If provided, upload this file and save its path/bucket to `captured_pdf_path` and `captured_pdf_bucket_id` respectively, in addition to handling the primary `fileUri` for `image_path`.
        - **`update` mutation**: This mutation already accepts `capturedPdfFileUri`. Ensure that when this URI comes from a locally picked PDF, the logic (e.g., for `deleteExistingCapturedPdf`) is appropriate. If a new local PDF is picked to go into `captured_pdf_path`, it should likely replace any PDF already in that slot for the current lab result.
    - **Implication for `image_path`**: With this change, `image_path` would primarily store images. If `DocumentPickerComponent` is still to be used for non-PDF documents intended for the primary slot, this needs further clarification and UI/logic design.

## Conclusion

The system for handling lab result attachments, including the new PDF capture from webview, is largely well-structured and supports storing both a primary file and a webview-captured PDF. The proposed change to redirect locally picked PDFs to the `captured_pdf_path` aims to further unify PDF handling. Key areas for development will be updating the form UI and data flow, modifying the `create` mutation in `useLabResults.ts`, and ensuring robust document viewing capabilities for all attachment types. Clarification of the storage bucket strategy and minor code cleanup remain relevant. The core logic for file operations is robust and centralized.
