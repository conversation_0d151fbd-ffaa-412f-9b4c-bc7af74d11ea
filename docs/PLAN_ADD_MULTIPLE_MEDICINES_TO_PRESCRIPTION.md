## Plan: Enhance PrescriptionForm to Add Multiple Medicines

**Goal:** Modify `PrescriptionForm.tsx` to allow users to add, edit, and remove multiple medicines associated with a single prescription. The existing `user_meds` table will be used to store these prescribed medicines, linking them to the `prescriptions` table.

**I. Database Schema (Confirmation - No Changes Needed)**

- **`prescriptions` table:** Stores main prescription details (patient, doctor, date).
- **`allmeds` table:** Master catalog of predefined medicines.
- **`user_meds` table:** Stores individual medicines prescribed as part of a prescription.
  - `id` (PK)
  - `prescription_id` (FK to `prescriptions.id`) - Links this medicine entry to a specific prescription.
  - `med_id` (FK to `allmeds.id`, nullable) - Links to a catalog medicine if not custom.
  - `name` (text) - Name of the medicine.
  - `is_custom` (boolean) - True if not from `allmeds`.
  - Dosage, frequency, duration, notes, patient_id, user_id, etc.
- This structure correctly supports one prescription having many prescribed medicines (one-to-many via `user_meds.prescription_id`).

**II. TypeScript Type Updates (If Necessary)**

1.  **Review `Prescription` related types:**

    - In `@/schema/prescription.ts` (or similar type definition files).
    - The main `Prescription` type might need to include an optional array of `UserMed` (or a similar type representing a medicine entry from `user_meds`). This will be primarily for UI state management and passing data to/from the form.
    - Example:

      ```typescript
      import { UserMed } from "@/hooks/entities/useMedicines"; // Assuming UserMed is the type for an item in user_meds

      export interface PrescriptionInsert {
        // ... existing fields
        medicines?: Omit<
          UserMed,
          "id" | "prescription_id" | "created_at" | "updated_at" | "user_id"
        >[]; // For new medicines to be created
      }

      export interface PrescriptionUpdate {
        // ... existing fields
        medicines?: (Omit<
          UserMed,
          "prescription_id" | "created_at" | "updated_at" | "user_id"
        > & { id?: string })[]; // For medicines to be updated or created (if id is missing)
        deleted_medicine_ids?: string[]; // IDs of medicines to be removed
      }

      export interface Prescription extends Tables<"prescriptions"> {
        user_meds: UserMed[]; // For displaying existing medicines
      }
      ```

**III. `PrescriptionForm.tsx` Enhancements**

1.  **State Management for Medicines:**

    - Add a new state variable within `PrescriptionForm` to hold an array of medicine objects that the user is adding/editing for the current prescription.
      - `const [prescribedMedicines, setPrescribedMedicines] = useState<Array<Partial<UserMed> & { _localId?: string }>>([]);`
      - `_localId` can be used for keying in lists before an actual DB ID is available.
    - When `initialValues` are provided (edit mode), populate `prescribedMedicines` from `initialValues.user_meds`.

2.  **UI for Adding/Managing Medicines:**

    - **"Add Medicine" Button:** Triggers a modal or an inline form section to input details for a new medicine.
    - **Medicine Input Section/Modal:**
      - This could reuse or adapt parts of the existing `MedicineForm.tsx` or be a simplified version.
      - Fields needed:
        - Searchable select to choose from `allmeds` (linking `med_id`) OR input for custom medicine `name`.
        - Inputs for `dosage_amount`, `dosage_unit`, `frequency_amount`, `frequency_unit`, `duration_amount`, `duration_unit`, `notes`.
        - A way to "Save" or "Add" this medicine to the `prescribedMedicines` list in the parent `PrescriptionForm`.
    - **Display List of Added Medicines:**
      - Below the main prescription fields, display a list of medicines currently added to the prescription (from `prescribedMedicines` state).
      - Each item in the list should show key medicine details (name, dosage, frequency, duration).
      - Each item should have "Edit" and "Remove" buttons.
        - "Edit": Opens the medicine input section/modal pre-filled with that medicine's data.
        - "Remove": Removes the medicine from the `prescribedMedicines` state. For existing medicines (in edit mode), this will mark them for deletion.

3.  **Component for Medicine Input (`PrescriptionMedicineSubForm` or similar):**

    - Consider creating a new sub-component to encapsulate the form elements for a single medicine entry (name, dosage, frequency, etc.). This would be used both for adding new medicines and editing existing ones within the `PrescriptionForm`.
    - This sub-form would take an optional `initialValues` (for a single medicine) and an `onSubmit` handler that updates the `prescribedMedicines` array in the main `PrescriptionForm`.

4.  **Hooks Integration:**
    - `useMedicines` (from `hooks/entities/useMedicines.ts`):
      - The `useFetchMedicines` (or a variant to fetch from `allmeds`) will be needed for the searchable select if users can pick from the global `allmeds` catalog.
    - `usePatients` and `useUserDoctors` are already correctly used for the prescription itself.

**IV. Form Submission Logic (`onSubmit` in `PrescriptionForm.tsx`)**

1.  **Modify `onSubmit` function signature:**

    - The data passed to the `onSubmit` prop of `PrescriptionForm` will need to include the array of prescribed medicines.
    - The parent component (e.g., an Add/Edit Prescription screen) will handle the actual DB operations.

2.  **Data Preparation in Parent Component's Handler:**
    - When the `PrescriptionForm` is submitted, the handler function (passed as `onSubmit` prop) will receive the main prescription data and the list of `prescribedMedicines`.
    - **Creating a New Prescription:**
      1.  Insert the main prescription data into the `prescriptions` table to get the `prescription.id`.
      2.  Iterate through the `prescribedMedicines` array from the form. For each medicine:
          - Prepare a `UserMedInsert` object.
          - Set `prescription_id` to the ID obtained in step 1.
          - Set `patient_id` (likely from the main prescription's `patient_id`).
          - Set `user_id`.
          - Insert this object into the `user_meds` table using `useCreateMedicine` (or a batch insert if available/preferred).
    - **Updating an Existing Prescription:**
      1.  Update the main prescription data in the `prescriptions` table.
      2.  Manage `user_meds` entries:
          - **New Medicines:** For medicines in the form's `prescribedMedicines` list that don't have an `id` (i.e., they are new), insert them into `user_meds` table, linking them to the current `prescription.id`.
          - **Updated Medicines:** For medicines that have an `id`, update their corresponding rows in `user_meds`.
          - **Deleted Medicines:** Identify medicines that were initially part of the prescription (fetched with `initialValues`) but are no longer in the `prescribedMedicines` list. Delete these from the `user_meds` table using `useDeleteMedicine`. Track IDs of medicines to be deleted (e.g., via `deleted_medicine_ids` in the `PrescriptionUpdate` type).

**V. Data Fetching for Edit Mode**

1.  When editing a prescription, the query that fetches the prescription details must also fetch its associated `user_meds` entries.
    - This likely involves a Supabase query like:
      ```javascript
      supabase
        .from("prescriptions")
        .select(
          `
          *,
          user_meds (*)
        `
        )
        .eq("id", prescriptionId)
        .single();
      ```
    - The fetched `user_meds` array will be passed to `PrescriptionForm` as part of `initialValues` to populate the `prescribedMedicines` state.

**VI. API/Hook Adjustments (`useMedicines.ts` & `usePrescriptions.ts`)**

1.  **`useMedicines` (`hooks/entities/useMedicines.ts`):**
    - Ensure `create`, `update`, and `delete` mutations are robust.
    - Consider adding a hook to fetch from the `allmeds` table for the searchable select (e.g., `useFetchAllMedsCatalog`).
      - `useFetchAllMedsCatalog`: `supabase.from('allmeds').select('id, name, strength, form')`
2.  **`usePrescriptions` (or equivalent hook for managing prescriptions):**
    - The `createPrescription` mutation will need to accept the list of medicines and handle the creation of `user_meds` entries after the main prescription is created.
    - The `updatePrescription` mutation will need to accept the list of medicines and handle the creation, update, and deletion of `user_meds` entries.
    - The `fetchPrescriptionById` query needs to include the related `user_meds` as shown in section V.

**VII. UI/UX Considerations**

- **Clarity:** Make it clear to the user how to add, edit, and see the list of medicines for the current prescription.
- **Efficiency:** For selecting from `allmeds`, a good search/autocomplete is essential.
- **Error Handling:** Provide feedback for any errors during medicine addition/saving.
- **Responsive Design:** Ensure the medicine management section works well on different screen sizes.

**VIII. Testing**

- Test creating prescriptions with no medicines, one medicine, and multiple medicines.
- Test editing prescriptions:
  - Adding new medicines.
  - Updating existing medicines.
  - Deleting medicines.
  - A combination of the above.
- Test with both custom medicines and medicines selected from `allmeds`.

This revised plan should guide the implementation effectively using the existing database structure.
