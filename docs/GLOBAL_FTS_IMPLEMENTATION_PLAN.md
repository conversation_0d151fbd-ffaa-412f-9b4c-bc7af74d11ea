# Plan: Implementing Global Full-Text Search with FTS Columns

This document outlines the steps to implement a global search functionality across various tabs in the application. It leverages PostgreSQL's Full-Text Search (FTS) capabilities by adding dedicated `tsvector` columns to relevant tables and uses <PERSON>ustand for global search state management.

## Phase I: Database Modifications (User Task)

**Goal:** Enhance database tables with FTS `tsvector` columns and GIN indexes for efficient text searching.

**Your Responsibilities:**

1.  **Execute the following SQL queries** against your Supabase database. These queries will:

    - Add a new column named `fts_document` of type `tsvector` to each specified table.
    - Define this column as a generated column that automatically concatenates and converts relevant text fields into a searchable `tsvector` using the `'english'` configuration.
    - Create a GIN index on the `fts_document` column for each table to ensure fast search performance.

    **Important:** Review the fields included in the `tsvector` for each table. For example, the `labresults` table includes the `password` field in the search; you might want to remove it if it's not intended to be searchable.

2.  **After successfully executing the SQL queries, regenerate your database type definitions.**
    If you are using the Supabase CLI and have local development setup, you can typically do this with a command like:

    ```bash
    supabase gen types typescript --local > types/database.types.ts
    ```

    Or, if you use a different method (e.g., a specific script or Supabase dashboard feature), please follow that to update `types/database.types.ts`. This step is crucial for TypeScript to recognize the new `fts_document` columns.

3.  **Inform the AI assistant (me)** once these database changes are complete and `types/database.types.ts` has been updated.

---

### SQL Queries for Database Modification:

**1. Table: `prescriptions`**
_Relevant for: Prescriptions Tab (`app/(protected)/(tabs)/index.tsx`)_
_Searches fields: `name`, `notes`_

```sql
ALTER TABLE public.prescriptions
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX prescriptions_fts_document_idx ON public.prescriptions USING gin(fts_document);
```

**2. Table: `labresults`**
_Relevant for: Lab Results Tab (`app/(protected)/(tabs)/lab-results.tsx`)_
_Searches fields: `lab_name`, `password`, `patient_name`, `phone_number1`, `phone_number2`, `phone_number3`, `website`_
**Note:** The `password` field is included below. Please review and remove `coalesce(password, '') || ' ' ||` if passwords should not be searchable.

```sql
ALTER TABLE public.labresults
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(lab_name, '') || ' ' ||
        coalesce(password, '') || ' ' || -- Review: Remove if password should not be searchable
        coalesce(patient_name, '') || ' ' ||
        coalesce(phone_number1, '') || ' ' ||
        coalesce(phone_number2, '') || ' ' ||
        coalesce(phone_number3, '') || ' ' ||
        coalesce(website, '')
    )
) STORED;

CREATE INDEX labresults_fts_document_idx ON public.labresults USING gin(fts_document);
```

**3. Table: `user_meds`**
_Relevant for: User's Medications Tab (`app/(protected)/(tabs)/(meds-tabs)/meds.tsx`)_
_Searches fields: `name`, `description`, `notes`_

```sql
ALTER TABLE public.user_meds
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(description, '') || ' ' ||
        coalesce(notes, '')
    )
) STORED;

CREATE INDEX user_meds_fts_document_idx ON public.user_meds USING gin(fts_document);
```

**4. Table: `allmeds`**
_Relevant for: All Medications Tab (`app/(protected)/(tabs)/(meds-tabs)/all-meds.tsx`)_
_Searches fields: `name`, `description`_

```sql
ALTER TABLE public.allmeds
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(description, '')
    )
) STORED;

CREATE INDEX allmeds_fts_document_idx ON public.allmeds USING gin(fts_document);
```

**5. Table: `user_doctors`**
_Relevant for: User's Doctors Tab (`app/(protected)/(tabs)/(doctors-tabs)/doctors.tsx`)_
_Searches fields: `name`, `city`, `specialty`, `workplace`, `workplace_address`, `notes`, `phone_number`_

```sql
ALTER TABLE public.user_doctors
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(notes, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX user_doctors_fts_document_idx ON public.user_doctors USING gin(fts_document);
```

**6. Table: `alldoctors`**
_Relevant for: All Doctors Tab (`app/(protected)/(tabs)/(doctors-tabs)/all-doctors.tsx`)_
_Searches fields: `name`, `city`, `specialty`, `workplace`, `workplace_address`, `phone_number`_

```sql
ALTER TABLE public.alldoctors
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '') || ' ' ||
        coalesce(city, '') || ' ' ||
        coalesce(specialty, '') || ' ' ||
        coalesce(workplace, '') || ' ' ||
        coalesce(workplace_address, '') || ' ' ||
        coalesce(phone_number, '')
    )
) STORED;

CREATE INDEX alldoctors_fts_document_idx ON public.alldoctors USING gin(fts_document);
```

**7. Table: `patients`**
_Relevant for: Patients Tab (`app/(protected)/(tabs)/(doctors-tabs)/patients.tsx`)_
_Searches fields: `name`_

```sql
ALTER TABLE public.patients
ADD COLUMN fts_document tsvector
GENERATED ALWAYS AS (
    to_tsvector('english',
        coalesce(name, '')
    )
) STORED;

CREATE INDEX patients_fts_document_idx ON public.patients USING gin(fts_document);
```

---

## Phase II: Global Search State & Utilities (AI Task)

**Goal:** Set up the client-side infrastructure for managing and using the search query.

**AI Assistant's Responsibilities (after you complete Phase I):**

1.  **Create Zustand Store:**

    - File: `store/useSearchStore.ts`
    - Content:

      ```typescript
      import { create } from "zustand";

      interface SearchState {
        searchQuery: string;
        setSearchQuery: (query: string) => void;
        clearSearchQuery: () => void;
      }

      export const useSearchStore = create<SearchState>((set) => ({
        searchQuery: "",
        setSearchQuery: (query) => set({ searchQuery: query }),
        clearSearchQuery: () => set({ searchQuery: "" }),
      }));
      ```

2.  **Create Debounce Hook:**

    - File: `hooks/useDebounce.ts`
    - Content:

      ```typescript
      import { useState, useEffect } from "react";

      export function useDebounce<T>(value: T, delay: number): T {
        const [debouncedValue, setDebouncedValue] = useState<T>(value);

        useEffect(() => {
          const handler = setTimeout(() => {
            setDebouncedValue(value);
          }, delay);

          return () => {
            clearTimeout(handler);
          };
        }, [value, delay]);

        return debouncedValue;
      }
      ```

3.  **Integrate with Tabs Layout:**
    - File: `app/(protected)/(tabs)/_layout.tsx`
    - Modify `TabsLayout` to:
      - Use `useSearchStore` to get `searchQuery` and `setSearchQuery`.
      - Pass these to the `SearchHeader` component. The `searchQuery` will be displayed directly. `setSearchQuery` will update the store on every keystroke.
      - (Debouncing will be applied within individual data-fetching hooks before using the query for API calls).

---

## Phase III: Implement Search in Tab Screens (AI Task)

**Goal:** Modify each relevant tab screen's data fetching logic to use the global, debounced search query with the new `fts_document` column.

**AI Assistant's Responsibilities (for each relevant tab/entity):**

1.  **Locate Data Fetching Logic:** Identify the Tanstack Query hook (e.g., `usePrescriptions`, `useLabResults`, etc.) responsible for fetching data for the current tab.
2.  **Subscribe to Search Store:** The hook will import and use `useSearchStore` to get the `searchQuery`.
3.  **Debounce Search Query:** Apply the `useDebounce` hook (e.g., with a delay of 300-500ms) to the `searchQuery` from the store.
4.  **Update Tanstack Query Key:** The `queryKey` for the Tanstack Query hook must include the _debounced_ search query to ensure correct caching and re-fetching (e.g., `['entityName', { params }, debouncedSearchQuery]`).
5.  **Modify Supabase Query:**
    - The query function (passed to `useQuery` or similar) will use the Supabase client.
    - If the debounced search query is not empty, it will add a `.textSearch('fts_document', \`'${searchQueryEscaped}'\`, { config: 'english', type: 'websearch' })` filter to the Supabase query builder. (`searchQueryEscaped`means the search term might need simple escaping for single quotes if not handled by`websearch`type, or formatting for`websearch_to_tsquery`syntax like joining terms with`&`or`|`). The `type: 'websearch'` is often helpful as it allows more natural language queries.
    - If the debounced search query is empty, no text search filter will be applied.

**Order of Implementation for Tabs (example):**

1.  Prescriptions (`index.tsx`)
2.  Lab Results (`lab-results.tsx`)
3.  User's Medications (`(meds-tabs)/meds.tsx`)
4.  All Medications (`(meds-tabs)/all-meds.tsx`)
5.  User's Doctors (`(doctors-tabs)/doctors.tsx`)
6.  All Doctors (`(doctors-tabs)/all-doctors.tsx`)
7.  Patients (`(doctors-tabs)/patients.tsx`)

_(Notifications tab will be excluded from search)._

---

## Phase IV: Testing and Refinement (Collaborative)

**Goal:** Ensure the search functionality works correctly across all tabs and handles various scenarios.

**Responsibilities:**

- **User & AI:** Test search functionality thoroughly on each tab.
- Verify edge cases (empty search, special characters, no results).
- Profile performance if needed and discuss further optimizations.
