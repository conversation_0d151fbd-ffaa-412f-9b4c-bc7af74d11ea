# AI Chatbot System Refactor Plan

## 📊 **PROGRESS TRACKER**

### ✅ Phase 1: Cleanup Current Implementation

- [x] Delete buggy hooks (useLabResultAI.ts)
- [x] Delete buggy UI components (lab-result-ai-chat.tsx, new-chat.tsx, components/chat/)
- [x] Delete old edge functions (chat-lab-result, chat-medicine, chat-general)

### ✅ Phase 2: Core Infrastructure

- [x] Create database migration
- [x] Create medication analysis edge function
- [x] Create unified streaming chat function
- [x] Deploy edge functions

### ✅ Phase 3: Clone UI from Expo AI Chatbot Lite

- [x] Copy base components from example
- [x] Create main chat screen
- [x] Create enhanced drawer layout
- [x] Update tab integration

### ✅ Phase 4: Integration & Testing

- [x] Create unified useAIChat hook
- [x] Update lab result cards
- [x] Update medication cards
- [x] Test all functionality

---

## 🎯 Goal

Create a unified AI chatbot system inspired by Expo AI Chatbot Lite with:

- General AI chat
- **Lab results AI analysis + chat**
- **Medication AI analysis + chat**
- Full text streaming (SSE)
- Unified drawer navigation
- Chat history management
- Tab integration for creating specialized chats
- **Clone UI from Expo AI Chatbot Lite example**

## 🗑️ Phase 1: Cleanup Current Implementation

### Files to Delete

```
hooks/useLabResultAI.ts                          # Buggy implementation
app/(protected)/(ai-chat)/lab-result-ai-chat.tsx # Buggy UI
components/chat/                                 # DELETE - clone from example instead
app/(protected)/(ai-chat)/new-chat.tsx          # Replace with proper implementation
```

### Edge Functions to Keep/Modify

```
supabase/functions/analyze-lab-result/   # Keep but modify for new system
supabase/functions/chat-lab-result/      # DELETE - replace with unified function
supabase/functions/chat-medicine/        # DELETE - replace with unified function
supabase/functions/chat-general/         # DELETE - replace with unified function
supabase/functions/image-ocr-openai/     # Keep - still needed for OCR
```

### Edge Functions to Create

```
supabase/functions/analyze-medication/   # NEW - Analyze medication interactions, side effects, etc.
supabase/functions/chat-universal/       # NEW - Unified streaming chat function
```

## 🏗️ Phase 2: Core Infrastructure

### 1. Unified Chat Database Schema

```sql
-- Universal chat sessions table
CREATE TABLE ai_chat_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id text NOT NULL,
  chat_type text NOT NULL CHECK (chat_type IN ('general', 'lab_result', 'medication')),
  title text,
  context_id uuid, -- References lab_result.id or medication.id
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Universal chat messages table
CREATE TABLE ai_chat_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id uuid REFERENCES ai_chat_sessions(id) ON DELETE CASCADE,
  role text NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content text NOT NULL,
  metadata jsonb, -- For attachments, analysis refs, etc.
  created_at timestamp DEFAULT now()
);

-- Medication analyses table (NEW - similar to lab_result_analyses)
CREATE TABLE medication_analyses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  medication_id uuid REFERENCES medications(id) ON DELETE CASCADE,
  ai_analysis jsonb NOT NULL, -- Analysis results from OpenAI
  analysis_type text DEFAULT 'general', -- 'interaction', 'side_effects', 'general'
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_ai_chat_sessions_user_id ON ai_chat_sessions(user_id);
CREATE INDEX idx_ai_chat_sessions_type ON ai_chat_sessions(chat_type);
CREATE INDEX idx_ai_chat_messages_session_id ON ai_chat_messages(session_id);
CREATE INDEX idx_medication_analyses_medication_id ON medication_analyses(medication_id);
```

### 2. Unified Chat Hook

```typescript
// hooks/useAIChat.ts
export interface ChatSession {
  id: string;
  chatType: "general" | "lab_result" | "medication";
  title: string;
  contextId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  sessionId: string;
  role: "user" | "assistant" | "system";
  content: string;
  metadata?: any;
  createdAt: Date;
}

export interface MedicationAnalysis {
  id: string;
  medicationId: string;
  aiAnalysis: {
    summary: string;
    interactions: string[];
    sideEffects: string[];
    warnings: string[];
    recommendations: string[];
  };
  analysisType: string;
  createdAt: Date;
}

export function useAIChat() {
  // Session management
  const createSession = (type: ChatSession["chatType"], contextId?: string) =>
    Promise<ChatSession>;
  const fetchSessions = (filters?: { type?: string; search?: string }) =>
    Promise<ChatSession[]>;
  const deleteSession = (sessionId: string) => Promise<void>;

  // Message management
  const fetchMessages = (sessionId: string) => Promise<ChatMessage[]>;
  const sendMessage = (sessionId: string, content: string) =>
    Promise<ReadableStream>;

  // Analysis functions
  const analyzeLabResult = (labResultId: string) => Promise<any>; // Keep existing
  const analyzeMedication = (medicationId: string) =>
    Promise<MedicationAnalysis>; // NEW

  return {
    createSession,
    fetchSessions,
    deleteSession,
    fetchMessages,
    sendMessage,
    analyzeLabResult,
    analyzeMedication, // NEW
  };
}
```

### 3. New Medication Analysis Edge Function

```typescript
// supabase/functions/analyze-medication/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { medicationId } = await req.json();

    const supabase = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Get medication details
    const { data: medication, error: medError } = await supabase
      .from("medications")
      .select("*")
      .eq("id", medicationId)
      .single();

    if (medError) throw new Error("Medication not found");

    // Check if analysis already exists
    const { data: existingAnalysis } = await supabase
      .from("medication_analyses")
      .select("*")
      .eq("medication_id", medicationId)
      .single();

    if (existingAnalysis) {
      return new Response(
        JSON.stringify({ analysis: existingAnalysis, cached: true }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Generate AI analysis
    const analysis = await analyzeMedicationWithAI(medication);

    // Save analysis
    const { data: savedAnalysis } = await supabase
      .from("medication_analyses")
      .insert({
        medication_id: medicationId,
        ai_analysis: analysis,
        analysis_type: "general",
      })
      .select()
      .single();

    return new Response(
      JSON.stringify({ analysis: savedAnalysis, cached: false }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});

async function analyzeMedicationWithAI(medication: any) {
  const openaiApiKey = Deno.env.get("OPENAI_API_KEY");
  if (!openaiApiKey) throw new Error("OpenAI API key not configured");

  const prompt = `Analyze this medication and provide detailed information:

Medication: ${medication.name}
Dosage: ${medication.dosage || "Not specified"}
Frequency: ${medication.frequency || "Not specified"}
Notes: ${medication.notes || "None"}

Please provide:
1. Summary of what this medication is used for
2. Common interactions with other medications
3. Side effects to watch for
4. Important warnings or precautions
5. General recommendations for use

Format as JSON with keys: summary, interactions, sideEffects, warnings, recommendations (all arrays except summary)`;

  const response = await fetch("https://api.openai.com/v1/chat/completions", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${openaiApiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a medical AI assistant. Provide accurate medication information and always remind users to consult healthcare professionals.",
        },
        { role: "user", content: prompt },
      ],
      max_tokens: 1000,
      temperature: 0.3,
    }),
  });

  const data = await response.json();
  const content = data.choices[0]?.message?.content;

  try {
    return JSON.parse(content);
  } catch {
    // Fallback if JSON parsing fails
    return {
      summary: content,
      interactions: [],
      sideEffects: [],
      warnings: ["Always consult your healthcare provider"],
      recommendations: ["Follow prescribed dosage", "Report any side effects"],
    };
  }
}
```

### 4. Unified Streaming Chat Function

```typescript
// supabase/functions/chat-universal/index.ts
serve(async (req) => {
  const { sessionId, message, chatType, contextId } = await req.json();

  // Get context based on chat type
  let systemPrompt = "";
  let contextData = null;

  switch (chatType) {
    case "lab_result":
      // Get lab result analysis
      const { data: labAnalysis } = await supabase
        .from("lab_result_analyses")
        .select("*")
        .eq("lab_result_id", contextId)
        .single();
      contextData = labAnalysis;
      systemPrompt = `You are a medical AI assistant helping users understand their lab results.
Lab Analysis Context: ${JSON.stringify(labAnalysis?.ai_analysis)}
Guidelines: Answer questions about lab results, always remind users to consult healthcare professionals.`;
      break;

    case "medication":
      // Get medication analysis
      const { data: medAnalysis } = await supabase
        .from("medication_analyses")
        .select("*")
        .eq("medication_id", contextId)
        .single();
      contextData = medAnalysis;
      systemPrompt = `You are a medical AI assistant helping users understand their medications.
Medication Analysis Context: ${JSON.stringify(medAnalysis?.ai_analysis)}
Guidelines: Answer questions about medications, interactions, side effects. Always remind users to consult healthcare professionals.`;
      break;

    case "general":
      systemPrompt = `You are a helpful medical AI assistant. Provide general health information and always remind users to consult healthcare professionals for medical advice.`;
      break;
  }

  // Get chat history
  const { data: history } = await supabase
    .from("ai_chat_messages")
    .select("role, content")
    .eq("session_id", sessionId)
    .order("created_at", { ascending: true });

  // Create OpenAI streaming request
  const stream = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: systemPrompt },
      ...(history || []),
      { role: "user", content: message },
    ],
    stream: true,
    max_tokens: 1000,
    temperature: 0.3,
  });

  // Return Server-Sent Events stream
  return new Response(
    new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let fullResponse = "";

        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || "";
            if (content) {
              fullResponse += content;
              controller.enqueue(
                encoder.encode(
                  `data: ${JSON.stringify({ content, done: false })}\n\n`
                )
              );
            }
          }

          // Save messages to database
          await supabase.from("ai_chat_messages").insert([
            { session_id: sessionId, role: "user", content: message },
            { session_id: sessionId, role: "assistant", content: fullResponse },
          ]);

          controller.enqueue(
            encoder.encode(
              `data: ${JSON.stringify({ content: "", done: true })}\n\n`
            )
          );
        } finally {
          controller.close();
        }
      },
    }),
    {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        ...corsHeaders,
      },
    }
  );
});
```

## 🎨 Phase 3: Clone UI from Expo AI Chatbot Lite

### 1. Copy Base Components from Example

```bash
# From the Expo AI Chatbot Lite example, copy these components:
components/
├── ui/
│   ├── chat-input.tsx           # Copy from example
│   ├── chat-text-input.tsx      # Copy from example
│   ├── markdown.tsx             # Copy from example
│   ├── avatar.tsx               # Copy from example
│   ├── button.tsx               # Copy from example
│   ├── card.tsx                 # Copy from example
│   └── text.tsx                 # Copy from example
├── chat-interface.tsx           # Copy and adapt from example
├── suggested-actions.tsx        # Copy from example
├── welcome-message.tsx          # Copy from example
└── lottie-loader.tsx           # Copy from example (for typing indicator)
```

### 2. Main Chat Screen (Based on Example)

```typescript
// app/(protected)/(ai-chat)/[sessionId].tsx
import React, { useState, useEffect, useRef } from "react";
import { View, FlatList } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ChatInterface } from "@/components/chat-interface";
import { WelcomeMessage } from "@/components/welcome-message";
import { useAIChat, type ChatMessage } from "@/hooks/useAIChat";

export default function ChatScreen() {
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const { fetchMessages, sendMessage } = useAIChat();

  // Load messages on mount
  useEffect(() => {
    if (sessionId) {
      loadMessages();
    }
  }, [sessionId]);

  const loadMessages = async () => {
    try {
      const loadedMessages = await fetchMessages(sessionId);
      setMessages(loadedMessages);
    } catch (error) {
      console.error("Failed to load messages:", error);
    }
  };

  const handleSendMessage = async (content: string) => {
    // Add user message immediately
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sessionId,
      role: "user",
      content,
      createdAt: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);

    setIsStreaming(true);

    try {
      // Start streaming AI response
      const stream = await sendMessage(sessionId, content);
      const reader = stream.getReader();
      const decoder = new TextDecoder();

      let assistantMessage: ChatMessage = {
        id: `ai-${Date.now()}`,
        sessionId,
        role: "assistant",
        content: "",
        createdAt: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);

      // Read stream
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = JSON.parse(line.slice(6));
            if (!data.done) {
              assistantMessage.content += data.content;
              setMessages((prev) =>
                prev.map((msg) =>
                  msg.id === assistantMessage.id
                    ? { ...msg, content: assistantMessage.content }
                    : msg
                )
              );
            }
          }
        }
      }
    } catch (error) {
      console.error("Chat error:", error);
      // Handle error - maybe show error message
    } finally {
      setIsStreaming(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <Stack.Screen
        options={{
          title: "AI Chat",
          headerBackTitle: "Back",
        }}
      />

      <ChatInterface
        messages={messages}
        onSendMessage={handleSendMessage}
        isLoading={isStreaming}
        welcomeComponent={
          messages.length === 0 ? <WelcomeMessage /> : undefined
        }
      />
    </SafeAreaView>
  );
}
```

### 3. Enhanced Drawer (Based on Example Patterns)

```typescript
// app/(protected)/(ai-chat)/_layout.tsx
import React, { useState, useEffect } from "react";
import { View, FlatList, Text, Pressable } from "react-native";
import { Drawer } from "expo-router/drawer";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import { useAIChat, type ChatSession } from "@/hooks/useAIChat";

export const CustomDrawerContent = (props: any) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const { fetchSessions, createSession, deleteSession } = useAIChat();

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      const allSessions = await fetchSessions();
      setSessions(allSessions);
    } catch (error) {
      console.error("Failed to load sessions:", error);
    }
  };

  const filteredSessions = sessions.filter((session) => {
    const matchesSearch = session.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesType = filterType === "all" || session.chatType === filterType;
    return matchesSearch && matchesType;
  });

  const handleCreateNewChat = async (type: ChatSession["chatType"]) => {
    try {
      const newSession = await createSession(type);
      router.push(`/(ai-chat)/${newSession.id}`);
    } catch (error) {
      console.error("Failed to create session:", error);
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    try {
      await deleteSession(sessionId);
      setSessions((prev) => prev.filter((s) => s.id !== sessionId));
    } catch (error) {
      console.error("Failed to delete session:", error);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      {/* Search Header */}
      <View className="p-4 border-b border-border">
        <Text className="text-lg font-semibold mb-3">AI Chat</Text>

        {/* New Chat Buttons */}
        <View className="space-y-2">
          <Pressable
            className="flex-row items-center p-3 bg-primary/10 rounded-lg"
            onPress={() => handleCreateNewChat("general")}
          >
            <Ionicons
              name="chatbubble-outline"
              size={20}
              className="text-primary mr-3"
            />
            <Text className="text-primary font-medium">New General Chat</Text>
          </Pressable>
        </View>

        {/* Filter Tabs */}
        <View className="flex-row mt-4 space-x-2">
          {[
            { key: "all", label: "All" },
            { key: "general", label: "General" },
            { key: "lab_result", label: "Lab Results" },
            { key: "medication", label: "Medications" },
          ].map((filter) => (
            <Pressable
              key={filter.key}
              className={`px-3 py-1 rounded-full ${
                filterType === filter.key ? "bg-primary" : "bg-muted"
              }`}
              onPress={() => setFilterType(filter.key)}
            >
              <Text
                className={`text-sm ${
                  filterType === filter.key
                    ? "text-primary-foreground"
                    : "text-muted-foreground"
                }`}
              >
                {filter.label}
              </Text>
            </Pressable>
          ))}
        </View>
      </View>

      {/* Sessions List */}
      <DrawerContentScrollView
        {...props}
        contentContainerStyle={{ paddingTop: 0 }}
      >
        <FlatList
          data={filteredSessions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ChatSessionItem
              session={item}
              onPress={() => router.push(`/(ai-chat)/${item.id}`)}
              onDelete={() => handleDeleteSession(item.id)}
            />
          )}
          ListEmptyComponent={
            <View className="p-4 items-center">
              <Text className="text-muted-foreground">No chats yet</Text>
            </View>
          }
        />
      </DrawerContentScrollView>
    </SafeAreaView>
  );
};

const ChatSessionItem = ({
  session,
  onPress,
  onDelete,
}: {
  session: ChatSession;
  onPress: () => void;
  onDelete: () => void;
}) => (
  <Pressable
    className="flex-row items-center p-4 border-b border-border/50"
    onPress={onPress}
  >
    <View className="mr-3">
      {session.chatType === "lab_result" && (
        <Ionicons
          name="document-text-outline"
          size={20}
          className="text-blue-500"
        />
      )}
      {session.chatType === "medication" && (
        <Ionicons name="medical-outline" size={20} className="text-green-500" />
      )}
      {session.chatType === "general" && (
        <Ionicons
          name="chatbubble-outline"
          size={20}
          className="text-purple-500"
        />
      )}
    </View>

    <View className="flex-1">
      <Text className="font-medium" numberOfLines={1}>
        {session.title}
      </Text>
      <Text className="text-sm text-muted-foreground" numberOfLines={1}>
        {session.chatType.replace("_", " ")} •{" "}
        {session.createdAt.toLocaleDateString()}
      </Text>
    </View>

    <Pressable onPress={onDelete} className="p-2">
      <Ionicons name="trash-outline" size={16} className="text-destructive" />
    </Pressable>
  </Pressable>
);

const DrawerLayout = () => {
  return (
    <Drawer
      drawerContent={CustomDrawerContent}
      screenOptions={{
        headerShown: false,
        drawerStyle: { width: "85%" },
      }}
    >
      <Drawer.Screen
        name="[sessionId]"
        options={{
          drawerLabel: () => null, // Hide from drawer menu
        }}
      />
    </Drawer>
  );
};

export default DrawerLayout;
```

### 4. Enhanced Tab Integration

```typescript
// components/labresults/LabResultCard.tsx
import { useAIChat } from "@/hooks/useAIChat";

const LabResultCard = ({ result }: { result: LabResult }) => {
  const { createSession, analyzeLabResult } = useAIChat();

  const handleAnalyzeWithAI = async () => {
    try {
      // First analyze the lab result
      await analyzeLabResult(result.id);

      // Then create a chat session
      const session = await createSession("lab_result", result.id);
      router.push(`/(ai-chat)/${session.id}`);
    } catch (error) {
      console.error("Failed to analyze lab result:", error);
      Alert.alert("Error", "Failed to analyze lab result");
    }
  };

  return (
    <Card>
      {/* Existing content */}

      {result.captured_pdf_path && (
        <Button
          title="Analyze with AI"
          onPress={handleAnalyzeWithAI}
          icon={<Ionicons name="sparkles" size={16} />}
          className="mt-3"
        />
      )}
    </Card>
  );
};

// components/medications/MedicationCard.tsx
const MedicationCard = ({ medication }: { medication: Medication }) => {
  const { createSession, analyzeMedication } = useAIChat();

  const handleAnalyzeWithAI = async () => {
    try {
      // First analyze the medication
      await analyzeMedication(medication.id);

      // Then create a chat session
      const session = await createSession("medication", medication.id);
      router.push(`/(ai-chat)/${session.id}`);
    } catch (error) {
      console.error("Failed to analyze medication:", error);
      Alert.alert("Error", "Failed to analyze medication");
    }
  };

  return (
    <Card>
      {/* Existing content */}

      <Button
        title="Analyze with AI"
        onPress={handleAnalyzeWithAI}
        icon={<Ionicons name="medical" size={16} />}
        className="mt-3"
      />
    </Card>
  );
};
```

## 🔧 Updated Implementation Steps

### Step 1: Delete Current Buggy Implementation

```bash
# Remove all buggy current implementations
rm hooks/useLabResultAI.ts
rm app/(protected)/(ai-chat)/lab-result-ai-chat.tsx
rm app/(protected)/(ai-chat)/new-chat.tsx
rm -rf components/chat/  # Delete buggy chat components

# Remove old edge functions
rm -rf supabase/functions/chat-lab-result
rm -rf supabase/functions/chat-medicine
rm -rf supabase/functions/chat-general
```

### Step 2: Database Migration

```bash
# Create new migration
supabase migration new unified_ai_chat_system
# Add the SQL schema above including medication_analyses table
supabase db push
```

### Step 3: Copy UI from Expo AI Chatbot Lite

```bash
# Copy these files from the example to your components folder:
# - chat-interface.tsx
# - suggested-actions.tsx
# - welcome-message.tsx
# - lottie-loader.tsx
# - ui/chat-input.tsx
# - ui/chat-text-input.tsx
# - ui/markdown.tsx
# - ui/avatar.tsx (if needed)
```

### Step 4: Create New Edge Functions

```bash
# Create medication analysis function
mkdir supabase/functions/analyze-medication
# Add the code above

# Create unified chat function
mkdir supabase/functions/chat-universal
# Add the code above

# Deploy
supabase functions deploy analyze-medication
supabase functions deploy chat-universal
```

### Step 5: Implement New Components

```bash
# Create new useAIChat hook with medication analysis
# Create new chat screen with cloned UI
# Update drawer layout with proper session management
# Update lab result and medication cards with AI buttons
```

## 📱 Updated Expected User Experience

1. **Lab Results**: User taps "Analyze with AI" → **Analyzes lab result + creates chat** → Streams initial analysis → User can ask questions
2. **Medications**: User taps "Analyze with AI" → **Analyzes medication + creates chat** → Streams interaction/side effect info → User can ask questions
3. **General Chat**: User creates general health chat → Streams responses
4. **Beautiful UI**: Clean interface cloned from Expo AI Chatbot Lite example
5. **Proper Streaming**: Real SSE streaming like ChatGPT
6. **Smart Drawer**: Search, filter, and manage all chat types

This updated plan now includes **both lab result AND medication analysis** plus clones the proven UI from the example instead of reusing buggy components!

## 🎉 **IMPLEMENTATION COMPLETE!**

All phases of the AI Chatbot System refactor have been successfully completed:

### ✅ **Fixed Issues**

1. **Streaming Error Fixed** - **Used Expo's `expo/fetch` API** which provides proper streaming support for React Native
2. **Automatic Analysis Delivery** - Analysis is now automatically sent as the first message in lab result and medication chats
3. **Proper Field Mapping** - Updated medication analysis to use correct `user_meds` table fields
4. **Enhanced UI** - Beautiful drawer with search, filtering, and session management

### 🔧 **Technical Solution: Expo Fetch API**

**Problem:** React Native's built-in fetch doesn't support `ReadableStream` properly - `response.body` is often null.

**Solution:** Used Expo's streaming-enabled fetch API:

```typescript
import { fetch } from "expo/fetch"; // ✨ Streaming support!

const response = await fetch(url, {
  headers: { Accept: "text/event-stream" },
});
const reader = response.body.getReader(); // ✅ Works!
```

**Benefits:**

- ✅ **Native streaming support** - No polyfills needed
- ✅ **WinterCG-compliant** - Follows web standards
- ✅ **Cross-platform** - Works on iOS, Android, and Web
- ✅ **Built into Expo** - No additional dependencies

### ✅ **Features Delivered**

- **Unified AI Chat System** supporting general, lab result, and medication chats
- **Real-time streaming** chat like ChatGPT with Server-Sent Events
- **Context-aware AI** that understands medical data and provides relevant analysis
- **Beautiful UI** cloned from Expo AI Chatbot Lite example
- **Smart drawer navigation** with search, filtering, and session management
- **Seamless integration** with existing lab results and medications tabs
- **Automatic analysis delivery** for specialized chats
- **Proper error handling** and user feedback

### 🚀 **Ready for Production**

The system is now fully functional with:

- Streaming AI responses ✅
- Medical context analysis ✅
- Beautiful user interface ✅
- Database integration ✅
- Authentication ✅
- Error handling ✅
