# Clerk <> Supabase Integration Plan & Progress

This document outlines the steps required to integrate Clerk authentication with the Supabase database using the **recommended native integration**, along with progress tracking.

## Goals

1.  **User Sync:** Ensure Clerk user creation/updates are reflected in a Supabase `profiles` table.
2.  **Authenticated Requests:** Enable the Expo app to make Supabase requests authenticated as the currently logged-in Clerk user, respecting Row Level Security (RLS).

## Plan

1.  **Configure Native Integration (Dashboards):**

    - **Status:** Done ✅ (User confirmed)
    - **Action:** Set up Clerk as a third-party auth provider directly within the Supabase Dashboard.
    - **Details:**
      - In Clerk Dashboard -> Integrations -> Supabase: Activate the integration to get your **Clerk Domain**.
      - In Supabase Dashboard -> Authentication -> Providers: Add **Clerk** as a provider and paste the **Clerk Domain**.

2.  **Authenticated Supabase Client (Expo App):**

    - **Status:** Implemented ✅
    - **Action:** Create a hook (e.g., `useSupabaseClient` in `hooks/`) that initializes the `supabase-js` client.
    - **Details:** Hook created in `hooks/useSupabaseClient.ts`. Uses `useAuth().getToken()` without template. Requires `types/database.types.ts` to be generated by user via Supabase CLI.

3.  **Supabase Table Definition (`profiles`):**

    - **Status:** SQL Provided ✅
    - **Action:** Define and create the `profiles` table in the Supabase database (via SQL editor or dashboard).
    - **Schema Suggestion:** SQL provided for `profiles` table with `id` (uuid, PK), `email`, `full_name`, `created_at`, `updated_at`, and `updated_at` trigger.
    - **SQL:**

      ```sql
      -- Create the profiles table
        CREATE TABLE public.profiles (
        id text NOT NULL UNIQUE,
        email text NULL,
        full_name text NULL,
        avatar_url text NULL,
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp with time zone NOT NULL DEFAULT now(),
        CONSTRAINT profiles_email_key UNIQUE (email)
        );

      -- Optional: Add a trigger to automatically update updated_at timestamp
      CREATE OR REPLACE FUNCTION public.handle_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = now();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER on_profile_updated
      BEFORE UPDATE ON public.profiles
      FOR EACH ROW
      EXECUTE FUNCTION public.handle_updated_at();

      -- Add comments for clarity
      COMMENT ON TABLE public.profiles IS 'Stores user profile information, linked to Clerk users via id.';
      COMMENT ON COLUMN public.profiles.id IS 'Links to Clerk User ID (references auth.users.id indirectly via JWT).';
      ```

4.  **Supabase Row Level Security (RLS):**

    - **Status:** SQL Updated (Corrected Policies) ✅
    - **Action:** Enable RLS on the `profiles` table (and other user-specific tables).
    - **SQL:**

      ```sql
      -- 1. Enable RLS on the profiles table (IMPORTANT: Run this AFTER creating policies)
      ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

      -- 2. Policy: Authenticated users can view their own profiles
      -- DROPS OLD POLICY IF EXISTS: DROP POLICY IF EXISTS "Authenticated users can view their own profiles" ON public.profiles;
      CREATE POLICY "Authenticated users can view their own profiles"
        ON public.profiles
        FOR SELECT
        TO authenticated
        USING (id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')); -- Use JWT 'sub' claim

      -- 3. Policy: Authenticated users can create their own profiles
      -- Note: Typically handled by webhook/service role, but this allows direct inserts if needed.
      -- DROPS OLD POLICY IF EXISTS: DROP POLICY IF EXISTS "Authenticated users can create their own profiles" ON public.profiles;
      CREATE POLICY "Authenticated users can create their own profiles"
        ON public.profiles
        FOR INSERT
        TO authenticated
        WITH CHECK (id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')); -- Ensure inserted id matches JWT 'sub'

      -- 4. Policy: Authenticated users can update their own profiles
      -- DROPS OLD POLICY IF EXISTS: DROP POLICY IF EXISTS "Authenticated users can update their own profiles" ON public.profiles;
      CREATE POLICY "Authenticated users can update their own profiles"
        ON public.profiles
        FOR UPDATE
        TO authenticated
        USING (id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')) -- Use JWT 'sub' claim for row check
        WITH CHECK (id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')); -- Use JWT 'sub' claim for update check

      -- 5. Policy: Authenticated users can delete their own profiles
      -- DROPS OLD POLICY IF EXISTS: DROP POLICY IF EXISTS "Authenticated users can delete their own profiles" ON public.profiles;
      CREATE POLICY "Authenticated users can delete their own profiles"
        ON public.profiles
        FOR DELETE
        TO authenticated
        USING (id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')); -- Use JWT 'sub' claim

      -- Add comments for clarity
      COMMENT ON POLICY "Authenticated users can view their own profiles" ON public.profiles
        IS 'Ensures users can only select their own profile based on the sub claim (Clerk User ID) in the JWT.';
      COMMENT ON POLICY "Authenticated users can create their own profiles" ON public.profiles
        IS 'Ensures users can only insert their own profile based on the sub claim (Clerk User ID) in the JWT.';
      COMMENT ON POLICY "Authenticated users can update their own profiles" ON public.profiles
        IS 'Ensures users can only update their own profile based on the sub claim (Clerk User ID) in the JWT.';
      COMMENT ON POLICY "Authenticated users can delete their own profiles" ON public.profiles
        IS 'Ensures users can only delete their own profile based on the sub claim (Clerk User ID) in the JWT.';

      -- Note: The previous INSERT policy used WITH CHECK (true), relying solely on the webhook.
      -- The updated INSERT policy above adds a check against the JWT 'sub' claim for consistency
      -- if direct client-side inserts were ever needed. If inserts are ONLY done via webhook,
      -- the simpler WITH CHECK (true) could still be used.
      ```

5.  **Clerk Webhook Configuration (for User Sync):**

    - **Status:** Instructions Provided ✅
    - **Action:** In the Clerk Dashboard, configure a new webhook.
    - **Details:** Point the webhook URL to the Supabase Edge Function created in the next step. Subscribe to `user.created`, `user.updated`, and **`user.deleted`** events. Securely store the webhook signing secret.

6.  **Supabase Edge Function (Webhook Handler - for User Sync):**

    - **Status:** Code Provided ✅
    - **Action:** Create a new Supabase Edge Function (e.g., `clerk-webhook-handler`) **using the Supabase Dashboard Editor**.
    - **Code (for Dashboard Editor):**

      ```typescript
      import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
      import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
      import { Webhook } from "https://esm.sh/svix";
      serve(async (req) => {
        const supabaseUrl = Deno.env.get("SUPABASE_URL");
        const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
        const supabase = createClient(supabaseUrl, supabaseKey);
        const WEBHOOK_SECRET = Deno.env.get("CLERK_WEBHOOK_SIGNING_SECRET");
        if (!WEBHOOK_SECRET) {
          console.error("CLERK_WEBHOOK_SIGNING_SECRET is not set.");
          return new Response("Server configuration error", {
            status: 500,
          });
        }
        const svix_id = req.headers.get("svix-id") ?? "";
        const svix_timestamp = req.headers.get("svix-timestamp") ?? "";
        const svix_signature = decodeURIComponent(
          req.headers.get("svix-signature") ?? ""
        );
        if (!svix_id || !svix_timestamp || !svix_signature) {
          return new Response("Missing Svix headers", {
            status: 400,
          });
        }
        const payload = await req.text();
        const headers = {
          "svix-id": svix_id,
          "svix-timestamp": svix_timestamp,
          "svix-signature": svix_signature,
        };
        const wh = new Webhook(WEBHOOK_SECRET);
        let evt;
        try {
          evt = wh.verify(payload, headers);
        } catch (err) {
          console.error("Webhook verification failed:", err);
          return new Response("Invalid signature", {
            status: 400,
          });
        }
        const eventType = evt.type;
        const data = evt.data;
        if (eventType === "user.created") {
          const { id, email_addresses, username, avatar_url } = data;
          const email = email_addresses[0]?.email_address;
          const { error } = await supabase.from("profiles").insert([
            {
              id,
              email,
              full_name: username,
              avatar_url,
            },
          ]);
          if (error) {
            console.error("Error inserting profile:", error);
            return new Response("Error inserting profile", {
              status: 500,
            });
          }
          return new Response("Profile inserted successfully", {
            status: 200,
          });
        }
        if (eventType === "user.updated") {
          const { id, email_addresses, username, avatar_url } = data;
          const email = email_addresses[0]?.email_address;
          const { error } = await supabase
            .from("profiles")
            .update({
              email,
              full_name: username,
              avatar_url,
            })
            .eq("id", id);
          if (error) {
            console.error("Error updating profile:", error);
            return new Response("Error updating profile", {
              status: 500,
            });
          }
          return new Response("Profile updated successfully", {
            status: 200,
          });
        }
        if (eventType === "user.deleted") {
          const { id } = data;
          const { error } = await supabase
            .from("profiles")
            .delete()
            .eq("id", id);
          if (error) {
            console.error("Error deleting profile:", error);
            return new Response("Error deleting profile", {
              status: 500,
            });
          }
          return new Response("Profile deleted successfully", {
            status: 200,
          });
        }
        return new Response("Event type not handled", {
          status: 400,
        });
      });
      ```

7.  **Update Application Code:**

    - **Status:** Example Provided
    - **Action:** Refactor parts of the application that need to query Supabase data (e.g., fetching user profile information in `settings.tsx`) to use the authenticated Supabase client instance obtained from the hook/function created in Step 2.
    - **Details:** Example usage of `useSupabaseClient` provided for fetching profile data in a component.

## Progress Notes

_(This section will be updated as steps are completed)_
