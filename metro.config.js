const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

// Ensure resolver object exists
config.resolver = config.resolver || {};

// Workaround for Supabase/Expo bundling issue with 'ws' dependency
// See: https://github.com/supabase/supabase-js/issues/1258
// Add 'browser' condition to help Metro resolve correct exports
config.resolver.unstable_conditionNames = ['browser', 'require', 'default'];
// Disable package exports as another potential part of the workaround
config.resolver.unstable_enablePackageExports = false;

module.exports = withNativeWind(config, { input: './global.css' });