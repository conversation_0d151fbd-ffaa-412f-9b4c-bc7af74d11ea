import { z } from 'zod';

// Updated phone number regex to allow spaces
const phoneRegex = /^[+]?[(]?[0-9\s]{3,}[)]?[-\s.]?[0-9\s]{3,}[-\s.]?[0-9\s]{4,6}$/im;

// Form schema for lab results
export const labResultFormSchema = z.object({
  lab_name: z.string().min(1, 'Lab name is required').trim(),
  password: z.string().min(1, 'Password is required'), // Consider if this needs more specific validation
  patient_id: z.string().min(1, 'Patient ID is required').trim(), // ID from the document itself
  patient_reference_id: z.string().optional(), // FK to patients table (optional)
  patient_name: z.string().trim().optional().nullable().or(z.literal('')), // Name from document (optional)
  result_date: z.string().optional().nullable(), // Keep as string, DatePicker handles format
  website: z.string().optional().nullable().or(z.literal('')), // Validate as URL
  phone_number1: z.string().regex(phoneRegex, 'Invalid phone number format').max(30, 'Phone number too long').optional().nullable().or(z.literal('')), // Added .max(30)
  phone_number2: z.string().regex(phoneRegex, 'Invalid phone number format').max(30, 'Phone number too long').optional().nullable().or(z.literal('')), // Added .max(30)
  phone_number3: z.string().regex(phoneRegex, 'Invalid phone number format').max(30, 'Phone number too long').optional().nullable().or(z.literal('')), // Added .max(30)
});

// Form data type derived from the schema
export type LabResultFormData = z.infer<typeof labResultFormSchema>; 