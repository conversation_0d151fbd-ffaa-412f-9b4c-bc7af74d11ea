import { z } from 'zod';
import { Constants } from '@/types/database.types'; // Import constants for enums

// Form schema for user medicines
export const medicineFormSchema = z.object({
  name: z.string().min(1, 'Medicine name is required').trim(),
  description: z.string().optional().nullable().or(z.literal('')),

  dosage_amount: z.coerce.number().positive('Dosage must be positive').optional().nullable(),
  dosage_unit: z.enum(Constants.public.Enums.dosage_unit_enum).optional().nullable(),

  frequency_amount: z.coerce.number().positive('Frequency must be positive').optional().nullable(),
  frequency_unit: z.enum(Constants.public.Enums.frequency_unit_enum).optional().nullable(),

  duration_amount: z.coerce.number().positive('Duration must be positive').optional().nullable(),
  duration_unit: z.enum(Constants.public.Enums.duration_unit_enum).optional().nullable(),

  expiration_date: z.string().optional().nullable(), // Keep as string, DatePicker handles format
  opened_on_date: z.string().optional().nullable(), // Keep as string, DatePicker handles format

  patient_id: z.string().uuid('Invalid patient ID').optional().nullable(), // FK to patients table
  prescription_id: z.string().uuid('Invalid prescription ID').optional().nullable(), // FK to prescriptions table

  notes: z.string().optional().nullable().or(z.literal('')),
  price: z.coerce.number().nonnegative('Price cannot be negative').optional().nullable(),

  // Fields related to linking with allmeds table (optional)
  med_id: z.string().uuid('Invalid global medicine ID').optional().nullable(), // FK to allmeds table
  is_custom: z.boolean().optional().nullable(),

  // Image fields handled separately, not typically part of the form schema directly
  // image_path: z.string().optional().nullable(),
  // image_bucket_id: z.string().optional().nullable(),
});

// Form data type derived from the schema
export type MedicineFormData = z.infer<typeof medicineFormSchema>; 