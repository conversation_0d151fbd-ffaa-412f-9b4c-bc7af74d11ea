import { z } from "zod";

// Based on types/database.types.ts -> Tables<"prescriptions">

export const prescriptionSchema = z.object({
  id: z.string().uuid().optional(), // Optional for creation
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
  user_id: z.string().uuid().optional(), // Should be set automatically based on logged-in user
  name: z.string().min(1, "Prescription name cannot be empty."),
  prescription_date: z.string().datetime(), // Or z.date() if handling conversion
  notes: z.string().nullable().optional(),
  patient_id: z.string().uuid().nullable().optional(), // Foreign key to patients
  doctor_id: z.string().uuid().nullable().optional(), // Foreign key to user_doctors
  // Image related fields - handled separately during upload?
  front_image_path: z.string().nullable().optional(),
  back_image_path: z.string().nullable().optional(),
  image_bucket_id: z.string().nullable().optional(), // Likely constant like 'prescription_images'
  image_metadata: z
    .object({})
    .passthrough()
    .nullable()
    .optional(), // Assuming JSON object for metadata
});

export const prescriptionInsertSchema = prescriptionSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
  user_id: true, // user_id will be added server-side or based on session
});

export const prescriptionUpdateSchema = prescriptionSchema
  .omit({
    id: true, // id is used for lookup, not update
    created_at: true,
    user_id: true, // Should not change owner
  })
  .partial(); // Allow partial updates

export type Prescription = z.infer<typeof prescriptionSchema>;
export type PrescriptionInsert = z.infer<typeof prescriptionInsertSchema>;
export type PrescriptionUpdate = z.infer<typeof prescriptionUpdateSchema>; 