import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

// Sign In Schema
export const signInSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

export type SignInFormData = z.infer<typeof signInSchema>;

export const signInResolver = zodResolver(signInSchema);

// Sign Up Schema
export const signUpSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters long' }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'], // path of error
});

export type SignUpFormData = z.infer<typeof signUpSchema>;

export const signUpResolver = zodResolver(signUpSchema);

// Forgot Password - Email Schema
export const forgotPasswordEmailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' })
});

export type ForgotPasswordEmailFormData = z.infer<typeof forgotPasswordEmailSchema>;

export const forgotPasswordEmailResolver = zodResolver(forgotPasswordEmailSchema);

// Forgot Password - Reset Schema
export const forgotPasswordResetSchema = z.object({
  code: z.string().min(6, { message: 'Verification code is required' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword']
});

export type ForgotPasswordResetFormData = z.infer<typeof forgotPasswordResetSchema>;

export const forgotPasswordResetResolver = zodResolver(forgotPasswordResetSchema);