import { z } from 'zod';
import { Constants } from '@/types/database.types'; // Import Constants

// Create a Zod enum from the database constants
const bloodTypeEnum = z.enum(Constants.public.Enums.blood_type_enum);

// Form schema for creating/updating patients
export const patientFormSchema = z.object({
  name: z.string().min(1, 'Name is required').trim(),
  age: z.preprocess(
    // Convert empty string/null/undefined to undefined, otherwise attempt to convert to number
    (val) => (val === '' || val === null || val === undefined ? undefined : Number(val)),
    // Age must be a positive integer or undefined (optional)
    z.number({
      invalid_type_error: 'Age must be a number',
    }).int('Age must be a whole number').positive('Age must be a positive number').optional()
  ),
  // Blood type must be one of the enum values or an empty string (for 'Unknown')
  bloodtype: z.union([
    bloodTypeEnum,
    z.literal(''), // Allow empty string for the 'Unknown' option
  ]).optional().nullable(), // Allow null/undefined overall
});

// Form data type derived from the schema
export type PatientFormData = z.infer<typeof patientFormSchema>; 