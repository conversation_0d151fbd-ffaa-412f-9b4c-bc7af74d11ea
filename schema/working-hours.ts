import { Schema, z } from 'zod';

/**
 * Days of the week enum
 */
export const DaysOfWeek = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday'
] as const;

export type DayOfWeek = typeof DaysOfWeek[number];

/**
 * Time slot schema for working hours
 */
export const TimeSlotSchema = z.object({
  start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format. Use HH:MM'),
  end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format. Use HH:MM'),
});

export type TimeSlot = z.infer<typeof TimeSlotSchema>;

/**
 * Working hours schema
 */
export const WorkingHoursSchema = z.record(
  z.enum(DaysOfWeek as unknown as [string, ...string[]]),
  z.array(TimeSlotSchema).optional().nullable()
);

export type WorkingHours = z.infer<typeof WorkingHoursSchema>;

export const defaultWorkingHours: WorkingHours = {
  monday: null,
  tuesday: null,
    wednesday: null,
    thursday: null,
    friday: null,
    saturday: null,
    sunday: null
  };