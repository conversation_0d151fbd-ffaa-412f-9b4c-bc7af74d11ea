import { z } from 'zod';
import { WorkingHoursSchema } from './working-hours'; // Import the correct schema directly

// Flexible phone number regex to support various domestic and international formats
// Allows digits, spaces, hyphens, parentheses, and + symbol with reasonable length limits
// Examples: (*************, +1 (*************, +359 88 123 4567, etc.
const phoneRegex = /^[\+]?[\d\s\-\(\)]{3,20}$/;

// Corresponds to createUserDoctorSchema in the hook
export const doctorFormSchema = z.object({
  name: z.string().min(1, 'Name is required').trim(),
  specialty: z.string().trim().optional().nullable().or(z.literal('')), // Allow empty string
  workplace: z.string().trim().optional().nullable().or(z.literal('')),
  workplace_address: z.string().trim().optional().nullable().or(z.literal('')),
  phone_number: z.string()
    .regex(phoneRegex, 'Invalid phone number format')
    .optional()
    .nullable()
    .or(z.literal('')), // Allow empty string
  city: z.string().trim().optional().nullable().or(z.literal('')),
  notes: z.string().trim().optional().nullable().or(z.literal('')),
  fee: z.preprocess(
    // Convert empty string/null/undefined to undefined, otherwise attempt to convert to number
    (val) => (val === '' || val === null || val === undefined ? undefined : Number(val)),
    // Fee must be a positive number or undefined (optional)
    z.number({
      invalid_type_error: 'Fee must be a number',
    }).positive('Fee must be a positive number').optional()
  ),
  working_hours: WorkingHoursSchema.optional().nullable(),
  doctor_id: z.string().uuid('Invalid Doctor ID format').optional().nullable(),
  // We are not linking to alldoctors via the form for now
  // doctor_id: z.string().optional().nullable(),
});

export type DoctorFormData = z.infer<typeof doctorFormSchema>; 