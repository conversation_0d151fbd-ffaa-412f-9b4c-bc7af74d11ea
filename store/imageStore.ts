import { create } from 'zustand';
import { useMemo } from 'react';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { OpenAIOcrResults } from '@/hooks/useOpenAIImageOcr';
import { MedicineOcrResults } from '@/hooks/useSingleMedicineOcr';

export interface ImageCaptureResult {
  uri: string;
  name?: string;
  type?: string;
  ocrText?: string;
  ocrResults?: OpenAIOcrResults | MedicineOcrResults;
}

interface ImageStateItem {
  uri: string | null;
  isProcessing: boolean;
  error: Error | null;
  ocrResult: {
    rawText?: string;
    extractedData?: OpenAIOcrResults | MedicineOcrResults;
  } | null;
  isViewerVisible: boolean;
  previewLoadError: boolean;
  isChangingImage: boolean;
  wasInitialImageRemoved: boolean;
  hasTriggeredInitialOcr: boolean;
}

interface ImageState {
  images: Record<string, ImageStateItem>;
  
  // Actions
  initializeImage: (key: string, initialUri?: string | null) => void;
  setImageUri: (key: string, uri: string | null) => void;
  setProcessing: (key: string, processing: boolean) => void;
  setError: (key: string, error: Error | null) => void;
  setOcrResult: (key: string, result: { rawText?: string; extractedData?: OpenAIOcrResults | MedicineOcrResults } | null) => void;
  setViewerVisible: (key: string, visible: boolean) => void;
  setPreviewLoadError: (key: string, hasError: boolean) => void;
  setChangingImage: (key: string, changing: boolean) => void;
  setWasInitialImageRemoved: (key: string, removed: boolean) => void;
  setHasTriggeredInitialOcr: (key: string, triggered: boolean) => void;
  clearError: (key: string) => void;
  cleanup: (key: string) => Promise<void>;
  
  // Image picker actions
  takePhoto: (key: string, options?: {
    manipulationOptions?: {
      resizeWidth?: number;
      compressQuality?: number;
      format?: ImageManipulator.SaveFormat;
    };
    pickerOptions?: {
      allowsEditing?: boolean;
      quality?: number;
      aspect?: [number, number];
      mediaTypes?: ImagePicker.MediaTypeOptions;
    };
  }) => Promise<ImageCaptureResult | null>;
  
  chooseFromLibrary: (key: string, options?: {
    manipulationOptions?: {
      resizeWidth?: number;
      compressQuality?: number;
      format?: ImageManipulator.SaveFormat;
    };
    pickerOptions?: {
      allowsEditing?: boolean;
      quality?: number;
      aspect?: [number, number];
      mediaTypes?: ImagePicker.MediaTypeOptions;
    };
  }) => Promise<ImageCaptureResult | null>;
}

const DEFAULT_MANIPULATION_OPTIONS = {
  resizeWidth: 1024,
  compressQuality: 0.7,
  format: ImageManipulator.SaveFormat.JPEG,
};

const DEFAULT_PICKER_OPTIONS = {
  allowsEditing: true,
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  quality: 0.8,
};

const createDefaultImageState = (): ImageStateItem => ({
  uri: null,
  isProcessing: false,
  error: null,
  ocrResult: null,
  isViewerVisible: false,
  previewLoadError: false,
  isChangingImage: false,
  wasInitialImageRemoved: false,
  hasTriggeredInitialOcr: false,
});

export const useImageStore = create<ImageState>((set, get) => ({
  images: {},

  initializeImage: (key: string, initialUri?: string | null) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...createDefaultImageState(),
          uri: initialUri || null,
        },
      },
    }));
  },

  setImageUri: (key: string, uri: string | null) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          uri,
        },
      },
    }));
  },

  setProcessing: (key: string, processing: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          isProcessing: processing,
        },
      },
    }));
  },

  setError: (key: string, error: Error | null) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          error,
        },
      },
    }));
  },

  setOcrResult: (key: string, result: { rawText?: string; extractedData?: OpenAIOcrResults | MedicineOcrResults } | null) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          ocrResult: result,
        },
      },
    }));
  },

  setViewerVisible: (key: string, visible: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          isViewerVisible: visible,
        },
      },
    }));
  },

  setPreviewLoadError: (key: string, hasError: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          previewLoadError: hasError,
        },
      },
    }));
  },

  setChangingImage: (key: string, changing: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          isChangingImage: changing,
        },
      },
    }));
  },

  setWasInitialImageRemoved: (key: string, removed: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          wasInitialImageRemoved: removed,
        },
      },
    }));
  },

  setHasTriggeredInitialOcr: (key: string, triggered: boolean) => {
    set((state) => ({
      images: {
        ...state.images,
        [key]: {
          ...(state.images[key] || createDefaultImageState()),
          hasTriggeredInitialOcr: triggered,
        },
      },
    }));
  },

  clearError: (key: string) => {
    get().setError(key, null);
  },

  cleanup: async (key: string) => {
    const imageState = get().images[key];
    if (imageState?.uri) {
      // Clean up temporary files from ImageManipulator
      if (imageState.uri.includes('ImageManipulator') || imageState.uri.includes('tmp')) {
        try {
          await FileSystem.deleteAsync(imageState.uri, { idempotent: true });
        } catch (error) {
          console.log('Failed to cleanup temporary file:', error);
        }
      }
    }

    // Clear from store
    set((state) => {
      const { [key]: removed, ...restImages } = state.images;
      return { images: restImages };
    });
  },

  takePhoto: async (key: string, options = {}) => {
    const { manipulationOptions = {}, pickerOptions = {} } = options;
    const finalManipulationOptions = { ...DEFAULT_MANIPULATION_OPTIONS, ...manipulationOptions };
    const finalPickerOptions = { ...DEFAULT_PICKER_OPTIONS, ...pickerOptions };

    const store = get();
    
    try {
      // Request permissions
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const libraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (cameraPermission.status !== 'granted' || libraryPermission.status !== 'granted') {
        const permError = new Error('Camera and Photo Library access are required.');
        store.setError(key, permError);
        throw permError;
      }

      store.setProcessing(key, true);
      store.clearError(key);

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: finalPickerOptions.mediaTypes || ImagePicker.MediaTypeOptions.Images,
        allowsEditing: finalPickerOptions.allowsEditing,
        quality: finalPickerOptions.quality,
        aspect: finalPickerOptions.aspect,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        store.setProcessing(key, false);
        return null;
      }

      // Manipulate image
      const originalAsset = result.assets[0];
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        originalAsset.uri,
        [{ resize: { width: finalManipulationOptions.resizeWidth } }],
        {
          compress: finalManipulationOptions.compressQuality,
          format: finalManipulationOptions.format,
        }
      );

      const processedUri = manipulatedImage.uri;
      const originalFileName = originalAsset.fileName || 'camera_image.jpg';
      const baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.')) || originalFileName;
      const finalName = `${baseName}.${finalManipulationOptions.format.toLowerCase()}`;

      // Update store
      store.setImageUri(key, processedUri);
      store.setWasInitialImageRemoved(key, false);
      store.setPreviewLoadError(key, false);
      store.setChangingImage(key, false);
      store.clearError(key);
      store.setOcrResult(key, null);

      const captureResult: ImageCaptureResult = {
        uri: processedUri,
        name: finalName,
        type: `image/${finalManipulationOptions.format.toLowerCase()}`,
      };

      return captureResult;

    } catch (error) {
      const pickerError = error instanceof Error ? error : new Error('Failed to take photo');
      store.setError(key, pickerError);
      throw pickerError;
    } finally {
      store.setProcessing(key, false);
    }
  },

  chooseFromLibrary: async (key: string, options = {}) => {
    const { manipulationOptions = {}, pickerOptions = {} } = options;
    const finalManipulationOptions = { ...DEFAULT_MANIPULATION_OPTIONS, ...manipulationOptions };
    const finalPickerOptions = { ...DEFAULT_PICKER_OPTIONS, ...pickerOptions };

    const store = get();
    
    try {
      // Request permissions
      const libraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (libraryPermission.status !== 'granted') {
        const permError = new Error('Photo Library access is required.');
        store.setError(key, permError);
        throw permError;
      }

      store.setProcessing(key, true);
      store.clearError(key);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: finalPickerOptions.mediaTypes || ImagePicker.MediaTypeOptions.Images,
        allowsEditing: finalPickerOptions.allowsEditing,
        quality: finalPickerOptions.quality,
        aspect: finalPickerOptions.aspect,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        store.setProcessing(key, false);
        return null;
      }

      // Manipulate image
      const originalAsset = result.assets[0];
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        originalAsset.uri,
        [{ resize: { width: finalManipulationOptions.resizeWidth } }],
        {
          compress: finalManipulationOptions.compressQuality,
          format: finalManipulationOptions.format,
        }
      );

      const processedUri = manipulatedImage.uri;
      const originalFileName = originalAsset.fileName || 'selected_image.jpg';
      const baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.')) || originalFileName;
      const finalName = `${baseName}.${finalManipulationOptions.format.toLowerCase()}`;

      // Update store
      store.setImageUri(key, processedUri);
      store.setWasInitialImageRemoved(key, false);
      store.setPreviewLoadError(key, false);
      store.setChangingImage(key, false);
      store.clearError(key);
      store.setOcrResult(key, null);

      const captureResult: ImageCaptureResult = {
        uri: processedUri,
        name: finalName,
        type: `image/${finalManipulationOptions.format.toLowerCase()}`,
      };

      return captureResult;

    } catch (error) {
      const pickerError = error instanceof Error ? error : new Error('Failed to choose from library');
      store.setError(key, pickerError);
      throw pickerError;
    } finally {
      store.setProcessing(key, false);
    }
  },
}));

// Helper hook to get image state for a specific key
export const useImage = (key: string) => {
  const store = useImageStore();
  const imageState = store.images[key] || createDefaultImageState();
  
  // Memoize actions to prevent infinite re-renders - only depend on key
  const actions = useMemo(() => ({
    setUri: (uri: string | null) => store.setImageUri(key, uri),
    setProcessing: (processing: boolean) => store.setProcessing(key, processing),
    setError: (error: Error | null) => store.setError(key, error),
    setOcrResult: (result: { rawText?: string; extractedData?: OpenAIOcrResults | MedicineOcrResults } | null) => store.setOcrResult(key, result),
    setViewerVisible: (visible: boolean) => store.setViewerVisible(key, visible),
    setPreviewLoadError: (hasError: boolean) => store.setPreviewLoadError(key, hasError),
    setChangingImage: (changing: boolean) => store.setChangingImage(key, changing),
    clearError: () => store.clearError(key),
    cleanup: () => store.cleanup(key),
    takePhoto: (options?: any) => store.takePhoto(key, options),
    chooseFromLibrary: (options?: any) => store.chooseFromLibrary(key, options),
  }), [key]);
  
  return {
    ...imageState,
    actions,
  };
};