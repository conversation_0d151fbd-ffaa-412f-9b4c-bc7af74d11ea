import { create } from 'zustand';
import { RouterSelectOption } from '@/components/common/RouterSelectInput';

interface RouterSelectState {
  isOpen: boolean;
  title: string;
  options: RouterSelectOption[];
  selectedOption?: RouterSelectOption;
  onSelect?: (option: RouterSelectOption) => void;
  onSearch?: (query: string) => void; // For server-side search if needed
  isLoading?: boolean;
  showCreateNewButton?: boolean;
  onCreateNew?: () => void;
  createText?: string;
  placeholder?: string;
  emptyResultsMessage?: string;
  showSearchInModal?: boolean; // New state property

  open: (config: Partial<Omit<RouterSelectState, 'isOpen' | 'open' | 'close'>>) => void;
  close: () => void;
  setSelectedOptionStore: (option?: RouterSelectOption) => void; // To update from modal if needed
  setOptionsStore: (options: RouterSelectOption[]) => void; // To update options dynamically
  setIsLoadingStore: (isLoading: boolean) => void;
}

export const useRouterSelectStore = create<RouterSelectState>((set, get) => ({
  isOpen: false,
  title: 'Select Option',
  options: [],
  selectedOption: undefined,
  onSelect: undefined,
  onSearch: undefined,
  isLoading: false,
  showCreateNewButton: false,
  onCreateNew: undefined,
  createText: 'Create New',
  placeholder: 'Search...',
  emptyResultsMessage: 'No results found',
  showSearchInModal: true, // Default value

  open: (config) => set({ ...config, isOpen: true }),
  close: () =>
    set({
      isOpen: false,
      // Reset transient props on close to avoid stale data for next open
      // options: [], // Keep options if they might be reused immediately? Or clear.
      // selectedOption: undefined, // This should be managed by the calling component's state
      onSelect: undefined,
      onSearch: undefined,
      // isLoading: false, // isLoading is more dynamic
      showCreateNewButton: false,
      onCreateNew: undefined,
      showSearchInModal: true, // Reset on close
    }),
  setSelectedOptionStore: (option) => set({ selectedOption: option }),
  setOptionsStore: (options) => set({ options }),
  setIsLoadingStore: (isLoading) => set({ isLoading }),
}));
