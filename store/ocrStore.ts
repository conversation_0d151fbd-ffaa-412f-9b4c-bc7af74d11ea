import { create } from "zustand";

export interface ExtractedMedicine {
  name: string;
  price?: number;
  description?: string;
}

interface OcrState {
  // Image handling
  capturedImageUri: string | null;
  setCapturedImageUri: (uri: string | null) => void;
  
  // OCR processing
  isProcessing: boolean;
  ocrError: string | null;
  extractedMedicines: ExtractedMedicine[];
  setProcessing: (processing: boolean) => void;
  setOcrError: (error: string | null) => void;
  setExtractedMedicines: (medicines: ExtractedMedicine[]) => void;
  
  // Reset function
  resetOcrState: () => void;
}

export const useOcrStore = create<OcrState>((set) => ({
  // Image handling
  capturedImageUri: null,
  setCapturedImageUri: (uri: string | null) => set({ capturedImageUri: uri }),
  
  // OCR processing
  isProcessing: false,
  ocrError: null,
  extractedMedicines: [],
  setProcessing: (processing: boolean) => set({ isProcessing: processing }),
  setOcrError: (error: string | null) => set({ ocrError: error }),
  setExtractedMedicines: (medicines: ExtractedMedicine[]) => set({ extractedMedicines: medicines }),
  
  // Reset function
  resetOcrState: () => set({ 
    capturedImageUri: null, 
    isProcessing: false, 
    ocrError: null, 
    extractedMedicines: []
  }),
})); 