import { create, StateCreator } from "zustand";

interface ModalCreationState {
  newlyCreatedPatientId: string | null;
  newlyCreatedDoctorId: string | null;
  setNewlyCreatedPatientId: (id: string | null) => void;
  setNewlyCreatedDoctorId: (id: string | null) => void;
  resetPatientCreation: () => void;
  resetDoctorCreation: () => void;
}

const storeCreator: StateCreator<ModalCreationState> = (set) => ({
  newlyCreatedPatientId: null,
  newlyCreatedDoctorId: null,
  setNewlyCreatedPatientId: (id: string | null) => set({ newlyCreatedPatientId: id }),
  setNewlyCreatedDoctorId: (id: string | null) => set({ newlyCreatedDoctorId: id }),
  resetPatientCreation: () => set({ newlyCreatedPatientId: null }),
  resetDoctorCreation: () => set({ newlyCreatedDoctorId: null }),
});

export const useModalCreationStore = create<ModalCreationState>(storeCreator); 