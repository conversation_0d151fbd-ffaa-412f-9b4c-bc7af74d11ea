import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { medicationId, sessionId } = await req.json();

    if (!medicationId) {
      return new Response(JSON.stringify({ error: "Medication ID is required" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const supabase = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Get the authenticated user from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Authorization header is required");
    }

    // Extract user ID from JWT token
    const token = authHeader.replace("Bearer ", "");
    let userId: string;
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      userId = payload.sub;
      if (!userId) {
        throw new Error("User ID not found in token");
      }
    } catch (error) {
      throw new Error("Invalid authorization token");
    }

    // Get medication details with ownership verification
    const { data: medication, error: medError } = await supabase
      .from("user_meds")
      .select("*")
      .eq("id", medicationId)
      .eq("user_id", userId) // Verify ownership
      .single();

    if (medError || !medication) {
      throw new Error("Medication not found or access denied");
    }

    // Check if analysis already exists
    const { data: existingAnalysis } = await supabase
      .from("medication_analyses")
      .select("*")
      .eq("medication_id", medicationId)
      .single();

    if (existingAnalysis) {
      // Update session_id if provided and not already set
      if (sessionId && !existingAnalysis.session_id) {
        const { data: updatedAnalysis } = await supabase
          .from("medication_analyses")
          .update({ session_id: sessionId })
          .eq("id", existingAnalysis.id)
          .select()
          .single();
        
        return new Response(
          JSON.stringify({ analysis: updatedAnalysis || existingAnalysis, cached: true }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }

      return new Response(
        JSON.stringify({ analysis: existingAnalysis, cached: true }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Generate AI analysis
    const analysis = await analyzeMedicationWithAI(medication);

    // Save analysis with session_id and user_id
    const insertData: any = {
      medication_id: medicationId,
      user_id: userId,
      ai_analysis: analysis,
      analysis_type: "general",
    };

    // Add session_id if provided
    if (sessionId) {
      insertData.session_id = sessionId;
    }

    const { data: savedAnalysis, error: saveError } = await supabase
      .from("medication_analyses")
      .insert(insertData)
      .select()
      .single();

    if (saveError) {
      throw new Error("Failed to save analysis");
    }

    return new Response(
      JSON.stringify({ analysis: savedAnalysis, cached: false }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Medication analysis error:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});

async function analyzeMedicationWithAI(medication: any) {
  const openaiApiKey = Deno.env.get("OPENAI_API_KEY");
  if (!openaiApiKey) throw new Error("OpenAI API key not configured");

  // Build dosage string
  let dosageInfo = "Not specified";
  if (medication.dosage_amount && medication.dosage_unit) {
    dosageInfo = `${medication.dosage_amount} ${medication.dosage_unit}`;
  }

  // Build frequency string  
  let frequencyInfo = "Not specified";
  if (medication.frequency_amount && medication.frequency_unit) {
    frequencyInfo = `${medication.frequency_amount} ${medication.frequency_unit}`;
  }

  // Build duration string
  let durationInfo = "Not specified";
  if (medication.duration_amount && medication.duration_unit) {
    durationInfo = `${medication.duration_amount} ${medication.duration_unit}`;
  } else if (medication.duration_unit) {
    durationInfo = medication.duration_unit;
  }

  const prompt = `Analyze this medication and provide detailed information:

Medication: ${medication.name || "Not specified"}
Description: ${medication.description || "Not specified"}
Dosage: ${dosageInfo}
Frequency: ${frequencyInfo}
Duration: ${durationInfo}
Expiration Date: ${medication.expiration_date || "Not specified"}
Opened On: ${medication.opened_on_date || "Not specified"}
Notes: ${medication.notes || "None"}

Please provide a comprehensive analysis in JSON format with these exact keys:
- summary: A clear explanation of what this medication is used for and any important information
- interactions: Array of common drug interactions to watch for
- sideEffects: Array of side effects to monitor
- warnings: Array of important warnings or precautions
- recommendations: Array of general recommendations for use

Always remind users to consult healthcare professionals.`;

  const response = await fetch("https://api.openai.com/v1/chat/completions", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${openaiApiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: "gpt-4.1-nano",
      messages: [
        {
          role: "system",
          content:
            "You are a medical AI assistant. Provide accurate medication information and always remind users to consult healthcare professionals. Return only valid JSON.",
        },
        { role: "user", content: prompt },
      ],
      max_tokens: 1000,
      temperature: 0.3,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const content = data.choices[0]?.message?.content;

  if (!content) {
    throw new Error("No response from OpenAI");
  }

  try {
    return JSON.parse(content);
  } catch {
    // Fallback if JSON parsing fails
    return {
      summary: content,
      interactions: ["Always check with your pharmacist for drug interactions"],
      sideEffects: ["Monitor for any unusual symptoms"],
      warnings: ["Always consult your healthcare provider"],
      recommendations: ["Follow prescribed dosage", "Report any side effects"],
    };
  }
} 