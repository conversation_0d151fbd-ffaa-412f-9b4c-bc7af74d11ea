import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface MedicineExtraction {
  name: string;
  price?: number;
  description?: string;
}

interface ReceiptOcrResponse {
  success: boolean;
  data?: {
    rawText: string;
    extractedMedicines: MedicineExtraction[];
    confidence: number;
    processingTime: number;
  };
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authenticated user from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Authorization header is required')
    }

    // Extract user ID from JWT token
    const token = authHeader.replace('Bearer ', '')
    let userId: string
    try {
      // Decode JWT to get user ID from 'sub' claim
      const payload = JSON.parse(atob(token.split('.')[1]))
      userId = payload.sub
      if (!userId) {
        throw new Error('User ID not found in token')
      }
    } catch (error) {
      throw new Error('Invalid authorization token')
    }

    const { imageBase64 } = await req.json()
    if (!imageBase64) {
      throw new Error('Image data is required')
    }

    const startTime = Date.now()
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Prepare the prompt for medicine extraction
    const prompt = `You are an expert OCR system specialized in extracting medicine information from pharmacy bills/receipts.

Analyze the provided pharmacy receipt/bill image and extract medicine information in JSON format:

{
  "rawText": "Complete text content from the image",
  "extractedMedicines": [
    {
      "name": "Medicine name",
      "price": 25.50,
      "description": "Brief description if available"
    }
  ],
  "confidence": 0.95
}

Rules:
1. Extract ONLY medicines/drugs, ignore other items
2. Clean medicine names (remove dosage info, keep only the medicine name)
3. Extract prices as numbers (remove currency symbols)
4. Combine similar medicines if they appear multiple times
5. If information is unclear, omit the field
6. Focus on medicine-related items only
7. Return ONLY the JSON object, no additional text`

    // Call OpenAI Vision API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`,
                  detail: 'high'
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    })

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      throw new Error(`OpenAI API error: ${errorText}`)
    }

    const openaiResult = await openaiResponse.json()
    const content = openaiResult.choices[0]?.message?.content

    if (!content) {
      throw new Error('No content received from OpenAI')
    }

    // Parse the JSON response
    let parsedResult
    try {
      parsedResult = JSON.parse(content)
    } catch (parseError) {
      // If JSON parsing fails, try to extract JSON from the content
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedResult = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('Failed to parse OpenAI response as JSON')
      }
    }

    const processingTime = Date.now() - startTime

    const response: ReceiptOcrResponse = {
      success: true,
      data: {
        rawText: parsedResult.rawText || '',
        extractedMedicines: parsedResult.extractedMedicines || [],
        confidence: parsedResult.confidence || 0.8,
        processingTime
      }
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Medicine OCR Error:', error)
    
    const errorResponse: ReceiptOcrResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}) 