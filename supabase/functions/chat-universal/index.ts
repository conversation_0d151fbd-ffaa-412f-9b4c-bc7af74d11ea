import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { sessionId, message, chatType, contextId } = await req.json();

    if (!sessionId || !message) {
      return new Response(
        JSON.stringify({ error: "Session ID and message are required" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const supabase = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Get the authenticated user from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Authorization header is required");
    }

    // Extract user ID from JWT token
    const token = authHeader.replace("Bearer ", "");
    let userId: string;
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      userId = payload.sub;
      if (!userId) {
        throw new Error("User ID not found in token");
      }
    } catch (error) {
      throw new Error("Invalid authorization token");
    }

    // Verify session ownership
    const { data: session, error: sessionError } = await supabase
      .from("ai_chat_sessions")
      .select("*")
      .eq("id", sessionId)
      .eq("user_id", userId)
      .single();

    if (sessionError || !session) {
      throw new Error("Chat session not found or access denied");
    }

    // Get context based on chat type
    let systemPrompt = "";
    let contextData = null;

    switch (session.chat_type) {
      case "lab_result":
        // Get lab result analysis
        const { data: labAnalysis } = await supabase
          .from("lab_result_analyses")
          .select("*")
          .eq("lab_result_id", session.context_id)
          .single();
        contextData = labAnalysis;
        systemPrompt = `You are a medical AI assistant helping users understand their lab results.

Lab Analysis Context: ${JSON.stringify(labAnalysis?.ai_analysis)}

Guidelines:
- Answer questions about the lab results clearly and accurately
- Use the analysis context to provide specific insights
- Always remind users to consult healthcare professionals for medical advice
- Keep explanations accessible to non-medical users
- If asked about values not in the analysis, acknowledge the limitation
- Be supportive and informative, not alarming

**IMPORTANT**: Always format your responses using markdown:
- Use **bold** for important terms and values
- Use headers (## or ###) to organize information
- Use bullet points (-) for lists
- Use \`code\` formatting for lab values and medical terms
- Use > for important warnings or notes
- Make responses well-structured and readable`;
        break;

      case "medication":
        // Get medication analysis
        const { data: medAnalysis } = await supabase
          .from("medication_analyses")
          .select("*")
          .eq("medication_id", session.context_id)
          .single();
        contextData = medAnalysis;
        systemPrompt = `You are a medical AI assistant helping users understand their medications.

Medication Analysis Context: ${JSON.stringify(medAnalysis?.ai_analysis)}

Guidelines:
- Answer questions about medications, interactions, side effects clearly
- Use the analysis context to provide specific insights
- Always remind users to consult healthcare professionals for medical advice
- Provide practical advice for medication management
- If asked about interactions not in the analysis, acknowledge the limitation
- Be supportive and informative, not alarming

**IMPORTANT**: Always format your responses using markdown:
- Use **bold** for important medication names and dosages
- Use headers (## or ###) to organize information
- Use bullet points (-) for lists of side effects, interactions, etc.
- Use \`code\` formatting for dosages and medical terms
- Use > for important warnings or precautions
- Make responses well-structured and readable`;
        break;

      case "general":
        systemPrompt = `You are a helpful medical AI assistant. Provide general health information and guidance.

Guidelines:
- Provide helpful health information and guidance
- Always remind users to consult healthcare professionals for medical advice
- Be supportive and encouraging
- Avoid making specific diagnoses or treatment recommendations
- Focus on general wellness and health education
- If unsure about medical facts, acknowledge limitations

**IMPORTANT**: Always format your responses using markdown:
- Use **bold** for important health terms
- Use headers (## or ###) to organize information
- Use bullet points (-) for lists and recommendations
- Use \`code\` formatting for medical terms when appropriate
- Use > for important health tips or warnings
- Make responses well-structured and readable`;
        break;

      default:
        throw new Error("Invalid chat type");
    }

    // Get chat history
    const { data: history } = await supabase
      .from("ai_chat_messages")
      .select("role, content")
      .eq("session_id", sessionId)
      .order("created_at", { ascending: true });

    // Prepare messages for OpenAI
    const messages: ChatMessage[] = [
      { role: "system", content: systemPrompt },
      ...(history || []).map(msg => ({
        role: msg.role as "user" | "assistant",
        content: msg.content,
      })),
      { role: "user", content: message },
    ];

    // Get OpenAI API key
    const openaiApiKey = Deno.env.get("OPENAI_API_KEY");
    if (!openaiApiKey) {
      throw new Error("OpenAI API key not configured");
    }

    // Create OpenAI streaming request
    const openaiResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4.1-nano",
        messages,
        stream: true,
        max_tokens: 1000,
        temperature: 0.3,
      }),
    });

    if (!openaiResponse.ok) {
      throw new Error(`OpenAI API error: ${openaiResponse.status}`);
    }

    // Return Server-Sent Events stream
    return new Response(
      new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          let fullResponse = "";

          try {
            const reader = openaiResponse.body?.getReader();
            const decoder = new TextDecoder();

            if (!reader) {
              throw new Error("Failed to get response reader");
            }

            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              const lines = chunk.split("\n");

              for (const line of lines) {
                if (line.startsWith("data: ")) {
                  const data = line.slice(6);
                  if (data === "[DONE]") {
                    break;
                  }

                  try {
                    const parsed = JSON.parse(data);
                    const content = parsed.choices[0]?.delta?.content || "";
                    if (content) {
                      fullResponse += content;
                      controller.enqueue(
                        encoder.encode(
                          `data: ${JSON.stringify({ content, done: false })}\n\n`
                        )
                      );
                    }
                  } catch (e) {
                    // Skip invalid JSON lines
                    continue;
                  }
                }
              }
            }

            // Save messages to database
            await supabase.from("ai_chat_messages").insert([
              { session_id: sessionId, role: "user", content: message },
              { session_id: sessionId, role: "assistant", content: fullResponse },
            ]);

            // Update session timestamp
            await supabase
              .from("ai_chat_sessions")
              .update({ updated_at: new Date().toISOString() })
              .eq("id", sessionId);

            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ content: "", done: true })}\n\n`
              )
            );
          } catch (error) {
            console.error("Streaming error:", error);
            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ error: error.message || "Stream failed", done: true })}\n\n`
              )
            );
          } finally {
            controller.close();
          }
        },
      }),
      {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          "Connection": "keep-alive",
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    console.error("Chat error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Chat failed" }),
      {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
}); 