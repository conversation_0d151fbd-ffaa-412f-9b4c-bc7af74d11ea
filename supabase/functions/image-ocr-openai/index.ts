/// <reference types="https://deno.land/x/deno/cli/types/dts/lib.deno.d.ts" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'npm:@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface OcrResults {
  labName?: string;
  resultDate?: string;
  patientName?: string;
  patientId?: string;
  website?: string;
  phoneNumber1?: string;
  phoneNumber2?: string;
  phoneNumber3?: string;
  password?: string;
}

interface OcrResponse {
  success: boolean;
  data?: {
    rawText: string;
    extractedData: OcrResults;
    confidence: number;
    processingTime: number;
  };
  error?: string;
}

const LAB_RESULT_OCR_PROMPT = `
You are an expert OCR system specialized in extracting structured data from medical lab result images.

Analyze the provided image and extract the following information in JSON format:

{
  "rawText": "Complete text content from the image",
  "extractedData": {
    "labName": "Name of the laboratory or medical facility",
    "resultDate": "Date of the lab results (ISO format YYYY-MM-DD)",
    "patientName": "Patient's full name",
    "patientId": "Patient ID, MRN, or reference number",
    "website": "Laboratory website URL if present",
    "phoneNumber1": "Primary phone number",
    "phoneNumber2": "Secondary phone number if present",
    "phoneNumber3": "Third phone number if present",
    "password": "Password for the lab portal should also be extracted from the images"
  },
  "confidence": 0.95
}

Rules:
1. Extract text exactly as it appears
2. For dates, convert to ISO format (YYYY-MM-DD)
3. Clean phone numbers to standard format
4. If information is unclear or missing, omit the field
5. Provide confidence score (0-1) based on text clarity
6. Focus on header information, patient details, and contact info
7. Return ONLY the JSON object, no additional text
`;

serve(async (req: Request) => {
  console.log('OCR Edge Function called with method:', req.method);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now();

  try {
    console.log('Processing OCR request...');
    
    // Get the authenticated user from the request
    const authHeader = req.headers.get('Authorization')
    console.log('Auth header present:', !!authHeader);
    
    if (!authHeader) {
      console.log('No authorization header found');
      return new Response(JSON.stringify({
        success: false,
        error: 'Authorization header is required'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Extract user ID from JWT token
    const token = authHeader.replace('Bearer ', '')
    let userId: string
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      userId = payload.sub
      console.log('User ID extracted:', userId);
      
      if (!userId) {
        console.log('No user ID found in token');
        return new Response(JSON.stringify({
          success: false,
          error: 'User ID not found in token'
        }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
    } catch (error) {
      console.log('Token parsing error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid authorization token'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const requestBody = await req.json()
    console.log('Request body keys:', Object.keys(requestBody));
    console.log('Has imageData:', !!requestBody.imageData);
    console.log('Has mimeType:', !!requestBody.mimeType);

    // Expect base64 image data from client
    if (!requestBody.imageData || !requestBody.mimeType) {
      console.log('Missing imageData or mimeType');
      return new Response(JSON.stringify({
        success: false,
        error: 'imageData and mimeType are required'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const imageData = requestBody.imageData
    const mimeType = requestBody.mimeType
    console.log('Image data length:', imageData.length);
    console.log('MIME type:', mimeType);

    // Call OpenAI Vision API
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    console.log('OpenAI API key present:', !!openaiApiKey);
    
    if (!openaiApiKey) {
      console.log('OpenAI API key not configured');
      return new Response(JSON.stringify({
        success: false,
        error: 'OpenAI API key not configured'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    console.log('Calling OpenAI Vision API...');
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-nano',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: LAB_RESULT_OCR_PROMPT
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${imageData}`,
                  detail: 'high'
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.1,
      }),
    })

    console.log('OpenAI response status:', openaiResponse.status);
    
    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      console.log('OpenAI error response:', errorText);
      return new Response(JSON.stringify({
        success: false,
        error: `OpenAI API error: ${openaiResponse.status} - ${errorText}`
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const openaiData = await openaiResponse.json()
    const content = openaiData.choices[0]?.message?.content
    console.log('OpenAI response content length:', content?.length || 0);

    if (!content) {
      console.log('No content in OpenAI response');
      return new Response(JSON.stringify({
        success: false,
        error: 'No response from OpenAI'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Parse the JSON response from OpenAI
    let parsedResult: any
    try {
      parsedResult = JSON.parse(content)
      console.log('Successfully parsed OpenAI response');
    } catch (parseError) {
      console.log('Failed to parse OpenAI response:', parseError);
      console.log('Raw content:', content);
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to parse OpenAI response as JSON'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const processingTime = Date.now() - startTime
    console.log('Processing completed in:', processingTime, 'ms');

    const response: OcrResponse = {
      success: true,
      data: {
        rawText: parsedResult.rawText || '',
        extractedData: parsedResult.extractedData || {},
        confidence: parsedResult.confidence || 0.8,
        processingTime
      }
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error: any) {
    console.error('OCR processing error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'OCR processing failed'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}) 