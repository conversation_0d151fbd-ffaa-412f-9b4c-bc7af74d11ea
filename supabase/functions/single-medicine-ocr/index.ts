import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SingleMedicineExtraction {
  name?: string;
  description?: string;
  dosage_amount?: number;
  dosage_unit?: string;
  frequency_amount?: number;
  frequency_unit?: string;
  duration_amount?: number;
  duration_unit?: string;
  expiration_date?: string;
  opened_on_date?: string;
  price?: number;
  notes?: string;
}

interface SingleMedicineOcrResponse {
  success: boolean;
  data?: {
    rawText: string;
    extractedMedicine: SingleMedicineExtraction;
    confidence: number;
    processingTime: number;
  };
  error?: string;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key
    // const supabase = createClient(
    //   Deno.env.get('SUPABASE_URL') ?? '',
    //   Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    // )

    // Get the authenticated user from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Authorization header is required')
    }

    // Extract user ID from JWT token
    const token = authHeader.replace('Bearer ', '')
    let userId: string
    try {
      // Decode JWT to get user ID from 'sub' claim
      const payload = JSON.parse(atob(token.split('.')[1]))
      userId = payload.sub
      if (!userId) {
        throw new Error('User ID not found in token')
      }
    } catch (error) {
      throw new Error('Invalid authorization token')
    }

    const { imageData, mimeType } = await req.json()
    if (!imageData) {
      throw new Error('Image data is required')
    }

    const startTime = Date.now()
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Prepare the prompt for single medicine extraction
    const prompt = `You are an expert OCR system specialized in extracting medicine information from medicine packaging, bottles, boxes, or labels.

Analyze the provided medicine image and extract medicine information in JSON format:

{
  "rawText": "Complete text content from the image",
  "extractedMedicine": {
    "name": "Medicine name only (exclude dosage)",
    "description": "Brief description or usage instructions",
    "dosage_amount": 25.5,
    "dosage_unit": "mg",
    "frequency_amount": 2,
    "frequency_unit": "times per day",
    "duration_amount": 7,
    "duration_unit": "days",
    "expiration_date": "2024-12-31",
    "opened_on_date": "2024-01-15",
    "price": 15.99,
    "notes": "Any additional relevant information"
  },
  "confidence": 0.95
}

Rules:
1. Extract the PRIMARY medicine name (remove brand suffixes, dosage info from name)
2. Extract dosage amount as a NUMBER and unit separately (mg, ml, tablets, etc.)
3. Extract frequency information (how often to take): amount + unit (times per day, daily, weekly, etc.)
4. Extract duration if specified (how long to take): amount + unit (days, weeks, months)
5. Find expiration date and format as YYYY-MM-DD
6. Extract opened date if visible and format as YYYY-MM-DD
7. Extract any usage instructions or descriptions
8. Extract price if visible (remove currency symbols)
9. Include any warnings, side effects, or special notes
10. If information is unclear or not visible, omit the field
11. Return ONLY the JSON object, no additional text
12. Focus on the MAIN medicine, ignore secondary ingredients details

Common dosage units: mg, ml, g, tablets, capsules, drops, patches, units
Common frequency units: times per day, daily, twice daily, weekly, monthly, as needed
Common duration units: days, weeks, months, until finished`

    // Call OpenAI Vision API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${imageData}`,
                  detail: 'high'
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    })

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      throw new Error(`OpenAI API error: ${errorText}`)
    }

    const openaiResult = await openaiResponse.json()
    const content = openaiResult.choices[0]?.message?.content

    if (!content) {
      throw new Error('No content received from OpenAI')
    }

    // Parse the JSON response
    let parsedResult
    try {
      parsedResult = JSON.parse(content)
    } catch (parseError) {
      // If JSON parsing fails, try to extract JSON from the content
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedResult = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('Failed to parse OpenAI response as JSON')
      }
    }

    const processingTime = Date.now() - startTime

    const response: SingleMedicineOcrResponse = {
      success: true,
      data: {
        rawText: parsedResult.rawText || '',
        extractedMedicine: parsedResult.extractedMedicine || {},
        confidence: parsedResult.confidence || 0.8,
        processingTime
      }
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Single Medicine OCR Error:', error)
    
    const errorResponse: SingleMedicineOcrResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}) 