-- Add session_id to analysis tables and set up CASCADE DELETE
-- This will automatically delete analyses when their related chat session is deleted

-- Add session_id column to lab_result_analyses
ALTER TABLE lab_result_analyses 
ADD COLUMN session_id UUID REFERENCES ai_chat_sessions(id) ON DELETE CASCADE;

-- Add session_id column to medication_analyses  
ALTER TABLE medication_analyses 
ADD COLUMN session_id UUID REFERENCES ai_chat_sessions(id) ON DELETE CASCADE;

-- Update existing records to link analyses with their sessions
-- For lab result analyses
UPDATE lab_result_analyses 
SET session_id = (
  SELECT id FROM ai_chat_sessions 
  WHERE chat_type = 'lab_result' 
  AND context_id = lab_result_analyses.lab_result_id
  LIMIT 1
)
WHERE session_id IS NULL;

-- For medication analyses
UPDATE medication_analyses 
SET session_id = (
  SELECT id FROM ai_chat_sessions 
  WHERE chat_type = 'medication' 
  AND context_id = medication_analyses.medication_id
  LIMIT 1
)
WHERE session_id IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_lab_result_analyses_session_id ON lab_result_analyses(session_id);
CREATE INDEX IF NOT EXISTS idx_medication_analyses_session_id ON medication_analyses(session_id); 