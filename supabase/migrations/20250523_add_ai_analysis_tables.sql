-- Lab result analyses table
CREATE TABLE IF NOT EXISTS lab_result_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lab_result_id UUID REFERENCES labresults(id) ON DELETE CASCADE,
  extracted_text TEXT,
  ai_analysis JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat sessions table
CREATE TABLE IF NOT EXISTS lab_result_chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lab_result_id UUID REFERENCES labresults(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages table
CREATE TABLE IF NOT EXISTS lab_result_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES lab_result_chat_sessions(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE lab_result_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_result_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_result_chat_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for lab_result_analyses (using Clerk JWT sub claim)
CREATE POLICY "Users can access own analyses" ON lab_result_analyses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM labresults
      WHERE labresults.id = lab_result_analyses.lab_result_id
      AND labresults.user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')
    )
  );

-- RLS Policies for lab_result_chat_sessions (using Clerk JWT sub claim)
CREATE POLICY "Users can access own chat sessions" ON lab_result_chat_sessions
  FOR ALL USING (user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub'));

-- RLS Policies for lab_result_chat_messages (using Clerk JWT sub claim)
CREATE POLICY "Users can access own chat messages" ON lab_result_chat_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM lab_result_chat_sessions
      WHERE lab_result_chat_sessions.id = lab_result_chat_messages.session_id
      AND lab_result_chat_sessions.user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')
    )
  );

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_lab_result_analyses_lab_result_id ON lab_result_analyses(lab_result_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_lab_result_id ON lab_result_chat_sessions(lab_result_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON lab_result_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON lab_result_chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON lab_result_chat_messages(created_at);