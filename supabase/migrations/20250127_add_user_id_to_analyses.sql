-- Add user_id columns to analysis tables for proper ownership tracking
-- This fixes the security issue where analysis records weren't linked to users

-- Add user_id to lab_result_analyses table
ALTER TABLE lab_result_analyses 
ADD COLUMN user_id TEXT REFERENCES profiles(id) ON DELETE CASCADE;

-- Add user_id to medication_analyses table  
ALTER TABLE medication_analyses
ADD COLUMN user_id TEXT REFERENCES profiles(id) ON DELETE CASCADE;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_lab_result_analyses_user_id ON lab_result_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_medication_analyses_user_id ON medication_analyses(user_id);

-- Update RLS policies to use user_id for security
-- Note: These policies should already exist, this just ensures they're correct

-- Lab Result Analyses RLS
DROP POLICY IF EXISTS "Users can view their own lab analyses" ON lab_result_analyses;
CREATE POLICY "Users can view their own lab analyses" ON lab_result_analyses
  FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert their own lab analyses" ON lab_result_analyses;  
CREATE POLICY "Users can insert their own lab analyses" ON lab_result_analyses
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update their own lab analyses" ON lab_result_analyses;
CREATE POLICY "Users can update their own lab analyses" ON lab_result_analyses
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Medication Analyses RLS  
DROP POLICY IF EXISTS "Users can view their own medication analyses" ON medication_analyses;
CREATE POLICY "Users can view their own medication analyses" ON medication_analyses
  FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert their own medication analyses" ON medication_analyses;
CREATE POLICY "Users can insert their own medication analyses" ON medication_analyses
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update their own medication analyses" ON medication_analyses;
CREATE POLICY "Users can update their own medication analyses" ON medication_analyses
  FOR UPDATE USING (auth.uid()::text = user_id); 