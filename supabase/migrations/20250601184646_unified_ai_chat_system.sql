-- Unified AI Chat System Migration
-- This creates the database schema for a unified chat system handling general, lab result, and medication chats

-- Universal chat sessions table
CREATE TABLE ai_chat_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id text NOT NULL,
  chat_type text NOT NULL CHECK (chat_type IN ('general', 'lab_result', 'medication')),
  title text,
  context_id uuid, -- References lab_result.id or user_meds.id
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Universal chat messages table
CREATE TABLE ai_chat_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id uuid REFERENCES ai_chat_sessions(id) ON DELETE CASCADE,
  role text NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content text NOT NULL,
  metadata jsonb, -- For attachments, analysis refs, etc.
  created_at timestamp DEFAULT now()
);

-- Medication analyses table (NEW - similar to lab_result_analyses)
CREATE TABLE medication_analyses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  medication_id uuid REFERENCES user_meds(id) ON DELETE CASCADE,
  ai_analysis jsonb NOT NULL, -- Analysis results from OpenAI
  analysis_type text DEFAULT 'general', -- 'interaction', 'side_effects', 'general'
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_ai_chat_sessions_user_id ON ai_chat_sessions(user_id);
CREATE INDEX idx_ai_chat_sessions_type ON ai_chat_sessions(chat_type);
CREATE INDEX idx_ai_chat_messages_session_id ON ai_chat_messages(session_id);
CREATE INDEX idx_medication_analyses_medication_id ON medication_analyses(medication_id);

-- Row Level Security (RLS) policies
ALTER TABLE ai_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE medication_analyses ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_chat_sessions
CREATE POLICY "Users can access own chat sessions" ON ai_chat_sessions
  FOR ALL USING (user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub'));

-- RLS Policies for ai_chat_messages
CREATE POLICY "Users can access own chat messages" ON ai_chat_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM ai_chat_sessions
      WHERE ai_chat_sessions.id = ai_chat_messages.session_id
      AND ai_chat_sessions.user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')
    )
  );

-- RLS Policies for medication_analyses
CREATE POLICY "Users can access own medication analyses" ON medication_analyses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_meds
      WHERE user_meds.id = medication_analyses.medication_id
      AND user_meds.user_id = (current_setting('request.jwt.claims', true)::jsonb ->> 'sub')
    )
  );
