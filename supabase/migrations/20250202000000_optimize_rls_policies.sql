-- Migration to optimize RLS policies for better performance
-- Replace direct auth function calls with subqueries to avoid re-evaluation per row

-- Drop existing policies that need optimization
-- AI Chat Sessions
DROP POLICY IF EXISTS "Users can access own chat sessions" ON ai_chat_sessions;

-- AI Chat Messages  
DROP POLICY IF EXISTS "Users can access own chat messages" ON ai_chat_messages;

-- Lab Result Analyses
DROP POLICY IF EXISTS "Users can access own analyses" ON lab_result_analyses;

-- Medication Analyses
DROP POLICY IF EXISTS "Users can access own medication analyses" ON medication_analyses;

-- Lab Results
DROP POLICY IF EXISTS "Authenticated users can view their own lab results" ON labresults;
DROP POLICY IF EXISTS "Authenticated users can create their own lab results" ON labresults;
DROP POLICY IF EXISTS "Authenticated users can update their own lab results" ON labresults;
DROP POLICY IF EXISTS "Authenticated users can delete their own lab results" ON labresults;

-- Patients
DROP POLICY IF EXISTS "Authenticated users can view their own patients" ON patients;
DROP POLICY IF EXISTS "Authenticated users can create their own patients" ON patients;
DROP POLICY IF EXISTS "Authenticated users can update their own patients" ON patients;
DROP POLICY IF EXISTS "Authenticated users can delete their own patients" ON patients;

-- Prescriptions
DROP POLICY IF EXISTS "Authenticated users can view their own prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Authenticated users can create their own prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Authenticated users can update their own prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Authenticated users can delete their own prescriptions" ON prescriptions;

-- Profiles
DROP POLICY IF EXISTS "Authenticated users can view their own profiles" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can update their own profiles" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can delete their own profiles" ON profiles;

-- User Doctors
DROP POLICY IF EXISTS "Authenticated users can view their own doctors" ON user_doctors;
DROP POLICY IF EXISTS "Authenticated users can create their own doctors" ON user_doctors;
DROP POLICY IF EXISTS "Authenticated users can update their own doctors" ON user_doctors;
DROP POLICY IF EXISTS "Authenticated users can delete their own doctors" ON user_doctors;

-- User Medications
DROP POLICY IF EXISTS "Authenticated users can view their own medications" ON user_meds;
DROP POLICY IF EXISTS "Authenticated users can create their own medications" ON user_meds;
DROP POLICY IF EXISTS "Authenticated users can update their own medications" ON user_meds;
DROP POLICY IF EXISTS "Authenticated users can delete their own medications" ON user_meds;

-- Create optimized policies with subqueries
-- AI Chat Sessions
CREATE POLICY "Users can access own chat sessions" ON ai_chat_sessions 
FOR ALL TO public 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- AI Chat Messages
CREATE POLICY "Users can access own chat messages" ON ai_chat_messages 
FOR ALL TO public 
USING (EXISTS (
  SELECT 1 FROM ai_chat_sessions 
  WHERE ai_chat_sessions.id = ai_chat_messages.session_id 
  AND ai_chat_sessions.user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text))
));

-- Lab Result Analyses
CREATE POLICY "Users can access own analyses" ON lab_result_analyses 
FOR ALL TO public 
USING (EXISTS (
  SELECT 1 FROM labresults 
  WHERE labresults.id = lab_result_analyses.lab_result_id 
  AND labresults.user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text))
));

-- Medication Analyses
CREATE POLICY "Users can access own medication analyses" ON medication_analyses 
FOR ALL TO public 
USING (EXISTS (
  SELECT 1 FROM user_meds 
  WHERE user_meds.id = medication_analyses.medication_id 
  AND user_meds.user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text))
));

-- Lab Results
CREATE POLICY "Authenticated users can view their own lab results" ON labresults 
FOR SELECT TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can create their own lab results" ON labresults 
FOR INSERT TO authenticated 
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own lab results" ON labresults 
FOR UPDATE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own lab results" ON labresults 
FOR DELETE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- Patients
CREATE POLICY "Authenticated users can view their own patients" ON patients 
FOR SELECT TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can create their own patients" ON patients 
FOR INSERT TO authenticated 
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own patients" ON patients 
FOR UPDATE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own patients" ON patients 
FOR DELETE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- Prescriptions
CREATE POLICY "Authenticated users can view their own prescriptions" ON prescriptions 
FOR SELECT TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can create their own prescriptions" ON prescriptions 
FOR INSERT TO authenticated 
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own prescriptions" ON prescriptions 
FOR UPDATE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own prescriptions" ON prescriptions 
FOR DELETE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- Profiles
CREATE POLICY "Authenticated users can view their own profiles" ON profiles 
FOR SELECT TO authenticated 
USING (id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own profiles" ON profiles 
FOR UPDATE TO authenticated 
USING (id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own profiles" ON profiles 
FOR DELETE TO authenticated 
USING (id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- User Doctors
CREATE POLICY "Authenticated users can view their own doctors" ON user_doctors 
FOR SELECT TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can create their own doctors" ON user_doctors 
FOR INSERT TO authenticated 
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own doctors" ON user_doctors 
FOR UPDATE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own doctors" ON user_doctors 
FOR DELETE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

-- User Medications
CREATE POLICY "Authenticated users can view their own medications" ON user_meds 
FOR SELECT TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can create their own medications" ON user_meds 
FOR INSERT TO authenticated 
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can update their own medications" ON user_meds 
FOR UPDATE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)))
WITH CHECK (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text)));

CREATE POLICY "Authenticated users can delete their own medications" ON user_meds 
FOR DELETE TO authenticated 
USING (user_id = (SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'sub'::text))); 