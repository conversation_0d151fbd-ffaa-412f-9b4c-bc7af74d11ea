import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';
import en from './locales/en.json';
import bg from './locales/bg.json';

const LANGUAGE_STORAGE_KEY = 'user_language_preference';

// Get device locale with better fallback handling
const getDeviceLanguage = (): string => {
  try {
    const locales = getLocales();
    const primaryLocale = locales[0];
    
    // Check if we support the exact language tag (e.g., 'bg-BG')
    const languageTag = primaryLocale.languageTag;
    if (languageTag.startsWith('bg')) return 'bg';
    if (languageTag.startsWith('en')) return 'en';
    
    // Check primary language code
    const languageCode = primaryLocale.languageCode;
    if (languageCode === 'bg') return 'bg';
    if (languageCode === 'en') return 'en';
    
    // Default fallback
    return 'en';
  } catch (error) {
    console.warn('Error detecting device language:', error);
    return 'en';
  }
};

const deviceLanguage = getDeviceLanguage();

// Initialize i18n
const i18n = i18next.use(initReactI18next);

// Async initialization function
const initI18n = async () => {
  try {
    // Try to get saved language preference
    const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    const initialLanguage = savedLanguage || deviceLanguage;

    await i18n.init({
      resources: {
        en: {
          translation: en,
        },
        bg: {
          translation: bg,
        },
      },
      lng: initialLanguage,
      fallbackLng: 'en',
      debug: __DEV__, // Enable debug in development
      interpolation: {
        escapeValue: false, // React already does escaping
      },
      react: {
        useSuspense: false,
      },
    });

    console.log(`I18n initialized with language: ${initialLanguage}`);
  } catch (error) {
    console.error('Failed to initialize i18n:', error);
    // Fallback initialization
    await i18n.init({
      resources: { en: { translation: en }, bg: { translation: bg } },
      lng: 'en',
      fallbackLng: 'en',
      interpolation: { escapeValue: false },
      react: { useSuspense: false },
    });
  }
};

// Function to change language and persist preference
export const changeLanguage = async (languageCode: string) => {
  try {
    await i18n.changeLanguage(languageCode);
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
    console.log(`Language changed to: ${languageCode}`);
  } catch (error) {
    console.error('Failed to change language:', error);
  }
};

// Function to get current language
export const getCurrentLanguage = () => i18n.language;

// Function to get available languages
export const getAvailableLanguages = () => [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'bg', name: 'Bulgarian', nativeName: 'Български' },
];

// Initialize on import
initI18n();

export default i18n;