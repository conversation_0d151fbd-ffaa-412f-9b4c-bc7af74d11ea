{"expo": {"name": "receptur<PERSON>", "slug": "receptur<PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "receptur<PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.miroslav.valev.recepturko"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.miroslav.valev.recepturko", "permissions": ["android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.SCHEDULE_EXACT_ALARM"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-localization", "expo-font", "expo-web-browser", ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff", "defaultChannel": "medicine-expiration", "sounds": []}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "9bd6526a-1326-4aa5-8fbb-73aa4e741b5f"}}, "owner": "miroslav.valev"}}