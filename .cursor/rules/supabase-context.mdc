---
description: 
globs: 
alwaysApply: true
---
# Supabase Backend Context

This project uses Supabase for its backend services.

- **Database Types:** The database schema and types are defined in [types/database.types.ts](mdc:types/database.types.ts).
- **Development Practices:** Refer to the [expo-best-practices.mdc](mdc:.cursor/rules/expo-best-practices.mdc) rule for general Expo/React Native development guidelines.

Use the Supabase client libraries and the defined types for interacting with the backend as well as available mcp or cli
