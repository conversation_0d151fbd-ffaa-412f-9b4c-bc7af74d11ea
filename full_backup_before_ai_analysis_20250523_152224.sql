

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."blood_type_enum" AS ENUM (
    'A+',
    'A-',
    'B+',
    'B-',
    'AB+',
    'AB-',
    'O+',
    'O-'
);


ALTER TYPE "public"."blood_type_enum" OWNER TO "postgres";


CREATE TYPE "public"."dosage_unit_enum" AS ENUM (
    'mg',
    'g',
    'mcg',
    'kg',
    'ng',
    'IU',
    'mEq',
    'mmol',
    'mL',
    'L',
    'tsp',
    'tbsp',
    'drop',
    'cc',
    'tablet',
    'capsule',
    'puff',
    'suppository',
    'patch',
    'ampoule',
    'vial',
    'unit',
    'spray',
    'dropperful',
    'lozenge',
    'troche',
    'film'
);


ALTER TYPE "public"."dosage_unit_enum" OWNER TO "postgres";


CREATE TYPE "public"."duration_unit_enum" AS ENUM (
    'days',
    'weeks',
    'months',
    'Ongoing',
    'As needed'
);


ALTER TYPE "public"."duration_unit_enum" OWNER TO "postgres";


CREATE TYPE "public"."frequency_unit_enum" AS ENUM (
    'daily',
    'times daily',
    'hours',
    'weekly',
    'Monthly',
    'As needed',
    'Before meals',
    'After meals',
    'With meals',
    'At bedtime'
);


ALTER TYPE "public"."frequency_unit_enum" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  INSERT INTO public.profiles (id, email, created_at)
  VALUES (new.id, new.email, now());
  RETURN new;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."alldoctors" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "specialty" "text",
    "workplace" "text",
    "city" "text",
    "workplace_address" "text",
    "working_hours" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "phone_number" "text",
    "fee" numeric
);


ALTER TABLE "public"."alldoctors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."allmeds" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "image_bucket_id" "text" DEFAULT 'medicine_images'::"text",
    "image_path" "text",
    "image_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "price" numeric(10,2),
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "dosage_amount" numeric,
    "frequency_amount" numeric,
    "frequency_unit" "public"."frequency_unit_enum",
    "duration_amount" numeric,
    "duration_unit" "public"."duration_unit_enum",
    "dosage_unit" "public"."dosage_unit_enum"
);


ALTER TABLE "public"."allmeds" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."labresults" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "text" NOT NULL,
    "password" "text" NOT NULL,
    "lab_name" "text" NOT NULL,
    "website" "text",
    "patient_name" "text",
    "image_bucket_id" "text",
    "image_path" "text",
    "result_date" "date",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "phone_number1" "text",
    "phone_number2" "text",
    "phone_number3" "text",
    "patient_reference_id" "uuid",
    "user_id" "text",
    "captured_pdf_path" "text",
    "captured_pdf_bucket_id" "text"
);


ALTER TABLE "public"."labresults" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patients" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "bloodtype" "public"."blood_type_enum",
    "age" integer,
    "user_id" "text"
);


ALTER TABLE "public"."patients" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prescriptions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "doctor_id" "uuid",
    "name" "text" NOT NULL,
    "prescription_date" "date" NOT NULL,
    "image_bucket_id" "text" DEFAULT 'prescription_images'::"text",
    "front_image_path" "text",
    "back_image_path" "text",
    "image_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "text"
);


ALTER TABLE "public"."prescriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "text" NOT NULL,
    "email" "text",
    "full_name" "text",
    "avatar_url" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_doctors" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "doctor_id" "uuid",
    "name" "text" NOT NULL,
    "specialty" "text",
    "workplace" "text",
    "city" "text",
    "workplace_address" "text",
    "working_hours" "jsonb",
    "is_custom" boolean DEFAULT false,
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "phone_number" "text",
    "fee" numeric,
    "user_id" "text"
);


ALTER TABLE "public"."user_doctors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_meds" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "med_id" "uuid",
    "prescription_id" "uuid",
    "name" "text" NOT NULL,
    "image_bucket_id" "text" DEFAULT 'medicine_images'::"text",
    "image_path" "text",
    "expiration_date" "date",
    "is_custom" boolean DEFAULT false,
    "price" numeric(10,2),
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "description" "text",
    "opened_on_date" "date",
    "dosage_amount" numeric,
    "frequency_amount" numeric,
    "frequency_unit" "public"."frequency_unit_enum",
    "duration_amount" numeric,
    "duration_unit" "public"."duration_unit_enum",
    "user_id" "text",
    "dosage_unit" "public"."dosage_unit_enum"
);


ALTER TABLE "public"."user_meds" OWNER TO "postgres";


ALTER TABLE ONLY "public"."alldoctors"
    ADD CONSTRAINT "alldoctors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."allmeds"
    ADD CONSTRAINT "allmeds_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."labresults"
    ADD CONSTRAINT "labresults_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prescriptions"
    ADD CONSTRAINT "prescriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_doctors"
    ADD CONSTRAINT "user_doctors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_meds"
    ADD CONSTRAINT "user_meds_pkey" PRIMARY KEY ("id");



CREATE OR REPLACE TRIGGER "set_patients_updated_at" BEFORE UPDATE ON "public"."patients" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "set_prescriptions_updated_at" BEFORE UPDATE ON "public"."prescriptions" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "set_updated_at" BEFORE UPDATE ON "public"."labresults" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "set_user_doctors_updated_at" BEFORE UPDATE ON "public"."user_doctors" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "set_user_meds_updated_at" BEFORE UPDATE ON "public"."user_meds" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



ALTER TABLE ONLY "public"."labresults"
    ADD CONSTRAINT "labresults_patient_reference_id_fkey" FOREIGN KEY ("patient_reference_id") REFERENCES "public"."patients"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."labresults"
    ADD CONSTRAINT "labresults_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prescriptions"
    ADD CONSTRAINT "prescriptions_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."user_doctors"("id");



ALTER TABLE ONLY "public"."prescriptions"
    ADD CONSTRAINT "prescriptions_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prescriptions"
    ADD CONSTRAINT "prescriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_doctors"
    ADD CONSTRAINT "user_doctors_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."alldoctors"("id");



ALTER TABLE ONLY "public"."user_doctors"
    ADD CONSTRAINT "user_doctors_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_meds"
    ADD CONSTRAINT "user_meds_med_id_fkey" FOREIGN KEY ("med_id") REFERENCES "public"."allmeds"("id");



ALTER TABLE ONLY "public"."user_meds"
    ADD CONSTRAINT "user_meds_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_meds"
    ADD CONSTRAINT "user_meds_prescription_id_fkey" FOREIGN KEY ("prescription_id") REFERENCES "public"."prescriptions"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_meds"
    ADD CONSTRAINT "user_meds_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



CREATE POLICY "Authenticated users can create their own doctors" ON "public"."user_doctors" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can create their own lab results" ON "public"."labresults" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can create their own medications" ON "public"."user_meds" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can create their own patients" ON "public"."patients" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can create their own prescriptions" ON "public"."prescriptions" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own doctors" ON "public"."user_doctors" FOR DELETE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own lab results" ON "public"."labresults" FOR DELETE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own medications" ON "public"."user_meds" FOR DELETE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own patients" ON "public"."patients" FOR DELETE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own prescriptions" ON "public"."prescriptions" FOR DELETE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can delete their own profiles" ON "public"."profiles" FOR DELETE TO "authenticated" USING (("id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



COMMENT ON POLICY "Authenticated users can delete their own profiles" ON "public"."profiles" IS 'Ensures users can only delete their own profile based on the sub claim (Clerk User ID) in the JWT.';



CREATE POLICY "Authenticated users can update their own doctors" ON "public"."user_doctors" FOR UPDATE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can update their own lab results" ON "public"."labresults" FOR UPDATE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can update their own medications" ON "public"."user_meds" FOR UPDATE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can update their own patients" ON "public"."patients" FOR UPDATE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can update their own prescriptions" ON "public"."prescriptions" FOR UPDATE TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can update their own profiles" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (("id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text"))) WITH CHECK (("id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



COMMENT ON POLICY "Authenticated users can update their own profiles" ON "public"."profiles" IS 'Ensures users can only update their own profile based on the sub claim (Clerk User ID) in the JWT.';



CREATE POLICY "Authenticated users can view their own doctors" ON "public"."user_doctors" FOR SELECT TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can view their own lab results" ON "public"."labresults" FOR SELECT TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can view their own medications" ON "public"."user_meds" FOR SELECT TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can view their own patients" ON "public"."patients" FOR SELECT TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can view their own prescriptions" ON "public"."prescriptions" FOR SELECT TO "authenticated" USING (("user_id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



CREATE POLICY "Authenticated users can view their own profiles" ON "public"."profiles" FOR SELECT TO "authenticated" USING (("id" = (("current_setting"('request.jwt.claims'::"text", true))::"jsonb" ->> 'sub'::"text")));



COMMENT ON POLICY "Authenticated users can view their own profiles" ON "public"."profiles" IS 'Ensures users can only select their own profile based on the sub claim (Clerk User ID) in the JWT.';



CREATE POLICY "Everyone can view alldoctors" ON "public"."alldoctors" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Everyone can view allmeds" ON "public"."allmeds" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Service role can delete profiles" ON "public"."profiles" FOR DELETE TO "service_role" USING (true);



CREATE POLICY "Service role can insert profiles" ON "public"."profiles" FOR INSERT TO "service_role" WITH CHECK (true);



CREATE POLICY "Service role can update profiles" ON "public"."profiles" FOR UPDATE TO "service_role" USING (true) WITH CHECK (true);



CREATE POLICY "Service role can view profiles" ON "public"."profiles" FOR SELECT TO "service_role" USING (true);



ALTER TABLE "public"."alldoctors" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."allmeds" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."labresults" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patients" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prescriptions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_doctors" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_meds" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."alldoctors" TO "anon";
GRANT ALL ON TABLE "public"."alldoctors" TO "authenticated";
GRANT ALL ON TABLE "public"."alldoctors" TO "service_role";



GRANT ALL ON TABLE "public"."allmeds" TO "anon";
GRANT ALL ON TABLE "public"."allmeds" TO "authenticated";
GRANT ALL ON TABLE "public"."allmeds" TO "service_role";



GRANT ALL ON TABLE "public"."labresults" TO "anon";
GRANT ALL ON TABLE "public"."labresults" TO "authenticated";
GRANT ALL ON TABLE "public"."labresults" TO "service_role";



GRANT ALL ON TABLE "public"."patients" TO "anon";
GRANT ALL ON TABLE "public"."patients" TO "authenticated";
GRANT ALL ON TABLE "public"."patients" TO "service_role";



GRANT ALL ON TABLE "public"."prescriptions" TO "anon";
GRANT ALL ON TABLE "public"."prescriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."prescriptions" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."user_doctors" TO "anon";
GRANT ALL ON TABLE "public"."user_doctors" TO "authenticated";
GRANT ALL ON TABLE "public"."user_doctors" TO "service_role";



GRANT ALL ON TABLE "public"."user_meds" TO "anon";
GRANT ALL ON TABLE "public"."user_meds" TO "authenticated";
GRANT ALL ON TABLE "public"."user_meds" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
