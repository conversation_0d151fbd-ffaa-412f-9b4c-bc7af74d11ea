import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSignIn } from '@clerk/clerk-expo';
import { Link, useRouter } from 'expo-router';
import { useForm } from 'react-hook-form';
import ControlledInput from '@/components/ControlledInput';
import { 
  ForgotPasswordEmailFormData,
  forgotPasswordEmailResolver, 
} from '@/schema/auth';

export default function ForgotPassword() {
  const { isLoaded, signIn } = useSignIn();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
  } = useForm<ForgotPasswordEmailFormData>({
    resolver: forgotPasswordEmailResolver,
  });

  const onRequestReset = async (data: ForgotPasswordEmailFormData) => {
    if (!isLoaded || isLoading) {
      return;
    }
    setIsLoading(true);

    try {
      await signIn.create({
        strategy: 'reset_password_email_code',
        identifier: data.email,
      });
      router.push({ pathname: '/reset-password', params: { email: data.email }});
    } catch (err: any) {
      // console.error('Clerk Forgot Password Error:', JSON.stringify(err, null, 2));
      Alert.alert('Error', err.errors?.[0]?.message || 'Could not send password reset email.');
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900 p-4">
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-4">Reset Password</Text>
        
        <View className="space-y-4">
          <Text className="text-center text-muted-foreground dark:text-neutral-400 mb-8">Enter your email address below to receive a code to reset your password.</Text>
          <ControlledInput
            control={control}
            name="email"
            label="Email"
            placeholder="<EMAIL>"
            leftIcon="mail-outline"
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <TouchableOpacity
            onPress={handleSubmit(onRequestReset)}
            disabled={isLoading}
            className={`bg-primary p-4 rounded-md items-center ${isLoading ? 'opacity-50' : ''}`}
          >
            {isLoading ? (
              <ActivityIndicator className="text-primary-foreground" />
            ) : (
              <Text className="text-primary-foreground font-semibold">Send Reset Code</Text>
            )}
          </TouchableOpacity>
          <View className="flex-row justify-center items-center mt-4">
            <Link href="/sign-in" asChild>
              <TouchableOpacity>
                <Text className="text-primary dark:text-primary font-semibold">Cancel</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </View> 
    </SafeAreaView> 
  );
}

