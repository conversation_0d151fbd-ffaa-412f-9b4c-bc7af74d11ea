import { useSignIn, useSSO } from '@clerk/clerk-expo';
import { Link } from 'expo-router';
import { Text, TouchableOpacity, View, Alert, ActivityIndicator, Platform, useColorScheme } from 'react-native';
import React, { useState, useCallback } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useForm } from 'react-hook-form';
import { signInResolver, SignInFormData } from '@/schema/auth';
import ControlledInput from '@/components/ControlledInput';
import { useWarmUpBrowser } from '@/hooks/useWarmBrowser';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri } from 'expo-auth-session';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '@/hooks/useTranslation';

// Ensure AuthSession completes if app was closed during web-based auth
WebBrowser.maybeCompleteAuthSession();

export default function SignIn() {
  useWarmUpBrowser();
  const { t } = useTranslation();
  const { signIn, setActive, isLoaded } = useSignIn();
  const { startSSOFlow } = useSSO();
  const [isLoadingEmail, setIsLoadingEmail] = useState(false);
  const [isLoadingGoogle, setIsLoadingGoogle] = useState(false);
  const [isLoadingApple, setIsLoadingApple] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const {
    control,
    handleSubmit,
  } = useForm<SignInFormData>({
    resolver: signInResolver,
  });

  // Create redirect URI based on scheme in app.json
  const redirectUri = makeRedirectUri({
    // For usage in bare and standalone
    native: 'recepturko://', // Your scheme here
    // Use `true` if working on a native platform
    // Use `false` if working on web
    // useProxy: Platform.OS !== 'web', // Optional: Adjust based on proxy needs
  });

  const onSignInPress = async (data: SignInFormData) => {
    if (!isLoaded || isLoadingEmail) {
      return;
    }
    setIsLoadingEmail(true);

    try {
      const signInAttempt = await signIn.create({
        identifier: data.email,
        password: data.password,
      });

      if (signInAttempt.status === 'complete') {
        await setActive({ session: signInAttempt.createdSessionId });
      } else if (signInAttempt.status === 'needs_second_factor') {
        Alert.alert(t('alerts.twoFactorRequired'), t('alerts.twoFactorMessage'));
        // console.log('MFA Required', JSON.stringify(signInAttempt, null, 2));s
      } else {
        // console.error('Sign In status not complete:', JSON.stringify(signInAttempt, null, 2));
        Alert.alert(t('alerts.signInFailed'), t('alerts.signInFailedMessage'));
      }
    } catch (err: any) {
      // console.error('Clerk Sign In Error:', JSON.stringify(err, null, 2));
      Alert.alert(t('alerts.signInFailed'), err.errors?.[0]?.message || t('alerts.invalidCredentials'));
    } finally {
      setIsLoadingEmail(false);
    }
  };

  // Handler for Social Logins
  const onSocialSignIn = useCallback(async (strategy: 'oauth_google' | 'oauth_apple') => {
    if (!isLoaded) return;

    const setIsLoading = strategy === 'oauth_google' ? setIsLoadingGoogle : setIsLoadingApple;
    setIsLoading(true);

    try {
      const { createdSessionId, setActive } = await startSSOFlow({
        strategy: strategy,
        redirectUrl: redirectUri,
      });

      if (createdSessionId) {
        await setActive!({ session: createdSessionId });
        // Layout handles redirect
      } else {
        // Handle potential missing requirements (e.g., MFA for existing user)
        // For now, just log - might need UI to handle signIn/signUp objects
        // console.log('SSO requires further steps for signIn or signUp:', { signIn, signUp });
        Alert.alert(t('auth.loginIncomplete'), t('auth.furtherStepsRequired'));
      }
    } catch (err: any) {
      // console.error(`Clerk Social Sign In Error (${strategy}):`, JSON.stringify(err, null, 2));
      Alert.alert(t('auth.loginFailed'), err.errors?.[0]?.message || t('auth.socialSignInError'));
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, redirectUri, startSSOFlow, t]);

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900 p-4">
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-8">{t('auth.welcomeBack')}</Text>
        <View className="space-y-4">
          <ControlledInput
            control={control}
            name="email"
            label={t('auth.email')}
            placeholder={t('auth.emailPlaceholder')}
            leftIcon="mail-outline"
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <ControlledInput
            control={control}
            name="password"
            label={t('auth.password')}
            leftIcon="lock-closed-outline"
            placeholder={t('auth.passwordPlaceholder')}
            secureTextEntry
          />

          <View className="items-end mb-2">
             <Link href="/forgot-password" asChild>
               <TouchableOpacity>
                 <Text className="text-sm text-primary dark:text-primary">{t('auth.forgotPassword')}?</Text>
               </TouchableOpacity>
             </Link>
           </View>

          <TouchableOpacity
            onPress={handleSubmit(onSignInPress)}
            disabled={isLoadingEmail}
            className={`bg-primary p-4 rounded-md items-center ${isLoadingEmail ? 'opacity-50' : ''}`}
          >
            {isLoadingEmail ? (
              <ActivityIndicator className="text-primary-foreground" />
            ) : (
              <Text className="text-primary-foreground font-semibold">{t('auth.signIn')}</Text>
            )}
          </TouchableOpacity>

           <View className="flex-row items-center my-6">
             <View className="flex-1 h-px bg-muted dark:bg-neutral-700" />
             <Text className="px-4 text-muted-foreground dark:text-neutral-400">{t('common.or')}</Text>
             <View className="flex-1 h-px bg-muted dark:bg-neutral-700" />
           </View>

            {/* Social Login Buttons */}
            <View className="space-y-3">
              <TouchableOpacity
                onPress={() => onSocialSignIn('oauth_google')}
                disabled={isLoadingGoogle || isLoadingApple}
                className={`flex-row items-center justify-center bg-background dark:bg-neutral-800 border border-border dark:border-neutral-700 p-3 rounded-md ${isLoadingGoogle || isLoadingApple ? 'opacity-50' : ''}`}
              >
                {isLoadingGoogle ? (
                  <ActivityIndicator className="mr-2" color="#4285F4" />
                ) : (
                  <Ionicons name="logo-google" size={20} color="#4285F4" className="mr-2" />
                )}
                <Text className="text-foreground dark:text-neutral-100 font-semibold">{t('auth.signInWithGoogle')}</Text>
              </TouchableOpacity>

              {/* Apple Sign in only on iOS */}
              {Platform.OS === 'ios' && (
                <TouchableOpacity
                  onPress={() => onSocialSignIn('oauth_apple')}
                  disabled={isLoadingApple || isLoadingGoogle}
                  className={`flex-row items-center justify-center p-3 rounded-md ${isLoadingApple || isLoadingGoogle ? 'opacity-50' : ''} ${isDark ? 'bg-white' : 'bg-black'}`}
                >
                  {isLoadingApple ? (
                    <ActivityIndicator className="mr-2" color={isDark ? 'black' : 'white'} />
                  ) : (
                    <Ionicons name="logo-apple" size={20} color={isDark ? 'black' : 'white'} className="mr-2" />
                  )}
                  <Text className={`font-semibold ${isDark ? 'text-black' : 'text-white'}`}>{t('auth.signInWithApple')}</Text>
                </TouchableOpacity>
              )}
            </View>

          <View className="flex-row justify-center items-center mt-6">
            <Text className="text-muted-foreground dark:text-neutral-400">{t('auth.needAccount')} </Text>
            <Link href="/sign-up-email" asChild>
              <TouchableOpacity>
                <Text className="text-primary dark:text-primary font-semibold">{t('auth.signUp')}</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
}