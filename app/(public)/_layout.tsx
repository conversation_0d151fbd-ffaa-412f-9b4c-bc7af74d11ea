import { useAuth } from "@clerk/clerk-expo";
import { Redirect, Stack } from "expo-router";
import { useTheme } from '@react-navigation/native';

export default function PublicLayout() {
  const {isSignedIn} = useAuth();
  const theme = useTheme();

  if (isSignedIn) {
    return <Redirect href="/(protected)/(tabs)/tracker" />;
  } 

  return (
    <Stack 
      screenOptions={{
        headerStyle: { backgroundColor: theme.colors.card },
        headerTintColor: theme.colors.text,
        headerTitleStyle: { color: theme.colors.text },
        headerShown: true,
      }}
    >
      <Stack.Screen name="sign-in" options={{ headerShown: false }} />
      <Stack.Screen name="sign-up-email" options={{ headerTitle: 'Create Account' }}/>
      <Stack.Screen name="forgot-password" options={{ headerTitle: 'Forgot Password' }}/>
      <Stack.Screen name="verify-email" options={{ headerTitle: 'Verify Email' }}/>
      <Stack.Screen name="reset-password" options={{ headerTitle: 'Reset Password' }}/>
    </Stack>
  );
}