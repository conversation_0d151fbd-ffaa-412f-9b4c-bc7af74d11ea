import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSignIn } from '@clerk/clerk-expo';
import { Link, useRouter, useLocalSearchParams } from 'expo-router';
import { useForm } from 'react-hook-form';
import ControlledInput from '@/components/ControlledInput';
import {
  ForgotPasswordResetFormData,
  forgotPasswordResetResolver,
} from '@/schema/auth';

export default function ResetPassword() {
  const { isLoaded, signIn, setActive } = useSignIn();
  const router = useRouter();
  const { email } = useLocalSearchParams<{ email?: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'verify_code' | 'set_password'>('verify_code');

  // Use a single form for the whole process
  const {
      control,
      handleSubmit,
      getValues, // Use getValues to get code for the first step
  } = useForm<ForgotPasswordResetFormData>({
      resolver: forgotPasswordResetResolver,
      // Set mode to onChange to validate confirmPassword as user types
      mode: 'onChange', 
  });

  useEffect(() => {
    if (!isLoaded) return;
    if (signIn?.status !== 'needs_first_factor') {
        console.warn('Unexpected sign in status for password reset:', signIn?.status);
    }
  }, [isLoaded, signIn, router]);

  // Step 1: Verify the code
  const onVerifyCode = async () => {
    if (!isLoaded || isLoading || !signIn) return;
    setIsLoading(true);
    try {
        const code = getValues("code");
        if (!code || code.length < 6) {
            Alert.alert('Error', 'Please enter a valid 6-digit code.');
            setIsLoading(false);
            return;
        }

      const result = await signIn.attemptFirstFactor({
        strategy: 'reset_password_email_code',
        code: code,
      });

      if (result.status === 'needs_new_password') {
        setStep('set_password');
      } else {
        // console.error('Unexpected status after code verification:', result.status);
        Alert.alert('Error', 'Could not verify code.');
      }
    } catch (err: any) {
      // console.error('Clerk Verify Code Error:', JSON.stringify(err, null, 2));
      Alert.alert('Error', err.errors?.[0]?.message || 'Invalid verification code.');
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Set the new password
  const onSetPassword = async (data: ForgotPasswordResetFormData) => {
    // Data already contains validated password and confirmPassword
    if (!isLoaded || isLoading || !signIn) return;
    setIsLoading(true);
    try {
      const result = await signIn.resetPassword({
        password: data.password,
      });

      if (result.status === 'complete') {
        await setActive({ session: result.createdSessionId });
        Alert.alert('Success', 'Your password has been reset successfully.', [
            { text: "OK", onPress: () => router.replace('/(protected)/(tabs)') }
        ]);
      } else {
        // console.error('Unexpected status after password reset:', result.status);
        Alert.alert('Error', 'Could not reset password.');
      }
    } catch (err: any) {
      // console.error('Clerk Reset Password Error:', JSON.stringify(err, null, 2));
      Alert.alert('Error', err.errors?.[0]?.message || 'Could not reset password.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900 p-4">
      <View className="flex-1 justify-center px-6">

        {step === 'verify_code' && (
          <View className="space-y-4">
            <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-4">Enter Code</Text>
            <Text className="text-center text-muted-foreground dark:text-neutral-400 mb-8">Enter the verification code sent {email ? `to ${email}` : 'to your email'}.</Text>
             <ControlledInput
               control={control} // Use main control
               name="code"
               label="Verification Code"
               placeholder="123456"
               leftIcon="shield-checkmark-outline"
               keyboardType="number-pad"
               maxLength={6} // Added maxLength
             />
            <TouchableOpacity
              onPress={onVerifyCode} // Directly call the handler, not via handleSubmit
              disabled={isLoading}
              className={`bg-primary p-4 rounded-md items-center ${isLoading ? 'opacity-50' : ''}`}
            >
              {isLoading ? <ActivityIndicator className="text-primary-foreground" /> : <Text className="text-primary-foreground font-semibold">Verify Code</Text>}
            </TouchableOpacity>
          </View>
        )}

        {step === 'set_password' && (
          <View className="space-y-4">
            <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-4">Set New Password</Text>
            <Text className="text-center text-muted-foreground dark:text-neutral-400 mb-8">Enter and confirm your new password.</Text>
             <ControlledInput
               control={control} // Use main control
               name="password"
               label="New Password"
               placeholder="••••••••"
               leftIcon="lock-closed-outline"
               secureTextEntry
             />
             <ControlledInput
               control={control} // Use main control
               name="confirmPassword"
               label="Confirm New Password"
               placeholder="••••••••"
               leftIcon="lock-closed-outline"
               secureTextEntry
             />
            <TouchableOpacity
              onPress={handleSubmit(onSetPassword)} // Use handleSubmit here to trigger validation
              disabled={isLoading}
              className={`bg-primary p-4 rounded-md items-center ${isLoading ? 'opacity-50' : ''}`}
            >
              {isLoading ? <ActivityIndicator className="text-primary-foreground" /> : <Text className="text-primary-foreground font-semibold">Set New Password</Text>}
            </TouchableOpacity>
          </View>
        )}

         <View className="flex-row justify-center items-center mt-6">
           <Link href="/sign-in" asChild>
             <TouchableOpacity>
               <Text className="text-primary dark:text-primary font-semibold">Cancel</Text>
             </TouchableOpacity>
           </Link>
         </View>

      </View>
    </SafeAreaView>
  );
} 