import { useSignUp } from '@clerk/clerk-expo';
import { Link, useRouter } from 'expo-router';
import { Text, TouchableOpacity, View, ActivityIndicator, Alert } from 'react-native';
import React, { useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useForm } from 'react-hook-form';
import { signUpResolver, SignUpFormData } from '../../schema/auth';
import ControlledInput from '../../components/ControlledInput';

export default function SignUpEmail() {
  const { isLoaded, signUp } = useSignUp();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
  } = useForm<SignUpFormData>({
    resolver: signUpResolver,
  });

  const onSignUpPress = async (data: SignUpFormData) => {
    if (!isLoaded || isLoading) {
      return;
    }
    setIsLoading(true);

    try {
      await signUp.create({
        emailAddress: data.email,
        password: data.password,
      });

      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });

      router.push({ pathname: '/verify-email', params: { email: data.email } });

    } catch (err: any) {
      // console.error('Clerk Sign Up Error:', JSON.stringify(err, null, 2));
      Alert.alert('Sign Up Failed', err.errors?.[0]?.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900 p-4">
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-8">Create Account</Text>
        <View className="space-y-4">
           <ControlledInput
             control={control}
             name="email"
             label="Email"
             placeholder="<EMAIL>"
             leftIcon="mail-outline"
             keyboardType="email-address"
             autoCapitalize="none"
          />
          <ControlledInput
            control={control}
            name="password"
            label="Password"
            leftIcon="lock-closed-outline"
            placeholder="••••••••"
            secureTextEntry
          />
          <ControlledInput
            control={control}
            name="confirmPassword"
            label="Confirm Password"
            leftIcon="lock-closed-outline"
            placeholder="••••••••"
            secureTextEntry
          />

           <TouchableOpacity
            onPress={handleSubmit(onSignUpPress)}
            disabled={isLoading}
            className={`bg-primary p-4 rounded-md items-center ${isLoading ? 'opacity-50' : ''}`}
          >
            {isLoading ? (
              <ActivityIndicator className="text-primary-foreground" />
            ) : (
              <Text className="text-primary-foreground font-semibold">Create Account</Text>
            )}
          </TouchableOpacity>

           <View className="flex-row justify-center items-center mt-4">
            <Text className="text-muted-foreground dark:text-neutral-400">Already have an account? </Text>
            <Link href="/sign-in" asChild>
              <TouchableOpacity>
                <Text className="text-primary dark:text-primary font-semibold">Sign In</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}