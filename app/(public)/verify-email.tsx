import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, TouchableOpacity, TextInput, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSignUp } from '@clerk/clerk-expo';
import { useRouter, useLocalSearchParams, Link } from 'expo-router';

const RESEND_COOLDOWN = 60; // Cooldown period in seconds

export default function VerifyEmail() {
  const { isLoaded, signUp, setActive } = useSignUp();
  const router = useRouter();
  const { email } = useLocalSearchParams<{ email: string }>();
  const [code, setCode] = useState('');
  const [isLoadingVerify, setIsLoadingVerify] = useState(false);
  const [isLoadingResend, setIsLoadingResend] = useState(false);
  const [cooldown, setCooldown] = useState(0);
  const intervalRef = useRef<number | null>(null);

  // Start cooldown timer
  const startCooldown = () => {
    setCooldown(RESEND_COOLDOWN);
    intervalRef.current = setInterval(() => {
      setCooldown((prev) => {
        if (prev <= 1) {
          if (intervalRef.current !== null) {
            clearInterval(intervalRef.current);
          }
          intervalRef.current = null;
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Clear interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!isLoaded) return;
    if (!signUp) {
        // console.warn("SignUp object not available. Potential issue.");
        // Consider redirecting or showing specific error if signUp is unexpectedly null
    }
  }, [isLoaded, signUp]);

  const onPressVerify = async () => {
    if (!isLoaded || isLoadingVerify || !signUp) return;
    setIsLoadingVerify(true);
    try {
      const completeSignUp = await signUp.attemptEmailAddressVerification({ code });
      if (completeSignUp.status === 'complete') {
        await setActive({ session: completeSignUp.createdSessionId });
        router.replace('/(protected)/(tabs)');
      } else {
        // console.error('Sign Up status not complete:', JSON.stringify(completeSignUp, null, 2));
        Alert.alert('Verification Failed', 'Sign up process requires further steps.');
      }
    } catch (err: any) {
      //  console.error('Clerk Verification Error:', JSON.stringify(err, null, 2));
      Alert.alert('Verification Failed', err.errors?.[0]?.message || 'Invalid code or verification expired.');
    } finally {
      setIsLoadingVerify(false);
    }
  };

  const onResendCode = useCallback(async () => {
    if (!isLoaded || isLoadingResend || !signUp || cooldown > 0) return;
    setIsLoadingResend(true);
    try {
        await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
        Alert.alert('Code Resent', 'A new verification code has been sent to your email.');
        startCooldown(); // Start cooldown timer after successful resend
    } catch (err: any) {
        // console.error('Clerk Resend Code Error:', JSON.stringify(err, null, 2));
        Alert.alert('Error Resending Code', err.errors?.[0]?.message || 'Could not resend verification code.');
    } finally {
        setIsLoadingResend(false);
    }
  }, [isLoaded, signUp, isLoadingResend, cooldown]);

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900 p-4">
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-center text-foreground dark:text-neutral-100 mb-4">Verify Your Email</Text>
        <Text className="text-center text-muted-foreground dark:text-neutral-400 mb-8">Enter the 6-digit code sent to {email || 'your email'}</Text>

        <View className="space-y-4">
          <TextInput
            value={code}
            placeholder="123456"
            onChangeText={setCode}
            keyboardType="number-pad"
            className="w-full rounded-lg border border-input dark:border-neutral-700 bg-card dark:bg-neutral-800 px-4 py-3 text-base text-card-foreground dark:text-neutral-100 focus:border-primary focus:ring-1 focus:ring-ring dark:focus:border-primary text-center tracking-[8px] placeholder-muted-foreground dark:placeholder-neutral-500"
            maxLength={6}
          />

          <TouchableOpacity
            onPress={onPressVerify}
            disabled={isLoadingVerify || isLoadingResend || code.length < 6}
            className={`bg-primary p-4 rounded-md items-center ${isLoadingVerify || isLoadingResend || code.length < 6 ? 'opacity-50' : ''}`}
          >
            {isLoadingVerify ? (
              <ActivityIndicator className="text-primary-foreground" />
            ) : (
              <Text className="text-primary-foreground font-semibold">Verify Email</Text>
            )}
          </TouchableOpacity>

          {/* Resend Code Button */}
           <View className="items-center mt-4">
             <TouchableOpacity onPress={onResendCode} disabled={isLoadingResend || cooldown > 0}>
               <Text className={`text-center ${isLoadingResend || cooldown > 0 
                 ? 'text-muted-foreground dark:text-neutral-500' 
                 : 'text-primary dark:text-primary'}`}>
                 {isLoadingResend 
                  ? 'Sending...' 
                  : cooldown > 0 
                  ? `Resend Code (${cooldown}s)` 
                  : 'Resend Code'}
               </Text>
             </TouchableOpacity>
           </View>

          <View className="flex-row justify-center items-center mt-4">
             <Text className="text-muted-foreground dark:text-neutral-400">Changed your mind? </Text>
             <Link href="/sign-up-email" asChild>
               <TouchableOpacity>
                 <Text className="text-primary dark:text-primary font-semibold">Go Back</Text>
               </TouchableOpacity>
             </Link>
           </View>
        </View>
      </View>
    </SafeAreaView>
  );
} 