import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { Stack, router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { useLabResults } from '@/hooks/entities/useLabResults';
import LabResultCard from '@/components/labresults/LabResultCard';
import { Button } from '@/components/ui/Button';
import { useTranslation } from '@/hooks/useTranslation';

/**
 * Lab Results screen showing a list of the user's lab results
 */
export default function LabResultsScreen() {
  const { t } = useTranslation();
  const { useFetchLabResults } = useLabResults();
  const { data: labResults, isLoading, isError, refetch } = useFetchLabResults();

  // Navigate to the modal screen for viewing/editing
  const handlePressLabResult = (id: string) => {
    // Push the modal route with the id parameter
    router.push({ pathname: '/modals/modal-labresult', params: { id } });
  };

  // Navigate to the modal screen for creation
  const handleCreate = () => {
    // Push the modal route without params
    router.push('/modals/modal-labresult'); 
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('labResults.loadingLabResults')}</Text>
      </View>
    );
  }

  // Show error state
  if (isError) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center">
          {t('labResults.failedToLoad')}
        </Text>
        <Button title={t('common.tryAgain')} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Show empty state
  if (!labResults || labResults.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="document-text-outline" size={48} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-muted-foreground dark:text-neutral-400 mt-4 mb-2 text-center font-medium text-lg">
          {t('labResults.noLabResults')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-4 text-center px-6">
          {t('labResults.noLabResultsMessage')}
        </Text>
        <Button title={t('labResults.addFirstLabResult')} onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900"> 
      <Stack.Screen options={{ title: t('navigation.labResults') }} />
      
      <FlashList
        data={labResults}
        renderItem={({ item }) => (
          <LabResultCard
            result={item}
            onPress={() => handlePressLabResult(item.id)}
          />
        )}
        estimatedItemSize={100}
        contentContainerClassName="p-4"
        ItemSeparatorComponent={() => <View className="h-3" />}
      />
    </View>
  );
}