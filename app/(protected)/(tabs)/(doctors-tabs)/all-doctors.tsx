import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { useAllDoctors, AllDoctor } from '@/hooks/entities/useAllDoctors'; // Import the doctor hook
import { Button } from '@/components/ui/Button'; // Import Button
import { useTranslation } from '@/hooks/useTranslation';

// Simple Card component for displaying doctor information
interface AllDoctorCardProps {
  doctor: AllDoctor;
  onAdd: () => void;
}

const AllDoctorCard: React.FC<AllDoctorCardProps> = ({ doctor, onAdd }) => {
  const { t } = useTranslation();
  
  return (
    <View className="bg-card dark:bg-neutral-800 p-4 rounded-lg shadow mb-4">
      <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">{doctor.name}</Text>
      {doctor.specialty && <Text className="text-sm text-muted-foreground dark:text-neutral-300">{doctor.specialty}</Text>}
      {doctor.workplace && <Text className="text-sm text-muted-foreground dark:text-neutral-300">{doctor.workplace}</Text>}
      {doctor.city && <Text className="text-sm text-muted-foreground dark:text-neutral-300">{doctor.city}</Text>}
      <View className="mt-3">
        <Button title={t('doctors.addToMyDoctors')} onPress={onAdd} variant="outline" />
      </View>
    </View>
  );
};

const AllDoctorsScreen = () => {
  const { t } = useTranslation();
  const { useFetchAllDoctors } = useAllDoctors();
  const { data: allDoctors, isLoading, isError, refetch, error } = useFetchAllDoctors();

  const handleAddDoctor = (doctor: AllDoctor) => {
    router.push({
      pathname: '/modals/modal-doctor',
      params: {
        initialName: doctor.name,
        initialSpecialty: doctor.specialty,
        initialWorkplace: doctor.workplace,
        initialWorkplaceAddress: doctor.workplace_address,
        initialPhoneNumber: doctor.phone_number,
        initialCity: doctor.city,
        initialFee: doctor.fee?.toString(),
        initialWorkingHours: doctor.working_hours ? JSON.stringify(doctor.working_hours) : undefined,
        initialAlldoctorId: doctor.id,
      },
    });
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('doctors.loadingDoctors')}</Text>
      </View>
    );
  }

  if (isError) {
    console.error("Error fetching all doctors:", error);
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle-outline" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center text-lg font-medium">
          {t('doctors.failedToLoad')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4">
          {error?.message || t('common.error')}
        </Text>
        <Button title={t('common.tryAgain')} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  if (!allDoctors || allDoctors.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <Ionicons name="people-outline" size={54} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-foreground dark:text-neutral-100 mt-5 mb-2 text-center font-semibold text-xl">
          {t('doctors.noDoctors')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4 leading-relaxed">
          {t('doctors.noDoctorsMessage')}
        </Text>
        {/* Optionally, add a button to refresh or some other action */}
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <FlashList
        data={allDoctors}
        renderItem={({ item }) => (
          <AllDoctorCard
            doctor={item}
            onAdd={() => handleAddDoctor(item)}
          />
        )}
        estimatedItemSize={150} // Adjust based on AllDoctorCard expected height
        contentContainerClassName="p-4"
      />
    </View>
  );
};

export default AllDoctorsScreen;