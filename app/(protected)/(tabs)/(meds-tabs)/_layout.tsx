import { Ionicons } from "@expo/vector-icons";
import { createMaterialTopTabNavigator, MaterialTopTabNavigationEventMap, MaterialTopTabNavigationOptions } from '@react-navigation/material-top-tabs';
import { ParamListBase, TabNavigationState } from '@react-navigation/native';
import { withLayoutContext } from 'expo-router';
import { View, Text, useColorScheme } from 'react-native';
import colors from 'tailwindcss/colors';

const { Navigator } = createMaterialTopTabNavigator();

// Expo Router usesLayoutContext to connect Navigators to the router
export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);


export default function NestedMedsTabsLayout() { // Renamed component for clarity
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const primaryColor = colors.blue[600];
  const inactiveColor = isDark ? colors.neutral[400] : colors.neutral[500];
  const backgroundColor = isDark ? colors.neutral[800] : colors.white; // Using 800 for dark tab bar

  return <MaterialTopTabs
   screenOptions={{
     tabBarStyle: {
       height: 40, // Keep desired height
       backgroundColor: backgroundColor,
       borderBottomWidth: 1, // Optional: add a border
       borderBottomColor: isDark ? colors.neutral[700] : colors.neutral[200],
      },
     tabBarShowLabel: true,
     tabBarIndicatorStyle: { backgroundColor: primaryColor }, 
     tabBarActiveTintColor: primaryColor,
     tabBarInactiveTintColor: inactiveColor,
    }}
   >
    <MaterialTopTabs.Screen name="meds" options={{

      tabBarLabel: ({ color, focused }: { color: string, focused: boolean }) => (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons name="medical-outline" color={color} size={18} style={{ marginRight: 5 }}/>
          <Text style={{ color: color, fontSize: 14, fontWeight: focused ? 'bold' : 'normal' }}>
            Meds
          </Text>
        </View>
      ),
    }} />
    <MaterialTopTabs.Screen name="all-meds" options={{

      tabBarLabel: ({ color, focused }: { color: string, focused: boolean }) => (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons name="briefcase-outline" color={color} size={18} style={{ marginRight: 5 }}/>
          <Text style={{ color: color, fontSize: 14, fontWeight: focused ? 'bold' : 'normal' }}>
            All Meds
          </Text>
        </View>
      ),
    }} />
   </MaterialTopTabs>
}