import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { useMedicines } from '@/hooks/entities/useMedicines'; // Import the medicine hook
import MedicineCard from '@/components/medications/MedicineCard'; // Import the medicine card
import { Button } from '@/components/ui/Button'; // Import Button
import { useTranslation } from '@/hooks/useTranslation';

/**
 * User Medicines screen showing a list of the user's added medicines.
 */
export default function UserMedicinesScreen() {
  const { t } = useTranslation();
  const { useFetchMedicines } = useMedicines();
  const { data: medicines, isLoading, isError, refetch, error } = useFetchMedicines();

  // Navigate to the modal screen for viewing/editing a specific medicine
  const handlePressMedicine = (id: string) => {
    // Push the modal route with the medicine id parameter
    router.push({ pathname: '/modals/modal-medicine', params: { id } });
  };

  // Navigate to the modal screen for creating a new medicine
  const handleCreate = () => {
    // Push the modal route without params
    router.push('/modals/modal-medicine'); 
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('medicines.loadingMedicines')}</Text>
      </View>
    );
  }

  // Show error state
  if (isError) {
    // Log the full error object to the console
    console.error("Error fetching medicines:", error);
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle-outline" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center text-lg font-medium">
          {t('medicines.failedToLoad')}
        </Text>
        {/* Display the actual error message */}
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4">
          {error?.message || t('common.error')}
        </Text>
        <Button title={t('common.tryAgain')} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Show empty state
  if (!medicines || medicines.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <Ionicons name="medkit-outline" size={54} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-foreground dark:text-neutral-100 mt-5 mb-2 text-center font-semibold text-xl">
          {t('medicines.noMedicines')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4 leading-relaxed">
          {t('medicines.noMedicinesMessage')}
        </Text>
        <Button title={t('medicines.addFirstMedicine')} onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  // Main list view with 2-card horizontal layout
  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <FlashList
        data={medicines}
        renderItem={({ item, index }) => {
          // Check if this is the first item of a pair
          const isFirstInPair = index % 2 === 0;
          const nextItem = medicines[index + 1];

          // Only render pairs from the first item of each pair
          if (!isFirstInPair) return null;

          return (
            <View className="flex-row px-2">
              <MedicineCard
                medicine={item}
                onPress={() => handlePressMedicine(item.id)}
              />
              {nextItem && (
                <MedicineCard
                  medicine={nextItem}
                  onPress={() => handlePressMedicine(nextItem.id)}
                />
              )}
              {/* If there's no second item, add a spacer to maintain layout */}
              {!nextItem && <View className="flex-1 mx-1" />}
            </View>
          );
        }}
        estimatedItemSize={200} // Increased for new card design
        contentContainerClassName="py-4" // Add vertical padding
        getItemCount={(data) => Math.ceil((data?.length || 0) / 2)} // Half the items since we show 2 per row
        getItem={(data, index) => data[index * 2]} // Get the first item of each pair
      />
    </View>
  );
}