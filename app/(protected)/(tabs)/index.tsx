import React from 'react';
import { Text, View, ActivityIndicator } from 'react-native';
import { Stack, router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { usePrescriptions, PrescriptionWithDetails } from '@/hooks/entities/usePrescriptions';
import PrescriptionCard from '@/components/prescriptions/PrescriptionCard';
import { Button } from '@/components/ui/Button';
import { useTranslation, useCommonTranslation } from '@/hooks/useTranslation';

export default function PrescriptionsIndexScreen() {
  const { t } = useTranslation();
  const { tryAgain } = useCommonTranslation();
  const { useFetchPrescriptions } = usePrescriptions();
  const { data: prescriptions, isLoading, error, refetch } = useFetchPrescriptions();

  // Navigate to the modal screen for viewing/editing
  const handlePressPrescription = (id: string) => {
    router.push({ pathname: '/modals/modal-prescription', params: { id } });
  };

  // Navigate to the modal screen for creation
  const handleCreate = () => {
    router.push('/modals/modal-prescription');
  };

  // Render Item using PrescriptionCard (to be created)
  const renderItem = ({ item }: { item: PrescriptionWithDetails }) => (
    <PrescriptionCard
      prescription={item}
      onPress={() => handlePressPrescription(item.id)}
    />
  );

  // Loading State
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('prescriptions.loadingPrescriptions')}</Text>
      </View>
    );
  }

  // Error State
  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle-outline" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center text-lg">
          {t('prescriptions.failedToLoad')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4">
          {error.message}
        </Text>
        <Button title={tryAgain} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Empty State
  if (!prescriptions || prescriptions.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: t('navigation.prescriptions') }} />
        <Ionicons name="document-text-outline" size={56} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-foreground dark:text-neutral-100 mt-4 mb-2 text-center font-semibold text-xl">
          {t('prescriptions.noPrescriptions')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-6">
          {t('prescriptions.noPrescriptionsMessage')}
        </Text>
        <Button title={t('prescriptions.addFirstPrescription')} onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  // Main List View
  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      {/* Set title directly here if not using a separate header component */}
      <Stack.Screen options={{ title: t('navigation.prescriptions') }} />

      <FlashList
        data={prescriptions}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={120} // Adjust based on expected PrescriptionCard height
        contentContainerClassName="p-4" // Add padding using Tailwind
        // Optional: Add separator if desired
        ItemSeparatorComponent={() => <View className="h-3" />} 
      />

      {/* FAB for adding prescriptions */}
      
    </View>
  );
}