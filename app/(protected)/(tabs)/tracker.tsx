import React, { useState, useCallback, useLayoutEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from '@/hooks/useTranslation';
import { useTrackerData } from '@/hooks/use-tracker';
import HeaderDropDown from '@/components/HeaderDropDown';
import { useNavigation } from '@react-navigation/native';

type TimeRange = 'week' | 'month' | '3months' | 'year' | 'all';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: keyof typeof Ionicons.glyphMap;
  color?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color = '#2563eb' }) => (
  <View className="bg-card dark:bg-neutral-800 rounded-2xl p-4 flex-1 mx-1 border border-border dark:border-neutral-700">
    <View className="items-center">
      <View 
        className="w-12 h-12 rounded-full items-center justify-center mb-3"
        style={{ backgroundColor: `${color}15` }}
      >
        <Ionicons name={icon} size={24} style={{ color }} />
      </View>
      <Text className="text-2xl font-bold text-card-foreground dark:text-neutral-100 mb-1">
        {value}
      </Text>
      <Text className="text-xs text-muted-foreground dark:text-neutral-400 text-center font-medium">
        {title}
      </Text>
    </View>
  </View>
);

interface ExpenseItemProps {
  medicine: {
    name: string;
    totalCost: number;
    count: number;
  };
  formatCurrency: (amount: number) => string;
  purchases: string;
  rank: number;
}

const ExpenseItem: React.FC<ExpenseItemProps> = ({ medicine, formatCurrency, purchases, rank }) => {
  const getRankIcon = (): keyof typeof Ionicons.glyphMap => {
    if (rank === 1) return 'trophy';
    if (rank === 2) return 'ribbon';
    if (rank === 3) return 'medal';
    return 'ellipse';
  };

  const getRankColor = () => {
    switch (rank) {
      case 1: return '#FFD700'; // Gold
      case 2: return '#C0C0C0'; // Silver
      case 3: return '#CD7F32'; // Bronze
      default: return '#6B7280'; // Gray
    }
  };

  return (
    <View className="bg-card dark:bg-neutral-800 rounded-xl p-4 mb-3 border border-border dark:border-neutral-700">
      <View className="flex-row items-center">
        <View className="flex-row items-center flex-1">
          <View 
            className="w-10 h-10 rounded-full items-center justify-center mr-4"
            style={{ backgroundColor: `${getRankColor()}15` }}
          >
            <Ionicons name={getRankIcon()} size={18} style={{ color: getRankColor() }} />
          </View>
          
          <View className="flex-1">
            <Text className="text-base font-semibold text-card-foreground dark:text-neutral-100" numberOfLines={1}>
              {medicine.name}
            </Text>
            <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">
              {medicine.count} {purchases}
            </Text>
          </View>
        </View>
        
        <View className="items-end">
          <Text className="text-lg font-bold text-card-foreground dark:text-neutral-100">
            {formatCurrency(medicine.totalCost)}
          </Text>
          <Text className="text-xs text-muted-foreground dark:text-neutral-400">
            #{rank}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default function TrackerScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [selectedPatient, setSelectedPatient] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [refreshing, setRefreshing] = useState(false);

  const { 
    costStats, 
    healthStats, 
    patients, 
    isLoading,
    refetch
  } = useTrackerData(selectedPatient, timeRange);

  // Set custom header with patient dropdown
  useLayoutEffect(() => {
    navigation.setOptions({
      headertitle: '',
      headerRight: () => (
        <View className="flex-row items-center px-4">
          {patients.length > 0 && (
            <HeaderDropDown
              title={selectedPatient === 'all' ? t('tracker.allPatients') : patients.find(p => p.id === selectedPatient)?.name || t('tracker.allPatients')}
              icon="chevron-down"
              items={[
                { key: 'all', title: t('tracker.allPatients'), icon: 'person.2' },
                ...patients.map(patient => ({
                  key: patient.id,
                  title: patient.name,
                  icon: 'person.crop.circle' as const
                }))
              ]}
              onSelect={setSelectedPatient}
            />
          )}
        </View>
      ),
    });
  }, [navigation, t, patients, selectedPatient, setSelectedPatient]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);

  const timeRangeOptions: TimeRange[] = ['week', 'month', '3months', 'year', 'all'];

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" className="text-primary" />
          <Text className="mt-4 text-muted-foreground dark:text-neutral-400">
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      
      <ScrollView 
        className="flex-1" 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View className="px-6 pt-6 pb-4">
          <View className="flex-row items-center mb-2">
            <Ionicons name="analytics" size={28} className="text-primary dark:text-primary mr-3" />
            <Text className="text-2xl font-bold text-foreground dark:text-neutral-100">
              {t('tracker.title')}
            </Text>
          </View>
          <Text className="text-base text-muted-foreground dark:text-neutral-400">
            {selectedPatient === 'all' 
              ? `${t('tracker.allPatients')} • ${t(`tracker.${timeRange}`)}` 
              : `${patients.find(p => p.id === selectedPatient)?.name || ''} • ${t(`tracker.${timeRange}`)}`
            }
          </Text>
        </View>

        {/* Total Spending Hero */}
        <View className="px-6 mb-6">
          <View className="bg-primary dark:bg-primary rounded-2xl p-6">
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="text-primary-foreground dark:text-primary-foreground text-sm font-medium uppercase tracking-wide mb-2">
                  {t('tracker.totalSpent')}
                </Text>
                <Text className="text-primary-foreground dark:text-primary-foreground text-4xl font-bold mb-1">
                  {formatCurrency(costStats.totalCost)}
                </Text>
                <Text className="text-primary-foreground/80 dark:text-primary-foreground/80 text-sm">
                  {t(`tracker.${timeRange}`)}
                </Text>
              </View>
              <View className="w-16 h-16 bg-primary-foreground/20 dark:bg-primary-foreground/20 rounded-full items-center justify-center">
                <Ionicons name="trending-up" size={32} className="text-primary-foreground dark:text-primary-foreground" />
              </View>
            </View>
          </View>
        </View>

        {/* Time Range Filter */}
        <View className="px-6 mb-6">
          <View className="flex-row items-center mb-4">
            <Ionicons name="calendar-outline" size={20} className="text-foreground dark:text-neutral-100 mr-2" />
            <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">
              {t('tracker.timeRange')}
            </Text>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} className="flex-row">
            {timeRangeOptions.map((range) => (
              <TouchableOpacity
                key={range}
                onPress={() => setTimeRange(range)}
                className={`px-4 py-2 rounded-full mr-2 min-w-[80px] items-center ${
                  timeRange === range
                    ? 'bg-primary dark:bg-primary'
                    : 'bg-secondary dark:bg-neutral-700 border border-border dark:border-neutral-600'
                }`}
                activeOpacity={0.7}
              >
                <Text
                  className={`text-sm font-medium ${
                    timeRange === range
                      ? 'text-primary-foreground dark:text-white'
                      : 'text-secondary-foreground dark:text-neutral-200'
                  }`}
                >
                  {t(`tracker.${range}`)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>


        {/* Cost Statistics */}
        <View className="px-6 mb-6">
          <View className="flex-row items-center mb-4">
            <Ionicons name="wallet-outline" size={20} className="text-foreground dark:text-neutral-100 mr-2" />
            <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">
              {t('tracker.costStatistics')}
            </Text>
          </View>
          <View className="flex-row gap-3">
            <MetricCard
              title={t('tracker.medicines')}
              value={formatCurrency(costStats.totalMedicineCost)}
              icon="medical"
              color="#10B981"
            />
            <MetricCard
              title={t('tracker.doctors')}
              value={formatCurrency(costStats.totalDoctorFees)}
              icon="people"
              color="#8B5CF6"
            />
          </View>
        </View>

        {/* Health Statistics */}
        <View className="px-6 mb-6">
          <View className="flex-row items-center mb-4">
            <Ionicons name="fitness-outline" size={20} className="text-foreground dark:text-neutral-100 mr-2" />
            <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">
              {t('tracker.healthStatistics')}
            </Text>
          </View>
          <View className="flex-row gap-2">
            <MetricCard
              title={t('tracker.prescriptions')}
              value={healthStats.prescriptionsCount}
              icon="document-text"
              color="#6366F1"
            />
            <MetricCard
              title={t('tracker.labResults')}
              value={healthStats.labResultsCount}
              icon="flask"
              color="#F59E0B"
            />
            <MetricCard
              title={t('tracker.activeMedications')}
              value={healthStats.medicationsCount}
              icon="medkit"
              color="#EF4444"
            />
          </View>
        </View>

        {/* Top Medicines */}
        {costStats.topMedicines.length > 0 && (
          <View className="px-6 mb-8">
            <View className="flex-row items-center mb-4">
              <Ionicons name="trophy-outline" size={20} className="text-foreground dark:text-neutral-100 mr-2" />
              <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">
                {t('tracker.topMedicines')}
              </Text>
            </View>
            {costStats.topMedicines.map((item, index) => (
              <ExpenseItem
                key={`${item.name}-${index}`}
                medicine={item}
                formatCurrency={formatCurrency}
                purchases={t('tracker.purchases')}
                rank={index + 1}
              />
            ))}
          </View>
        )}

        {/* Empty State */}
        {costStats.totalCost === 0 && (
          <View className="px-6 py-16 items-center">
            <View className="w-24 h-24 bg-muted dark:bg-neutral-700 rounded-full items-center justify-center mb-6">
              <Ionicons name="analytics-outline" size={48} className="text-muted-foreground dark:text-neutral-400" />
            </View>
            <Text className="text-xl font-semibold text-foreground dark:text-neutral-100 mb-3 text-center">
              No data yet
            </Text>
            <Text className="text-muted-foreground dark:text-neutral-400 text-center text-base leading-6 px-4">
              Start adding medicines and prescriptions to see your health spending insights
            </Text>
          </View>
        )}

        {/* Bottom padding for safe area */}
        <View className="h-8" />
      </ScrollView>
    </View>
  );
}