import React, { useEffect } from 'react';
import { SafeAreaView, View, Text, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useOcrStore } from '@/store/ocrStore';
import { useMedicineReceiptOcr } from '@/hooks/useMedicineReceiptOcr';
import MedicineReviewForm from '@/components/medications/MedicineReviewForm';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { Button } from '@/components/ui/Button';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { useTranslation } from '@/hooks/useTranslation';

export default function MedicineOcrModalScreen() {
  const router = useRouter();
  const { showError } = useAppAlerts();
  const { t } = useTranslation();
  
  const { 
    capturedImageUri, 
    isProcessing, 
    ocrError, 
    extractedMedicines,
    resetOcrState 
  } = useOcrStore();
  
  const { processReceipt } = useMedicineReceiptOcr();

  // Start OCR processing when modal opens with image
  useEffect(() => {
    if (capturedImageUri && !isProcessing && extractedMedicines.length === 0 && !ocrError) {
      processReceipt(capturedImageUri).catch((error) => {
        console.error('OCR processing failed:', error);
      });
    }
  }, [capturedImageUri, isProcessing, extractedMedicines.length, ocrError, processReceipt]);

  // Handle modal dismiss
  const handleDismiss = () => {
    resetOcrState();
    router.dismiss();
  };

  // Handle retry OCR processing
  const handleRetry = () => {
    if (capturedImageUri) {
      processReceipt(capturedImageUri).catch((error) => {
        console.error('OCR retry failed:', error);
        showError(t('medicines.ocrProcessingFailed'));
      });
    }
  };

  // If no image captured, show error
  if (!capturedImageUri) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator
          error={new Error(t('errors.noReceiptImage'))}
          errorMessage={t('errors.noReceiptImage')}
          onRetry={handleDismiss}
        />
      </SafeAreaView>
    );
  }

  // Show processing state
  if (isProcessing) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <View className="flex-1 justify-center items-center p-6">
          {/* Show captured image */}
          <View className="w-64 h-64 mb-6 rounded-lg overflow-hidden border border-border dark:border-neutral-700">
            <Image 
              source={{ uri: capturedImageUri }} 
              className="w-full h-full"
              resizeMode="cover"
            />
          </View>
          
          {/* Processing indicator */}
          <FullScreenStatusIndicator
            isLoading={true}
            loadingMessage={t('medicines.ocrAnalyzingReceipt')}
          />
          
          {/* Cancel button */}
          <View className="mt-8">
            <Button
              title={t('common.cancel')}
              onPress={handleDismiss}
              variant="outline"
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (ocrError) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <View className="flex-1 justify-center items-center p-6">
          {/* Show captured image */}
          <View className="w-48 h-48 mb-6 rounded-lg overflow-hidden border border-border dark:border-neutral-700">
            <Image 
              source={{ uri: capturedImageUri }} 
              className="w-full h-full"
              resizeMode="cover"
            />
          </View>
          
          <FullScreenStatusIndicator
            error={new Error(ocrError)}
            errorMessage={t('medicines.ocrProcessingFailedMessage', { error: ocrError })}
            onRetry={handleRetry}
          />
          
          {/* Cancel button */}
          <View className="mt-8">
            <Button
              title={t('common.cancel')}
              onPress={handleDismiss}
              variant="outline"
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Show review form with extracted medicines
  if (extractedMedicines.length > 0) {
    return (
      <MedicineReviewForm 
        onComplete={handleDismiss}
      />
    );
  }

  // Show no medicines found state
  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <View className="flex-1 justify-center items-center p-6">
        {/* Show captured image */}
        <View className="w-48 h-48 mb-6 rounded-lg overflow-hidden border border-border dark:border-neutral-700">
          <Image 
            source={{ uri: capturedImageUri }} 
            className="w-full h-full"
            resizeMode="cover"
          />
        </View>
        
        <Text className="text-foreground dark:text-neutral-100 text-xl font-semibold mb-4 text-center">
          {t('medicines.ocrNoMedicinesFound')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 text-center mb-8 px-4 leading-relaxed">
          {t('medicines.ocrNoMedicinesFoundMessage')}
        </Text>
        
        <View className="flex-row gap-4">
          <Button
            title={t('medicines.ocrTryAgain')}
            onPress={handleRetry}
            variant="primary"
          />
          <Button
            title={t('common.cancel')}
            onPress={handleDismiss}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
} 