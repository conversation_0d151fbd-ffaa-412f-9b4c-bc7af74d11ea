import React from 'react';
import { SafeAreaView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { usePatients } from '@/hooks/entities/usePatients';
import PatientForm from '@/components/patients/PatientForm';
import { PatientFormData } from '@/schema/patient';
import { useModalCreationStore } from '@/store/modalCreationStore';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { useTranslation } from '@/hooks/useTranslation';

export default function PatientModalScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const params = useLocalSearchParams<{ id?: string }>();
  const patientId = params.id;
  const isEditing = !!patientId;
  const { setNewlyCreatedPatientId } = useModalCreationStore();
  const { showSuccess, showError } = useAppAlerts();

  const { 
    useFetchPatient, 
    useCreatePatient, 
    useUpdatePatient 
  } = usePatients();

  const { 
    data: patient, 
    isLoading: isLoadingPatient, 
    isError: isFetchError, 
    error: fetchError
  } = useFetchPatient(patientId);

  const createPatientMutation = useCreatePatient();
  const updatePatientMutation = useUpdatePatient();

  const handleSubmit = async (formData: PatientFormData) => {
    try {
      if (!isEditing) {
        const newPatient = await createPatientMutation.mutateAsync(formData);
        if (newPatient && newPatient.id) {
          setNewlyCreatedPatientId(newPatient.id);
        }
        showSuccess(t('patients.createdSuccessfully'));
      } else {
        if (!patientId) return;
        await updatePatientMutation.mutateAsync({ id: patientId, ...formData });
        showSuccess(t('patients.updatedSuccessfully'));
      }
      router.dismiss();
    } catch (error: any) {
      showError(error.message || t('patients.saveFailed', { action: isEditing ? t('common.update') : t('common.create') }));
    }
  };

  if (isLoadingPatient && isEditing) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator isLoading={true} loadingMessage={t('loading.patientDetails')} />
      </SafeAreaView>
    );
  }

  if (isFetchError && isEditing) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator 
          error={fetchError} 
          errorMessage={t('errors.loadingPatientData')} 
          onRetry={() => {
            showError(t('common.retryNotImplemented'));
          }}
        />
      </SafeAreaView>
    );
  }

  return (
   
      <PatientForm
        initialValues={patient}
        onSubmit={handleSubmit}
        isLoading={createPatientMutation.isPending || updatePatientMutation.isPending}
        isEditing={isEditing}
        patientId={patientId}
      />
    
  );
} 