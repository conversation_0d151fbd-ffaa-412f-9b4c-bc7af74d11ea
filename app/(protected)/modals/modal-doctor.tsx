import React from 'react';
import { SafeAreaView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';

import { useUserDoctors } from '@/hooks/entities/useUserDoctors';
import DoctorForm from '@/components/doctors/DoctorForm';
import { DoctorFormData } from '@/schema/doctor';
import { WorkingHours } from '@/schema/working-hours';
import { useModalCreationStore } from '@/store/modalCreationStore';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { useTranslation } from '@/hooks/useTranslation';

export default function DoctorModalScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const params = useLocalSearchParams<{
    id?: string;
    initialName?: string;
    initialSpecialty?: string;
    initialWorkplace?: string;
    initialWorkplaceAddress?: string;
    initialPhoneNumber?: string;
    initialCity?: string;
    initialFee?: string;
    initialNotes?: string;
    initialWorkingHours?: string;
    initialAlldoctorId?: string;
  }>();
  
  const doctorIdFromNav = params.id;
  const isCreatingFromTemplate = !!params.initialName;

  const isEditing = !!doctorIdFromNav && !isCreatingFromTemplate;
  const doctorIdForForm = isEditing ? doctorIdFromNav : undefined;

  const { setNewlyCreatedDoctorId } = useModalCreationStore();
  const { showSuccess, showError } = useAppAlerts();

  const { 
    useFetchUserDoctor, 
    useCreateUserDoctor, 
    useUpdateUserDoctor 
  } = useUserDoctors();

  const { 
    data: existingDoctorData, 
    isLoading: isLoadingDoctor, 
    isError: isFetchError, 
    error: fetchError
  } = useFetchUserDoctor(doctorIdForForm);

  const createDoctorMutation = useCreateUserDoctor();
  const updateDoctorMutation = useUpdateUserDoctor();

  const handleSubmit = async (formData: DoctorFormData) => {
    try {
      if (!isEditing) {
        const newDoctor = await createDoctorMutation.mutateAsync(formData);
        if (newDoctor && newDoctor.id) {
          setNewlyCreatedDoctorId(newDoctor.id);
        }
        showSuccess(t('doctors.addedSuccessfully'));
      } else {
        if (!doctorIdForForm) return; 
        await updateDoctorMutation.mutateAsync({ id: doctorIdForForm, ...formData });
        showSuccess(t('doctors.updatedSuccessfully'));
      }
      router.dismiss();
    } catch (error: any) {
      showError(error.message || t('doctors.saveFailed', { action: isEditing ? t('common.update') : t('common.add') }));
    }
  };

  let formInitialValues: Partial<DoctorFormData> | undefined = undefined;
  if (isCreatingFromTemplate) {
    formInitialValues = {
      name: params.initialName,
      specialty: params.initialSpecialty || undefined,
      workplace: params.initialWorkplace || undefined,
      workplace_address: params.initialWorkplaceAddress || undefined,
      phone_number: params.initialPhoneNumber || undefined,
      city: params.initialCity || undefined,
      notes: params.initialNotes || undefined,
      fee: params.initialFee ? parseFloat(params.initialFee) : undefined,
      working_hours: params.initialWorkingHours 
        ? JSON.parse(params.initialWorkingHours) as WorkingHours 
        : undefined,
      doctor_id: params.initialAlldoctorId || null,
    };
  } else if (isEditing && existingDoctorData) {
    formInitialValues = {
      ...existingDoctorData,
      fee: existingDoctorData.fee === null ? undefined : existingDoctorData.fee,
      specialty: existingDoctorData.specialty === null ? undefined : existingDoctorData.specialty,
      workplace: existingDoctorData.workplace === null ? undefined : existingDoctorData.workplace,
      workplace_address: existingDoctorData.workplace_address === null ? undefined : existingDoctorData.workplace_address,
      phone_number: existingDoctorData.phone_number === null ? undefined : existingDoctorData.phone_number,
      city: existingDoctorData.city === null ? undefined : existingDoctorData.city,
      notes: existingDoctorData.notes === null ? undefined : existingDoctorData.notes,
      working_hours: typeof existingDoctorData.working_hours === 'string' 
        ? JSON.parse(existingDoctorData.working_hours) as WorkingHours 
        : existingDoctorData.working_hours as WorkingHours | null | undefined,
    };
  }

  if (isLoadingDoctor && isEditing) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator isLoading={true} loadingMessage={t('loading.doctorDetails')} />
      </SafeAreaView>
    );
  }

  if (isFetchError && isEditing) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator 
          error={fetchError} 
          errorMessage={t('errors.loadingDoctorData')} 
          onRetry={() => { 
            showError(t('common.retryNotImplemented'));
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <DoctorForm
      initialValues={formInitialValues as any}
      onSubmit={handleSubmit}
      isLoading={createDoctorMutation.isPending || updateDoctorMutation.isPending}
      isEditing={isEditing}
      doctorId={doctorIdForForm}
    />
  );
} 