import React, { useEffect, useState, useMemo, useRef } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';

import LabResultForm, { LabResultWithImage } from '@/components/labresults/LabResultForm';
import { LabResultFormData } from '@/schema/lab-result';
import { useLabResults } from '@/hooks/entities/useLabResults';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { useTranslation } from '@/hooks/useTranslation';

/**
 * Screen for CREATING AND EDITING a lab result (presented modally)
 */
export default function LabResultModalScreen() {
  const { t } = useTranslation();
  const { 
    id, 
    startWithCamera, 
    newAttachmentUri,
    attachmentAction,
    capturedImageUri,
  } = useLocalSearchParams<{ 
    id?: string, 
    startWithCamera?: string, 
    newAttachmentUri?: string,
    attachmentAction?: 'replaceWithPdf' | 'attachCapturedPdf',
    capturedImageUri?: string,
  }>(); 
  const isEditing = !!id;
  const { showSuccess, showError } = useAppAlerts();
  const [isUpdatingWithPdf, setIsUpdatingWithPdf] = useState(false);

  // Ref to track processed attachment actions
  const processedAttachmentKeyRef = useRef<string | null>(null);

  const { 
    useFetchLabResult, 
    useUpdateLabResult, 
    useCreateLabResult 
  } = useLabResults();
  
  const { 
    data: labResult,
    isLoading: isLoadingLabResult,
    isError: isFetchError,
    error: fetchError,
    refetch: refetchLabResult,
  } = useFetchLabResult(id);

  const createLabResultMutation = useCreateLabResult();
  const updateLabResultMutation = useUpdateLabResult();
  
  const initialFormValues: LabResultWithImage | undefined = useMemo(() => {
    if (isEditing && labResult) { 
      return { 
        ...labResult, 
        id: labResult.id,
        lab_name: labResult.lab_name || '',
        password: labResult.password || '',
        patient_id: labResult.patient_id || '',
        patient_reference_id: labResult.patient_reference_id === null ? undefined : labResult.patient_reference_id,
        patient_name: labResult.patient_name || '',
        result_date: labResult.result_date === null ? undefined : labResult.result_date,
        website: labResult.website === null ? undefined : labResult.website,
        phone_number1: labResult.phone_number1 === null ? undefined : labResult.phone_number1,
        phone_number2: labResult.phone_number2 === null ? undefined : labResult.phone_number2,
        phone_number3: labResult.phone_number3 === null ? undefined : labResult.phone_number3,
        image_path: labResult.image_path === null ? undefined : labResult.image_path,
        image_bucket_id: labResult.image_bucket_id === null ? undefined : labResult.image_bucket_id,
        captured_pdf_path: labResult.captured_pdf_path === null ? undefined : labResult.captured_pdf_path,
        captured_pdf_bucket_id: labResult.captured_pdf_bucket_id === null ? undefined : labResult.captured_pdf_bucket_id,
        created_at: labResult.created_at === null ? undefined : labResult.created_at,
        updated_at: labResult.updated_at === null ? undefined : labResult.updated_at,
        user_id: labResult.user_id === null ? undefined : labResult.user_id,
      };
    }
    return undefined;
  }, [isEditing, labResult]);
  
  useEffect(() => {
    const currentAttachmentProcessingKey = newAttachmentUri && attachmentAction ? `${newAttachmentUri}-${attachmentAction}` : null;

    if (currentAttachmentProcessingKey && id && labResult && processedAttachmentKeyRef.current !== currentAttachmentProcessingKey) {
      // Mark as processing this key immediately to prevent re-entry for the same key
      processedAttachmentKeyRef.current = currentAttachmentProcessingKey;
      
      router.setParams({ newAttachmentUri: undefined, attachmentAction: undefined });

      const processAttachment = async () => {
        setIsUpdatingWithPdf(true); 
        try {
          const formDataForUpdate: LabResultFormData = {
            lab_name: labResult.lab_name || '',
            password: labResult.password || '', 
            patient_id: labResult.patient_id || '',
            patient_reference_id: labResult.patient_reference_id === null ? undefined : labResult.patient_reference_id,
            patient_name: labResult.patient_name || '',
            result_date: labResult.result_date === null ? undefined : labResult.result_date,
            website: labResult.website || '',
            phone_number1: labResult.phone_number1 || '',
            phone_number2: labResult.phone_number2 || '',
            phone_number3: labResult.phone_number3 || '',
          };

          if (attachmentAction === 'attachCapturedPdf') {
            await updateLabResultMutation.mutateAsync({
              id,
              data: formDataForUpdate, 
              capturedPdfFileUri: newAttachmentUri, 
              deleteExistingCapturedPdf: true,
              fileUri: undefined,
              deleteFile: false,
            });
            showSuccess(t('labResults.updatedWithPdf'));
          } 
          else if (attachmentAction === 'replaceWithPdf') {
            await updateLabResultMutation.mutateAsync({
              id,
              data: formDataForUpdate, 
              fileUri: newAttachmentUri, 
              deleteFile: true,
              capturedPdfFileUri: undefined,
              deleteExistingCapturedPdf: false,
            });
            showSuccess(t('labResults.replacedWithPdf'));
          }
          refetchLabResult();
        } catch (error: any) {
          console.error(`Error updating lab result with ${attachmentAction}:`, error);
          showError(error.message || t('labResults.updateFailed', { action: attachmentAction }));
          if (processedAttachmentKeyRef.current === currentAttachmentProcessingKey) {
            processedAttachmentKeyRef.current = null;
          }
        } finally {
          setIsUpdatingWithPdf(false); 
        }
      };
      processAttachment();
    } else if (!newAttachmentUri && !attachmentAction && processedAttachmentKeyRef.current) {
      processedAttachmentKeyRef.current = null;
    }
  }, [
    id, 
    newAttachmentUri, 
    attachmentAction, 
    labResult, 
    updateLabResultMutation, 
    showError, 
    showSuccess, 
    refetchLabResult, 
    t
  ]);
  
  const handleSubmit = async (
    data: LabResultFormData,
    primaryFileUri?: string,
    deletePrimaryFile?: boolean,
    localCapturedPdfUri?: string,
    deleteExistingCapturedPdfFromForm?: boolean
  ) => {
    try {
      if (!isEditing) {
        await createLabResultMutation.mutateAsync({
          data,
          fileUri: primaryFileUri,
          capturedPdfFileUri: localCapturedPdfUri,
        });
        showSuccess(t('labResults.createdSuccessfully'));
      } else {
        if (!id) return;
        await updateLabResultMutation.mutateAsync({
          id,
          data,
          fileUri: primaryFileUri,
          deleteFile: deletePrimaryFile,
          capturedPdfFileUri: localCapturedPdfUri,
          deleteExistingCapturedPdf: deleteExistingCapturedPdfFromForm || !!localCapturedPdfUri,
        });
        showSuccess(t('labResults.updatedSuccessfully'));
      }
      router.dismiss();
    } catch (error: any) {
      console.error(`Error ${!isEditing ? 'creating' : 'updating'} lab result:`, error);
      showError(error.message || t('labResults.saveFailed', { action: !isEditing ? t('common.create') : t('common.update') }));
    }
  };



  if (isEditing && isLoadingLabResult) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator isLoading={true} loadingMessage={t('loading.labResultDetails')} />
      </SafeAreaView>
    );
  }

  if (isUpdatingWithPdf) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator isLoading={true} loadingMessage={t('labResults.updatingWithPdf')} />
      </SafeAreaView>
    );
  }


  if (isEditing && (isFetchError || (!isLoadingLabResult && !labResult))) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator 
            error={fetchError || new Error(t('labResults.notFound'))}
            errorMessage={fetchError?.message || t('labResults.loadingFailed')}
            onRetry={() => refetchLabResult()}
        />
      </SafeAreaView>
    );
  }

  return (
    
      <LabResultForm 
        initialValues={initialFormValues}
        onSubmit={handleSubmit}
        isLoading={createLabResultMutation.isPending || updateLabResultMutation.isPending || isUpdatingWithPdf}
        startWithCamera={!isEditing && startWithCamera === 'true'}
        isEditing={isEditing}
        labResultId={id}
        capturedImageUri={capturedImageUri}
      />
    
  );
} 