import React, { useState, useEffect, useLayoutEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  Pressable,
  ActivityIndicator,
  Keyboard,
  Platform,
} from 'react-native';
import { useNavigation, useRouter } from 'expo-router';
import { useTheme } from '@react-navigation/native';
import { useRouterSelectStore } from '@/store/routerSelectStore';
import { RouterSelectOption } from '@/components/common/RouterSelectInput';
import { SearchHeader } from '@/components/common/SearchHeader';
import UserDoctorCard from '@/components/doctors/UserDoctorCard';
import PatientCard from '@/components/patients/PatientCard';
import { useTranslation } from '@/hooks/useTranslation';

export default function RouterSelectScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const navigation = useNavigation();
  const theme = useTheme(); // For theme-aware icon colors if needed
  const {
    title,
    options: initialOptions,
    selectedOption: currentSelectedOptionFromStore,
    onSelect: actualOnSelect,
    onSearch: actualOnSearch,
    isLoading: externalIsLoading,
    showCreateNewButton: externalShowCreateNewButton,
    onCreateNew: actualOnCreateNew,
    placeholder,
    emptyResultsMessage,
    close: closeStoreModal,
    showSearchInModal, // Get the new state
  } = useRouterSelectStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<RouterSelectOption[]>(initialOptions || []);

  // Define handlers before they are used in effects
  const handleSelect = (option: RouterSelectOption) => {
    Keyboard.dismiss();
    if (actualOnSelect) {
      actualOnSelect(option);
    }
    closeStoreModal(); // Clear store state related to this instance
    router.dismiss();
  };

  const handleCreateNew = useCallback(() => {
    Keyboard.dismiss();
    if (actualOnCreateNew) {
      actualOnCreateNew();
    }
    // Decide if creating new dismisses this modal. Often it does, or navigates elsewhere.
    // For now, let's assume it might keep the modal open or navigate from the callback.
    // closeStoreModal();
    // router.dismiss();
  }, [actualOnCreateNew]);

  // Fixed search functionality - removed store updates that caused infinite loop
  const handleSearch = useCallback(
    (query: string) => {
      if (actualOnSearch) {
        // External search - don't manage loading state here to avoid loops
        actualOnSearch(query);
      } else {
        // Handle client-side filtering
        if (!query) {
          setFilteredOptions(initialOptions || []);
        } else {
          const filtered = (initialOptions || []).filter(
            (opt) =>
              opt.label.toLowerCase().includes(query.toLowerCase()) ||
              opt.subtitle?.toLowerCase().includes(query.toLowerCase()),
          );
          setFilteredOptions(filtered);
        }
      }
    },
    [actualOnSearch, initialOptions]
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () =>
        showSearchInModal ? ( // Check the flag here
          <SearchHeader
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            placeholder={placeholder || t('common.search')}
            theme={theme}
            showCreateButton={externalShowCreateNewButton && !!actualOnCreateNew}
            onCreateButtonPress={handleCreateNew}
          />
        ) : (
          // Render a simple text title if search is not shown in modal
          <Text style={{ color: theme.colors.text, fontSize: 17, fontWeight: '600', marginLeft: Platform.OS === 'ios' ? 0 : 16 }}> 
            {title}
          </Text>
        ),
      // headerRight remains undefined as the create button is now part of SearchHeader
    });
  }, [
    navigation,
    searchQuery,
    setSearchQuery,
    placeholder,
    theme,
    externalShowCreateNewButton,
    actualOnCreateNew,
    handleCreateNew,
    title,
    showSearchInModal,
    t
  ]);

  useEffect(() => {
    // Use the enhanced search handler
    handleSearch(searchQuery);
  }, [searchQuery, handleSearch]);
  
  // Reflect changes from store if options are updated externally (e.g. by onSearch)
  useEffect(() => {
    // This effect now correctly updates filteredOptions when initialOptions from the store changes.
    // It also applies client-side filtering if searchQuery is present and no external search is active.
    if (!actualOnSearch && searchQuery) {
        const filtered = (initialOptions || []).filter(
          (opt) =>
            opt.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
            opt.subtitle?.toLowerCase().includes(searchQuery.toLowerCase()),
        );
        setFilteredOptions(filtered);
    } else {
        setFilteredOptions(initialOptions || []);
    }
  }, [initialOptions, searchQuery, actualOnSearch]); // Added searchQuery and actualOnSearch here

  const renderCustomItem = ({ item }: { item: RouterSelectOption }) => {
    const isSelected = item.id === currentSelectedOptionFromStore?.id;

    if ((item.itemType === 'doctor' || item.itemType === 'patient') && item.data) {
      return (
        <View
          className={`
            border-b border-border dark:border-neutral-700
            ${isSelected ? 'border-l-4 border-l-primary' : 'border-l-4 border-l-transparent'}
          `}
        >
          {item.itemType === 'doctor' && (
            <UserDoctorCard
              doctor={item.data}
              onPress={() => handleSelect(item)}
            />
          )}
          {item.itemType === 'patient' && (
            <PatientCard
              patient={item.data}
              onPress={() => handleSelect(item)}
            />
          )}
        </View>
      );
    }

    // Default rendering
    return (
      <Pressable
        onPress={() => handleSelect(item)}
        className={`p-4 border-b border-border dark:border-neutral-700 
          ${item.id === currentSelectedOptionFromStore?.id ? 'bg-primary/10 dark:bg-primary/20' : ''}`}
      >
        <Text
          className={`text-base ${item.id === currentSelectedOptionFromStore?.id
              ? 'text-primary dark:text-primary font-medium'
              : 'text-foreground dark:text-neutral-200'
            }`}
        >
          {item.label}
        </Text>
        {item.subtitle && (
          <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">
            {item.subtitle}
          </Text>
        )}
      </Pressable>
    );
  };

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900" collapsable={false}>
      
      {/* Scrollable content / loading indicator */}
      {externalIsLoading ? (
        <View className="flex-1 py-8 items-center justify-center">
          <ActivityIndicator size="large" className="text-primary" />
        </View>
      ) : (
        <FlatList
          data={filteredOptions}
          keyExtractor={(item) => item.id}
          renderItem={renderCustomItem}
          ListEmptyComponent={() => (
            <View className="flex-1 py-8 items-center justify-center">
              <Text className="text-muted-foreground dark:text-neutral-400">
                {emptyResultsMessage || 'No results found'}
              </Text>
            </View>
          )}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 80 }} // Let FlatList manage its own content growth
          className="flex-1" // Ensure FlatList takes remaining space
        />
      )}
    </View>
  );
}
