import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';

import { usePrescriptions } from '@/hooks/entities/usePrescriptions';
import { PrescriptionForm } from '@/components/prescriptions/PrescriptionForm';
import { PrescriptionInsert, PrescriptionUpdate, Prescription } from '@/schema/prescription';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { useTranslation } from '@/hooks/useTranslation';

export default function PrescriptionModalScreen() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const isEditing = !!id;
  const { showSuccess, showError } = useAppAlerts();
  const { t } = useTranslation();

  const {
    useFetchPrescription,
    useCreatePrescription,
    useUpdatePrescription,
  } = usePrescriptions();

  const { 
    data: existingPrescription, 
    isLoading: isLoadingData, 
    error: fetchError, 
    isError: isFetchError 
  } = useFetchPrescription(id);

  const createPrescriptionMutation = useCreatePrescription();
  const updatePrescriptionMutation = useUpdatePrescription();

  const handleSubmit = async (
    formData: PrescriptionInsert | PrescriptionUpdate,
    frontImageUri?: string,
    backImageUri?: string,
    deleteFrontImage?: boolean,
    deleteBackImage?: boolean
  ) => {
    try {
        if (isEditing && id) {
            await updatePrescriptionMutation.mutateAsync(
                { 
                    id, 
                    data: formData as PrescriptionUpdate,
                    frontImageUri,
                    backImageUri,
                    deleteFrontImage,
                    deleteBackImage,
                }
            );
            showSuccess(t('prescriptions.updatedSuccessfully'));
        } else {
            await createPrescriptionMutation.mutateAsync(
                { 
                    data: formData as PrescriptionInsert,
                    frontImageUri, 
                    backImageUri 
                }
            );
            showSuccess(t('prescriptions.addedSuccessfully'));
        }
        router.dismiss();
    } catch (error: any) {
         console.error(`Error ${!isEditing ? 'creating' : 'updating'} prescription:`, error);
         showError(error.message || t('prescriptions.saveFailed', { action: !isEditing ? t('common.add') : t('common.update') }));
    }
  };

  if (isEditing && isLoadingData) {
    return (
      <SafeAreaView className="flex-1 bg-muted dark:bg-neutral-800">
        <FullScreenStatusIndicator isLoading={true} loadingMessage={t('loading.prescriptionDetails')} />
      </SafeAreaView>
    );
  }

  if (isEditing && (isFetchError || (!isLoadingData && !existingPrescription))) {
     return (
      <SafeAreaView className="flex-1 bg-muted dark:bg-neutral-800">
        <FullScreenStatusIndicator 
            error={fetchError || new Error(t('prescriptions.notFound'))}
            errorMessage={fetchError?.message || t('prescriptions.loadingFailed')}
            onRetry={() => { 
                showError(t('common.retryNotImplemented'));
            }}
        />
      </SafeAreaView>
    );
  }
  
  const initialValues: Partial<Prescription> | undefined = isEditing && existingPrescription 
    ? {
        ...existingPrescription,
        id: existingPrescription.id,
        user_id: existingPrescription.user_id === null ? undefined : existingPrescription.user_id,
        created_at: existingPrescription.created_at === null ? undefined : existingPrescription.created_at,
        updated_at: existingPrescription.updated_at === null ? undefined : existingPrescription.updated_at,
        image_metadata: typeof existingPrescription.image_metadata === 'string' 
            ? JSON.parse(existingPrescription.image_metadata) 
            : existingPrescription.image_metadata === null ? undefined : existingPrescription.image_metadata,
      }
    : undefined;

  return (
  
    
      <PrescriptionForm 
          initialValues={initialValues} 
          onSubmit={handleSubmit}
          isLoading={createPrescriptionMutation.isPending || updatePrescriptionMutation.isPending}
          isEditing={isEditing}
          prescriptionId={id}
      />
  );
} 