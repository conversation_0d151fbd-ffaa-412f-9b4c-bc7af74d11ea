import React, { useState } from "react";
import { View, FlatList, Text, Pressable, TextInput, Alert } from "react-native";
import { Drawer } from "expo-router/drawer";
import { Ionicons } from "@expo/vector-icons";
import { router, usePathname } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import { useChatSessions, ChatSession } from "@/hooks/entities/useChatSessions";
import FullScreenStatusIndicator from "@/components/common/FullScreenStatusIndicator";
import { useTranslation } from "@/hooks/useTranslation";

export const CustomDrawerContent = (props: any) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const pathname = usePathname();

  // Use TanStack Query hooks
  const { useFetchChatSessions, useCreateChatSession, useDeleteChatSession } = useChatSessions();

  const {
    data: sessions = [],
    isLoading: loading,
  } = useFetchChatSessions();

  const createSessionMutation = useCreateChatSession();
  const deleteSessionMutation = useDeleteChatSession();

  const filteredSessions = sessions.filter((session) => {
    const matchesSearch = session.title
      ?.toLowerCase()
      .includes(searchQuery.toLowerCase()) || false;
    const matchesType = filterType === "all" || session.chat_type === filterType;
    return matchesSearch && matchesType;
  });

  const handleCreateNewChat = async (type: "general" | "lab_result" | "medication") => {
    try {
      const newSession = await createSessionMutation.mutateAsync({
        type,
        title: `New ${type === "general" ? "Chat" : type === "lab_result" ? "Lab Analysis" : "Medication Analysis"}`
      });
      router.push(`/(ai-chat)/${newSession.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToCreateChat'));
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    Alert.alert(
              t('aiChat.deleteChat'),
      "Are you sure you want to delete this chat? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteSessionMutation.mutateAsync(sessionId);
              
              // ✅ FIXED: Check if we're currently viewing the deleted session
              // If so, navigate back to welcome or close drawer
              if (pathname.includes(sessionId)) {
                // Navigate back to welcome within the drawer
                router.replace("/(ai-chat)/welcome");
              }
            } catch (error) {
              console.error("Failed to delete session:", error);
              Alert.alert(t('common.error'), t('aiChat.failedToDeleteChat'));
            }
          },
        },
      ]
    );
  };

  const getSessionIcon = (chatType: string) => {
    switch (chatType) {
      case "lab_result":
        return <Ionicons name="document-text-outline" size={20} color="#3B82F6" />;
      case "medication":
        return <Ionicons name="medical-outline" size={20} color="#10B981" />;
      default:
        return <Ionicons name="chatbubble-outline" size={20} color="#8B5CF6" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "Today";
    if (diffDays === 2) return "Yesterday";
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      {/* ✅ FULL SCREEN LOADING during deletion */}
      {deleteSessionMutation.isPending ? (
        <FullScreenStatusIndicator 
          isLoading={true} 
          loadingMessage="Deleting session..." 
        />
      ) : (
        <>
          {/* Header */}
          <View className="p-4 border-b border-border dark:border-neutral-700">
            <Text className="text-xl font-bold mb-4 text-foreground dark:text-neutral-100">AI Health Assistant</Text>

            {/* Search Bar */}
            <View className="flex-row items-center bg-muted dark:bg-neutral-800 rounded-lg px-3 py-2 mb-4 border border-input dark:border-neutral-700">
              <Ionicons name="search-outline" size={20} className="text-muted-foreground dark:text-neutral-400 mr-2" />
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder={t('aiChat.searchChats')}
                placeholderTextColor="#9CA3AF"
                className="flex-1 text-foreground dark:text-neutral-100"
              />
            </View>

            {/* New Chat Buttons */}
            <View className="mb-4">
              <Pressable
                className="flex-row items-center p-3 bg-primary/10 dark:bg-primary/20 rounded-lg border border-primary/20 dark:border-primary/30"
                onPress={() => handleCreateNewChat("general")}
              >
                <Ionicons name="chatbubble-outline" size={20} className="text-primary mr-3" />
                <Text className="text-primary font-medium">New General Chat</Text>
              </Pressable>
            </View>

            {/* Filter Tabs */}
            <View className="flex-row flex-wrap gap-2">
              {[
                { key: "all", label: "All", count: sessions.length },
                { key: "general", label: "General", count: sessions.filter(s => s.chat_type === "general").length },
                { key: "lab_result", label: "Lab Results", count: sessions.filter(s => s.chat_type === "lab_result").length },
                { key: "medication", label: "Medications", count: sessions.filter(s => s.chat_type === "medication").length },
              ].map((filter) => (
                <Pressable
                  key={filter.key}
                  className={`px-3 py-2 rounded-full ${
                    filterType === filter.key 
                      ? "bg-primary" 
                      : "bg-muted dark:bg-neutral-800 border border-border dark:border-neutral-700"
                  }`}
                  onPress={() => setFilterType(filter.key)}
                >
                  <Text
                    className={`text-sm ${
                      filterType === filter.key
                        ? "text-primary-foreground"
                        : "text-muted-foreground dark:text-neutral-400"
                    }`}
                  >
                    {filter.label} ({filter.count})
                  </Text>
                </Pressable>
              ))}
            </View>
          </View>

          {/* Sessions List */}
          <DrawerContentScrollView
            {...props}
            contentContainerStyle={{ paddingTop: 0 }}
          >
            {loading ? (
              <View className="p-4 items-center">
                <Text className="text-muted-foreground dark:text-neutral-400">Loading chats...</Text>
              </View>
            ) : filteredSessions.length === 0 ? (
              <View className="p-4 items-center">
                <Ionicons name="chatbubbles-outline" size={48} className="text-muted-foreground dark:text-neutral-500 mb-4" />
                <Text className="text-muted-foreground dark:text-neutral-400 text-center font-medium">
                  {searchQuery ? t('aiChat.noChatsMatch') : t('aiChat.noChatsYet')}
                </Text>
                <Text className="text-sm text-muted-foreground dark:text-neutral-500 text-center mt-2">
                  Create a new chat to get started
                </Text>
              </View>
            ) : (
              <FlatList
                data={filteredSessions}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <ChatSessionItem
                    session={item}
                    onPress={() => router.push(`/(ai-chat)/${item.id}` as any)}
                    onDelete={() => handleDeleteSession(item.id)}
                    formatDate={formatDate}
                    getSessionIcon={getSessionIcon}
                    t={t}
                  />
                )}
                scrollEnabled={false}
              />
            )}
          </DrawerContentScrollView>
        </>
      )}
    </SafeAreaView>
  );
};

const ChatSessionItem = ({
  session,
  onPress,
  onDelete,
  formatDate,
  getSessionIcon,
  t,
}: {
  session: ChatSession;
  onPress: () => void;
  onDelete: () => void;
  formatDate: (date: string | null) => string;
  getSessionIcon: (chatType: string) => React.ReactNode;
  t: (key: string) => string;
}) => {
  const { useDeleteChatSession } = useChatSessions();
  const deleteSessionMutation = useDeleteChatSession();
  const isDeleting = deleteSessionMutation.isPending;

  return (
    <Pressable
      className={`flex-row items-center p-4 border-b border-border/30 dark:border-neutral-700/30 active:bg-muted/50 dark:active:bg-neutral-800/50 ${isDeleting ? 'opacity-50' : ''}`}
      onPress={onPress}
      disabled={isDeleting}
    >
      <View className="mr-3">
        {getSessionIcon(session.chat_type)}
      </View>

      <View className="flex-1">
        <Text className="font-medium text-foreground dark:text-neutral-100" numberOfLines={1}>
                              {isDeleting ? t('aiChat.deleting') : session.title || t('aiChat.untitledChat')}
        </Text>
        <View className="flex-row items-center mt-1">
          <Text className="text-xs text-muted-foreground dark:text-neutral-400 capitalize">
            {session.chat_type.replace("_", " ")}
          </Text>
          <Text className="text-xs text-muted-foreground dark:text-neutral-400 mx-1">•</Text>
          <Text className="text-xs text-muted-foreground dark:text-neutral-400">
            {formatDate(session.updated_at)}
          </Text>
        </View>
      </View>

      <Pressable
        onPress={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="p-2 ml-2"
        disabled={isDeleting}
      >
        <Ionicons 
          name={isDeleting ? "hourglass-outline" : "trash-outline"} 
          size={16} 
          className="text-destructive" 
        />
      </Pressable>
    </Pressable>
  );
};

const DrawerLayout = () => {
  return (
    <Drawer
      drawerContent={CustomDrawerContent}
      screenOptions={{
        headerShown: true,
        drawerStyle: { width: "85%" },
        drawerType: "front",
        headerRight: () => (
          <Pressable className='mr-4' onPress={() => router.replace("/(protected)/(tabs)")}>
            <Ionicons name="close" size={24} className="text-foreground dark:text-neutral-100" />
          </Pressable>
        ),
      }}
    >
      <Drawer.Screen
        name="[sessionId]"
        options={{
          drawerLabel: () => null, // Hide from drawer menu
        }}
      />
      <Drawer.Screen
        name="welcome"
        options={{
          drawerLabel: () => null, // Hide from drawer menu
        }}
      />
    </Drawer>
  );
};

export default DrawerLayout;