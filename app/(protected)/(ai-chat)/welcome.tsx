import React from "react";
import { View, Text, Pressable } from "react-native";
import { Stack, router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useChatSessions } from "@/hooks/entities/useChatSessions";
import { useTranslation } from '@/hooks/useTranslation';

export default function WelcomeScreen() {
  const { t } = useTranslation();
  const { useCreateChatSession } = useChatSessions();
  const createSessionMutation = useCreateChatSession();

  const handleStartGeneralChat = async () => {
    try {
      const session = await createSessionMutation.mutateAsync({
        type: "general",
        title: t('aiChat.generalChat')
      });
      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t('aiChat.title'),
          headerBackTitle: t('common.back'),
        }}
      />

      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <View className="items-center max-w-sm">
          <View className="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full items-center justify-center mb-6">
            <Ionicons name="medical" size={40} className="text-primary" />
          </View>

          <Text className="text-2xl font-bold text-center mb-4 text-foreground dark:text-neutral-100">
            {t('aiChat.title')}
          </Text>

          <Text className="text-base text-center mb-8 max-w-sm text-muted-foreground dark:text-neutral-400 leading-6">
            {t('aiChat.welcomeMessage')}
          </Text>

          <Pressable
            className="bg-primary px-8 py-4 rounded-lg flex-row items-center space-x-2"
            onPress={handleStartGeneralChat}
          >
            <Ionicons name="chatbubble-outline" size={20} color="white" />
            <Text className="text-primary-foreground font-medium text-lg ml-2">
              {t('aiChat.startConversation')}
            </Text>
          </Pressable>

          <View className="mt-8 p-4 bg-muted dark:bg-neutral-800 rounded-lg border border-border dark:border-neutral-700">
            <View className="flex-row items-start">
              <Ionicons name="warning-outline" size={16} className="text-orange-500 mt-0.5 mr-2" />
              <Text className="text-xs text-muted-foreground dark:text-neutral-400 flex-1 leading-4">
                {t('aiChat.disclaimer')}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </>
  );
} 