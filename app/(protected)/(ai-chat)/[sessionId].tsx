import React, { useState, useEffect, useRef, useCallback } from "react";
import { View, Alert, Text, SafeAreaView, ActivityIndicator } from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { ChatInterface } from "@/components/chat/chat-interface";
import { Message } from "@/components/chat/chat-message";
import { WelcomeMessage } from "@/components/chat/welcome-message";
import { useAIChat } from "@/hooks/useAIChat";
import { useChatSessions } from "@/hooks/entities/useChatSessions";
import { useAnalyses } from "@/hooks/entities/useAnalyses";
import HeaderDropDown from "@/components/HeaderDropDown";
import FullScreenStatusIndicator from "@/components/common/FullScreenStatusIndicator";
import { useTranslation } from "@/hooks/useTranslation";


export default function ChatScreen() {
  const { t } = useTranslation();
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();
  
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState("");
  const [currentUserMessage, setCurrentUserMessage] = useState<Message | null>(null);
  
  // Track if analysis message has been created to prevent duplicates
  const analysisMessageCreatedRef = useRef(false);
  const analysisTriggeredRef = useRef(false);

  const { sendMessage } = useAIChat();
  const { useFetchLabResultAnalysis, useFetchMedicationAnalysis, useCreateLabAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useFetchSession, useFetchMessages, useCreateMessage, useDeleteChatSession } = useChatSessions();

  // Fetch session data
  const {
    data: session,
    isLoading: isLoadingSession,
    error: sessionError,
  } = useFetchSession(sessionId);

  // Fetch messages
  const {
    data: chatMessages = [],
    isLoading: isLoadingMessages,
    error: messagesError,
    refetch: refetchMessages,
  } = useFetchMessages(sessionId);

  // Conditional analysis fetching based on session type
  const shouldFetchLabAnalysis = session?.chat_type === "lab_result" && session.context_id;
  const shouldFetchMedicationAnalysis = session?.chat_type === "medication" && session.context_id;

  const { data: labAnalysis } = useFetchLabResultAnalysis(
    shouldFetchLabAnalysis ? session.context_id! : undefined
  );

  const { data: medicationAnalysis } = useFetchMedicationAnalysis(
    shouldFetchMedicationAnalysis ? session.context_id! : undefined
  );

  // Mutations
  const labAnalysisMutation = useCreateLabAnalysis();
  const medicationAnalysisMutation = useCreateMedicationAnalysis();
  const createMessageMutation = useCreateMessage();
  const deleteSessionMutation = useDeleteChatSession();

  const handleSendMessage = useCallback(async (content: string) => {
    if (!sessionId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content,
      createdAt: new Date(),
    };

    // Set user message and reset streaming content
    setCurrentUserMessage(userMessage);
    setStreamingContent("");
    setIsStreaming(true);

    try {
      const stream = await sendMessage(sessionId, content);
      const reader = stream.getReader();
      const decoder = new TextDecoder();

      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // ✅ FIXED: Accumulate chunks properly for SSE parsing
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        
        // Keep the last incomplete line in buffer
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const jsonStr = line.slice(6).trim();
              if (jsonStr) {
                const data = JSON.parse(jsonStr);
                
                if (data.error) {
                  console.error("Stream error:", data.error);
                  throw new Error(data.error);
                }
                
                if (data.done) {
                  // ✅ Stream completed successfully
                  break;
                }
                
                if (data.content) {
                  setStreamingContent(prev => prev + data.content);
                }
              }
            } catch (e) {
              console.warn("Failed to parse SSE data:", line, e);
              continue;
            }
          }
        }
      }

      // Refetch messages to get the final saved messages
      await refetchMessages();
      
      // Clear streaming state
      setCurrentUserMessage(null);
      setStreamingContent("");

    } catch (error) {
      console.error("Chat error:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToSendMessage'));
      
      // Clear streaming state on error
      setCurrentUserMessage(null);
      setStreamingContent("");
    } finally {
      setIsStreaming(false);
    }
  }, [sessionId, sendMessage, refetchMessages, t]);

  // Enhanced message creation functionality - used for manual message insertion
  const handleCreateMessage = useCallback(async (content: string, role: "user" | "assistant" = "user") => {
    if (!sessionId || createMessageMutation.isPending) return;

    try {
      await createMessageMutation.mutateAsync({
        sessionId,
        content,
        role,
      });
      
      // Refresh messages after creation
      await refetchMessages();
    } catch (error) {
      console.error("Failed to create message:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToSendMessage'));
    }
  }, [sessionId, createMessageMutation, refetchMessages, t]);

  // Function to manually add system messages or analysis results
  const handleAddSystemMessage = useCallback((content: string) => {
    handleCreateMessage(content, "assistant");
  }, [handleCreateMessage]);

  // Enhanced function to add system messages with context
  const addAnalysisSystemMessage = useCallback((analysisContent: string, analysisType: 'lab_result' | 'medication') => {
    const systemMessage = `🔬 **${analysisType === 'lab_result' ? 'Lab Result' : 'Medication'} Analysis Complete**\n\n${analysisContent}\n\n*You can ask follow-up questions about this analysis.*`;
    handleAddSystemMessage(systemMessage);
  }, [handleAddSystemMessage]);

  // Auto-trigger analysis when needed (only once)
  useEffect(() => {
    if (!session || analysisTriggeredRef.current) return;

    const shouldTriggerLabAnalysis = 
      session.chat_type === "lab_result" && 
      session.context_id && 
      !labAnalysis && 
      !labAnalysisMutation.isPending &&
      chatMessages.length === 0;

    const shouldTriggerMedicationAnalysis = 
      session.chat_type === "medication" && 
      session.context_id && 
      !medicationAnalysis && 
      !medicationAnalysisMutation.isPending &&
      chatMessages.length === 0;

    if (shouldTriggerLabAnalysis) {
      analysisTriggeredRef.current = true;
      labAnalysisMutation.mutate({ 
        labResultId: session.context_id!, 
        sessionId: session.id 
      });
    } else if (shouldTriggerMedicationAnalysis) {
      analysisTriggeredRef.current = true;
      medicationAnalysisMutation.mutate({ 
        medicationId: session.context_id!, 
        sessionId: session.id 
      });
    }
  }, [session, labAnalysis, medicationAnalysis, chatMessages.length, labAnalysisMutation, medicationAnalysisMutation]);

  // Auto-stream first analysis message or add system message (only once)  
  useEffect(() => {
    if (!session || analysisMessageCreatedRef.current || isStreaming) return;

    const analysis = session.chat_type === "lab_result" ? labAnalysis : medicationAnalysis;
    
    const shouldStreamFirstMessage = 
      analysis?.ai_analysis && 
      chatMessages.length === 0 && 
      !labAnalysisMutation.isPending &&
      !medicationAnalysisMutation.isPending;

    if (shouldStreamFirstMessage) {
      analysisMessageCreatedRef.current = true;
      
      // Option 1: Stream the analysis (current behavior)
      const analysisPrompt = `Please provide a comprehensive summary of the analysis results.`;
      handleSendMessage(analysisPrompt);
      
      // Option 2: Add as system message for immediate display (alternative)
      // addAnalysisSystemMessage(analysis.ai_analysis, session.chat_type as 'lab_result' | 'medication');
    }
  }, [session, labAnalysis, medicationAnalysis, chatMessages.length, labAnalysisMutation.isPending, medicationAnalysisMutation.isPending, isStreaming, handleSendMessage, addAnalysisSystemMessage]);

  const handleDeleteSession = async () => {
    if (!sessionId) return;

    try {
      await deleteSessionMutation.mutateAsync(sessionId);
      
      // ✅ FIXED: Proper navigation for drawer-based chat
      // Use router.back() to properly exit the drawer context
      router.back();
    } catch (error) {
      console.error("Failed to delete session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToDeleteSession'));
    }
  };

  const getWelcomeComponent = () => {
    if (!session) return null;

    let chatType: "general" | "lab_result" | "medication" = "general";
    if (session.chat_type === "lab_result") chatType = "lab_result";
    else if (session.chat_type === "medication") chatType = "medication";

    return <WelcomeMessage chatType={chatType} />;
  };

  // Determine if we should show analysis loading state in the chat interface
  const isAnalyzing = labAnalysisMutation.isPending || medicationAnalysisMutation.isPending;

  // Show loading state when session or initial messages are loading
  if (isLoadingSession || (isLoadingMessages && chatMessages.length === 0)) {
    return (
      <View className="flex-1 bg-background dark:bg-neutral-900">
        <SafeAreaView className="flex-1">
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" className="text-primary" />
            <Text className="mt-4 text-muted-foreground dark:text-neutral-400">
              {isLoadingSession ? t('aiChat.loadingSession') : t('aiChat.loadingMessages')}
            </Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  if (sessionError || messagesError) {
    return (
      <View className="flex-1 bg-background dark:bg-neutral-900">
        <Stack.Screen
          options={{
            title: "Error",
            headerBackTitle: "Back",
          }}
        />
        <View className="flex-1 justify-center items-center">
          <Text className="text-destructive dark:text-red-400">
            Failed to load chat
          </Text>
        </View>
      </View>
    );
  }

  // Create formatted messages with proper ordering
  const databaseMessages: Message[] = (chatMessages || []).map((msg) => ({
    id: msg.id,
    role: msg.role as "user" | "assistant" | "system",
    content: msg.content,
    createdAt: new Date(msg.created_at || Date.now()),
  }));

  // Add streaming messages if active
  const streamingMessages: Message[] = [];
  if (isStreaming && currentUserMessage) {
    streamingMessages.push(currentUserMessage);
    
    if (streamingContent) {
      streamingMessages.push({
        id: `streaming-${currentUserMessage.id}`,
        role: "assistant",
        content: streamingContent,
        createdAt: new Date(),
      });
    }
  }

  // Combine all messages in correct order
  const allMessages = [...databaseMessages, ...streamingMessages];

  // Show analyzing state as typing indicator, but avoid duplicate indicators
  // Only show typing indicator if:
  // 1. Analyzing and no messages yet, OR  
  // 2. Streaming but no streaming content visible yet
  const shouldShowTypingIndicator = (isAnalyzing && allMessages.length === 0) || (isStreaming && !streamingContent);

  return (
    <>
      <Stack.Screen
        options={{
          headerBackTitle: "Back",
          headerTitle: () => (
            <HeaderDropDown
              title={session?.title || "AI Chat"}
              icon="chevron-down"
              selected=""
              onSelect={(key) => {
                if (key === "delete") {
                  Alert.alert(
                    t('aiChat.deleteSession'),
                    "Are you sure you want to delete this chat session?",
                    [
                      { text: "Cancel", style: "cancel" },
                      {
                        text: "Delete",
                        style: "destructive",
                        onPress: handleDeleteSession,
                      },
                    ]
                  );
                }
              }}
              items={[
                { key: "delete", title: "Delete", icon: "trash-outline" },
              ]}
            />
          ),
        }}
      />

      {/* ✅ FULL SCREEN LOADING during deletion */}
      {deleteSessionMutation.isPending ? (
        <FullScreenStatusIndicator 
          isLoading={true} 
          loadingMessage="Deleting session..." 
        />
      ) : (
        <ChatInterface
          messages={allMessages}
          onSendMessage={handleSendMessage}
          isLoading={shouldShowTypingIndicator}
          welcomeComponent={
            allMessages.length === 0 && !isAnalyzing && session?.chat_type === "general" ? getWelcomeComponent() : undefined
          }
                          placeholder={t('placeholders.typeMessage')}
        />
      )}
    </>
  );
} 