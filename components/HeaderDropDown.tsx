import { View, Text } from 'react-native'
import React from 'react'
import * as DropdownMenu from 'zeego/dropdown-menu'
import { SFSymbol } from 'sf-symbols-typescript'
import { Ionicons } from '@expo/vector-icons'

export type HeaderDropDownProps = {
  title: string;
  icon: string;
  selected?: string;
  onSelect:(key:string) => void;
  items: {key:string, title:string, icon: SFSymbol | (string & {})}[];
}

const HeaderDropDown = ({title, selected, onSelect, items,icon}: HeaderDropDownProps) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <View className='flex-row items-center gap-2'>
          <Text className='text-xl font-medium'>{title}</Text>
          <Ionicons name={icon as keyof typeof Ionicons.glyphMap} size={24} className="text-foreground dark:text-neutral-100" />
        </View>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        {items.map((item) => (
          <DropdownMenu.Item 
            key={item.key}
            onSelect={() => onSelect(item.key)}>
           <DropdownMenu.ItemTitle>{item.title} </DropdownMenu.ItemTitle>
           <DropdownMenu.ItemIcon
            ios={{
              name: item.icon as SFSymbol,
              pointSize: 16,
            }}
           />
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  )
}

export default HeaderDropDown