import React from 'react';
import { 
  Controller, 
  FieldValues, 
  Path, 
  PathValue, 
  UseControllerProps
} from 'react-hook-form';
import { TextInputProps } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Input from './ui/Input';

interface ControlledInputProps<TFieldValues extends FieldValues> 
  extends Omit<TextInputProps, 'onChange' | 'onBlur' | 'value' | 'defaultValue'>,
          Pick<UseControllerProps<TFieldValues>, 'name' | 'control' | 'rules' | 'defaultValue'> {
  label?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
}

function ControlledInput<TFieldValues extends FieldValues>({
  control,
  name,
  label,
  leftIcon,
  rules,
  defaultValue,
  ...textInputProps
}: ControlledInputProps<TFieldValues>) {
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue as PathValue<TFieldValues, Path<TFieldValues>>}
      render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => {
        const stringValue =
          value === null || value === undefined ? '' : String(value);

        return (
          <Input
            label={label}
            leftIcon={leftIcon}
            onBlur={onBlur}
            onChangeText={onChange}
            value={stringValue}
            error={error?.message}
            {...textInputProps}
          />
        );
      }}
    />
  );
}

export default ControlledInput;