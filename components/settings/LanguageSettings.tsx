import React from 'react';
import { View, Text, Pressable, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '@/hooks/useTranslation';
import { getAvailableLanguages } from '@/i18n';
import Card from '@/components/ui/Card';

export function LanguageSettings() {
  const { t, currentLanguage, changeLanguage } = useTranslation();
  const availableLanguages = getAvailableLanguages();

  const handleLanguageChange = async (languageCode: string) => {
    if (languageCode === currentLanguage) return;

    try {
      await changeLanguage(languageCode);
      Alert.alert(
        t('common.success'),
        t('settings.languageChangeSuccess', { 
          language: availableLanguages.find(lang => lang.code === languageCode)?.nativeName 
        })
      );
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(
        t('common.error'),
        t('settings.languageChangeError')
      );
    }
  };

  return (
    <Card title={t('settings.language')} style={{ padding: 16 }}>
      <View className="space-y-3">
        {availableLanguages.map((language) => {
          const isSelected = currentLanguage.startsWith(language.code);
          
          return (
            <Pressable
              key={language.code}
              onPress={() => handleLanguageChange(language.code)}
              className={`flex-row items-center justify-between p-4 rounded-lg border ${
                isSelected 
                  ? 'bg-primary/10 border-primary dark:bg-primary/20 dark:border-primary' 
                  : 'bg-muted border-border dark:bg-neutral-800 dark:border-neutral-700'
              }`}
            >
              <View className="flex-row items-center flex-1">
                <Text className="text-lg font-medium text-foreground dark:text-neutral-100">
                  {language.nativeName}
                </Text>
                <Text className="text-sm text-muted-foreground dark:text-neutral-400 ml-2">
                  ({language.name})
                </Text>
              </View>
              
              {isSelected && (
                <Ionicons 
                  name="checkmark-circle" 
                  size={24} 
                  className="text-primary" 
                />
              )}
            </Pressable>
          );
        })}
      </View>
      
      <View className="mt-4 p-3 bg-muted dark:bg-neutral-800 rounded-lg">
        <Text className="text-sm text-muted-foreground dark:text-neutral-400">
          {t('settings.languageNote')}
        </Text>
      </View>
    </Card>
  );
} 