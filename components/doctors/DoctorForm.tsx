import React, { useLayoutEffect, useCallback } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { useForm, Resolver, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput';
import { Button } from '../ui/Button';
import { doctorFormSchema, DoctorFormData } from '@/schema/doctor';
import { UserDoctor, useUserDoctors } from '@/hooks/entities/useUserDoctors';
import WorkingHoursSelector from '../WorkingHoursSelector';
import { WorkingHours } from '@/schema/working-hours';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { useTranslation } from '@/hooks/useTranslation';

interface DoctorFormProps {
  initialValues?: UserDoctor;
  onSubmit: (data: Doctor<PERSON>ormData) => void;
  isLoading?: boolean;
  isEditing: boolean;
  doctorId?: string;
}

const DoctorForm: React.FC<DoctorFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  isEditing,
  doctorId,
}) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeleteUserDoctor } = useUserDoctors();
  const deleteDoctorMutation = useDeleteUserDoctor();

  const { control, handleSubmit, setValue, watch, getValues } = useForm<DoctorFormData>({
    resolver: zodResolver(doctorFormSchema) as Resolver<DoctorFormData>,
    defaultValues: initialValues ? {
      name: initialValues.name,
      specialty: initialValues.specialty ?? undefined,
      workplace: initialValues.workplace ?? undefined,
      workplace_address: initialValues.workplace_address ?? undefined,
      phone_number: initialValues.phone_number ?? undefined,
      city: initialValues.city ?? undefined,
      notes: initialValues.notes ?? undefined,
      fee: initialValues.fee ?? undefined,
      working_hours: (initialValues.working_hours as WorkingHours | null) ?? null,
      doctor_id: initialValues.doctor_id ?? undefined,
    } : {
      name: '',
      specialty: undefined,
      workplace: undefined,
      workplace_address: undefined,
      phone_number: undefined,
      city: undefined,
      notes: undefined,
      fee: undefined,
      working_hours: null,
      doctor_id: undefined,
    },
  });

  const handleDeletePress = useCallback(() => {
    if (!doctorId) return;

    confirm({
      title: t('alerts.confirmDelete'),
      message: t('alerts.deleteDoctorMessage'),
      confirmText: t('common.delete'),
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deleteDoctorMutation.mutateAsync({ id: doctorId });
          showSuccess(t('alerts.deleteSuccess'), t('common.delete'));
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from DoctorForm:', error);
          showError(error.message || t('doctors.deleteError'));
        }
      },
    });
  }, [doctorId, confirm, deleteDoctorMutation, showSuccess, showError, router, t]);

  // Smart form features using setValue, watch, and getValues
  const watchWorkplace = watch('workplace');
  const watchCity = watch('city');
  const watchPhoneNumber = watch('phone_number');
  
  // Auto-format phone number when user enters it
  const formatPhoneNumber = React.useCallback((value: string) => {
    if (!value) return value;
    
    // Preserve leading '+' if present
    const hasPlus = value.startsWith('+');
    // Remove all non-digits except preserve the leading '+'
    const cleanValue = hasPlus ? '+' + value.slice(1).replace(/\D/g, '') : value.replace(/\D/g, '');
    const phoneNumber = hasPlus ? cleanValue.slice(1) : cleanValue; // digits only for length check
    
    // Validate phone number length (ITU-T E.164 standard allows up to 15 digits)
    const MAX_PHONE_DIGITS = 15;
    if (phoneNumber.length > MAX_PHONE_DIGITS) {
      // Return the original value truncated to reasonable length
      const truncated = hasPlus ? '+' + phoneNumber.substring(0, MAX_PHONE_DIGITS) : phoneNumber.substring(0, MAX_PHONE_DIGITS);
      return truncated;
    }
    
    // Format based on length and presence of country code
    if (hasPlus) {
      // International format with country code
      if (phoneNumber.length >= 10) {
        const countryCode = phoneNumber.substring(0, phoneNumber.length - 10);
        const number = phoneNumber.substring(phoneNumber.length - 10);
        return `+${countryCode} ${number.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3')}`;
      }
      // Return as-is for shorter international numbers
      return cleanValue;
    } else {
      // Domestic format
      if (phoneNumber.length === 10) {
        return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
      }
      // Partial formatting for numbers less than 10 digits
      if (phoneNumber.length >= 6) {
        return phoneNumber.replace(/(\d{3})(\d{3})(\d+)/, '($1) $2-$3');
      }
      if (phoneNumber.length >= 3) {
        return phoneNumber.replace(/(\d{3})(\d+)/, '($1) $2');
      }
      return phoneNumber;
    }
  }, []);

  // Auto-format phone number when it changes
  React.useEffect(() => {
    if (watchPhoneNumber && watchPhoneNumber.length > 0) {
      const formatted = formatPhoneNumber(watchPhoneNumber);
      if (formatted !== watchPhoneNumber) {
        setValue('phone_number', formatted, { shouldValidate: true });
      }
    }
  }, [watchPhoneNumber, formatPhoneNumber, setValue]);

  // Auto-complete workplace address based on workplace name and city
  React.useEffect(() => {
    const currentValues = getValues();
    if (watchWorkplace && watchCity && !currentValues.workplace_address) {
      // Auto-suggest workplace address format
      const suggestedAddress = `${watchWorkplace}, ${watchCity}`;
      setValue('workplace_address', suggestedAddress, { shouldValidate: false });
    }
  }, [watchWorkplace, watchCity, setValue, getValues]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? t('buttons.editDoctor') : t('buttons.addDoctor'),
      headerRight: () => (
        isEditing && doctorId && (
          <Pressable onPress={handleDeletePress} disabled={deleteDoctorMutation.isPending} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash-outline"
              size={24}
              color={deleteDoctorMutation.isPending ? 'gray' : 'red'} 
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, doctorId, deleteDoctorMutation.isPending, handleDeletePress, t]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <ScrollView 
        className="flex-1 px-4" 
        contentContainerStyle={{ paddingVertical: 24 }}
        keyboardShouldPersistTaps="handled"
      >
        <ControlledInput
          control={control}
          name="name"
          label={t('fields.name')}
          placeholder={t('placeholders.doctorName')}
          leftIcon="person"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="specialty"
          label={t('fields.specialty')}
          placeholder={t('placeholders.specialty')}
          leftIcon="medkit-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="workplace"
          label={t('fields.workplace')}
          placeholder={t('placeholders.workplace')}
          leftIcon="business-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="workplace_address"
          label={t('fields.address')}
          placeholder={t('placeholders.enterAddress')}
          leftIcon="location-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="phone_number"
          label={t('fields.phone')}
          placeholder={t('placeholders.enterPhone')}
          leftIcon="call-outline"
          keyboardType="phone-pad"
        />
        <ControlledInput
          control={control}
          name="city"
          label={t('fields.city')}
          placeholder={t('placeholders.enterCity')}
          leftIcon="map-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="fee"
          label={t('fields.fee')}
          placeholder={t('placeholders.enterFee')}
          leftIcon="cash-outline"
          keyboardType="decimal-pad"
        />
        <Controller
          control={control}
          name="working_hours"
          render={({ field: { onChange, value } }) => (
            <WorkingHoursSelector
              value={value as WorkingHours | null | undefined}
              onChange={onChange}
            />
          )}
        />
        <ControlledInput
          control={control}
          name="notes"
          label={t('fields.notes')}
          placeholder={t('placeholders.enterNotes')}
          leftIcon="document-text-outline"
          multiline
          numberOfLines={3}
          style={{ height: 80, paddingTop: 8 }} 
        />
      </ScrollView>
      <View className="px-4 py-3 border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900">
        <Button
          title={isEditing ? t('buttons.editDoctor') : t('buttons.addDoctor')}
          onPress={handleSubmit(onSubmit)}
          isLoading={isLoading}
          icon={isEditing ? "save" : "add-circle"}
          fullWidth
        />
      </View>
    </View>
  );
};

export default DoctorForm; 