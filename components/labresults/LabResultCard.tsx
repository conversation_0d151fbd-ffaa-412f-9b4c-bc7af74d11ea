import React from 'react';
import { View, Text, Alert } from 'react-native';
import { format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as Linking from 'expo-linking';
import Card from '../ui/Card';
import { LabResultWithPatient } from '@/hooks/entities/useLabResults';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface LabResultCardProps {
  result: LabResultWithPatient;
  onPress?: () => void;
}

/**
 * A card component for displaying a lab result in a list.
 */
const LabResultCard: React.FC<LabResultCardProps> = ({ result, onPress }) => {
  const { t } = useTranslation();
  
  // Format date if available
  const formattedDate = result.result_date
    ? format(new Date(result.result_date), 'MMM d, yyyy')
    : t('misc.noDate');

  const { confirm } = useAppAlerts();

  // Use TanStack Query hooks
  const { useFetchLabResultAnalysis, useCreateLabAnalysis } = useAnalyses();
  const { useFetchSessionByContext, useCreateChatSession } = useChatSessions();

  // Query for existing analysis - THIS IS ALL WE NEED!
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchLabResultAnalysis(result.id);

  // Enhanced session fetching for existing analysis
  const {
    data: existingSession,
    isLoading: isLoadingSession
  } = useFetchSessionByContext('lab_result', result.id);

  // Mutations
  const createAnalysisMutation = useCreateLabAnalysis();
  const createSessionMutation = useCreateChatSession();

  const hasAnalysis = !!analysis;
  const hasExistingSession = !!existingSession;

  const imagePath = result.image_path;
  const bucketId = result.image_bucket_id;

  console.log(`LabResultCard (${result.lab_name}): imagePath:`, imagePath, 'bucketId:', bucketId);

  const { url: thumbnailUrl, isLoading: isLoadingThumbnail, error: urlError } = useSupabaseStorageUrl({
    path: imagePath,
    bucketId: bucketId,
  });

  // Get URL for captured PDF if it exists
  const { url: capturedPdfUrl } = useSupabaseStorageUrl({
    path: result.captured_pdf_path,
    bucketId: result.captured_pdf_bucket_id || 'labresults',
  });

  console.log(`LabResultCard (${result.lab_name}): thumbnailUrl:`, thumbnailUrl, 'isLoading:', isLoadingThumbnail, 'urlError:', urlError);

  // Check if the lab result has an attachment
  const hasAttachment = !!result.image_path;
  
  // Check if there's a PDF attachment (either in image_path or captured_pdf_path)
  const hasPdfAttachment = !!result.captured_pdf_path || (result.image_path && result.image_path.toLowerCase().includes('.pdf'));

  const handleCheckResultsPress = () => {
    if (!result.website) return;

    let url = result.website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    confirm({
      title: t('misc.openExternalWebsite'),
      message: t('misc.externalWebsiteMessage'),
      confirmText: t('buttons.proceed'),
      cancelText: t('common.cancel'),
      onConfirm: () => {
        router.push({
          pathname: '/modals/lab-result-webview',
          params: {
            websiteUrl: url,
            patientId: result.patient_id,
            password: result.password,
            labResultId: result.id,
          },
        });
      },
    });
  };

  const handleViewLabResult = () => {
    // Determine which PDF to open
    let pdfUrl: string | null = null;
    
    if (result.captured_pdf_path) {
      pdfUrl = capturedPdfUrl;
    } else if (result.image_path && result.image_path.toLowerCase().includes('.pdf')) {
      pdfUrl = thumbnailUrl;
    }

    if (pdfUrl) {
      Linking.openURL(pdfUrl).catch(err => {
        console.error('Failed to open PDF:', err);
        Alert.alert(t('common.error'), t('alerts.failedToOpenPdf'));
      });
    } else {
      Alert.alert(t('common.error'), t('alerts.pdfNotFound'));
    }
  };

  const handleAnalyzeWithAI = async () => {
    try {
      // ✅ PREVENT DUPLICATE ANALYSIS: Check if analysis is already being processed
      if (createAnalysisMutation.isPending || createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      // If we have existing session, navigate to it
      if (hasExistingSession && existingSession) {
        router.push(`/(ai-chat)/${existingSession.id}` as any);
        return;
      }

      // If we already have analysis with session, navigate to it
      if (hasAnalysis && analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      // Create or get session first
      const session = await createSessionMutation.mutateAsync({
        type: 'lab_result',
        contextId: result.id,
        title: `${result.lab_name} Analysis`,
      });

      // ✅ NAVIGATE IMMEDIATELY - NO LOADING STATES
      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  // Enhanced button logic with proper loading states using computed flags
  const getAIButtonText = () => {
    if (isCheckingAnalysis || isLoadingSession) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (hasExistingSession || hasAnalysis) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis || isLoadingSession) return "hourglass-outline";
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (hasExistingSession || hasAnalysis) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           isLoadingSession || 
           createAnalysisMutation.isPending || 
           createSessionMutation.isPending;
  };

  // Content for the card
  const content = (
    <View className="space-y-2">
      {/* Date and attachment indicator */}
      <View className="flex-row justify-between items-center">
        <Text className="text-sm text-gray-500">{formattedDate}</Text>
        <View className="flex-row items-center space-x-2">
          {hasAttachment && (
            <View className="flex-row items-center">
              <Ionicons name="document-attach" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-500 ml-1">{t('misc.attachment')}</Text>
            </View>
          )}
          
        </View>
      </View>

      {/* Patient info if available */}
      {result.patients && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="person" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">{result.patients.name}</Text>
        </View>
      )}

      {/* Show patient name from the lab result if there's no linked patient */}
      {!result.patients && result.patient_name && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="person" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">{result.patient_name}</Text>
        </View>
      )}
    </View>
  );

  return (
    <Card
      title={result.lab_name}
      content={content}
      onPress={onPress}
      imageUrl={thumbnailUrl}
      imageLoading={isLoadingThumbnail}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    >
      {/* Action buttons section */}
      {(result.website || hasAttachment) && (
        <View className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 space-y-2">
          {/* View Lab Result button - show if there's a PDF attachment */}
          {hasPdfAttachment && (
            <Button
              title={t('buttons.viewLabResult')}
              onPress={handleViewLabResult}
              variant="outline"
              icon={<Ionicons name="document-text-outline" size={16} className="text-primary dark:text-sky-400" />}
            />
          )}

          {/* AI Analysis button - only show if there's a PDF attachment */}
          {hasPdfAttachment && (
            <Button
              title={getAIButtonText()}
              onPress={handleAnalyzeWithAI}
              variant="outline"
              icon={<Ionicons name={getAIButtonIcon()} size={16} className="text-primary dark:text-sky-400" />}
              disabled={isAIButtonDisabled()}
            />
          )}

          {/* Check Results button - only show if there's a website but no PDF */}
          {result.website && !hasPdfAttachment && (
            <Button
              title={t('buttons.checkResults')}
              onPress={handleCheckResultsPress}
              variant="outline"
              icon={<Ionicons name="open-outline" size={16} className="text-primary dark:text-sky-400" />}
            />
          )}
        </View>
      )}
    </Card>
  );
};

export default LabResultCard;