import React from "react";
import { View, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { MarkdownComponent } from "@/components/ui/markdown";

export interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: Date;
}

interface ChatMessageProps {
  message: Message;
  isLast?: boolean;
}

export function ChatMessage({ message, isLast }: ChatMessageProps) {
  if (message.role === "system") {
    return null; // Don't render system messages
  }

  if (message.role === "user") {
    // Simple, right-aligned user message
    return (
      <View className="mb-4">
        <View className="flex-row justify-end items-start gap-2">
          <View className="bg-blue-500 dark:bg-blue-600 rounded-lg px-4 py-2 max-w-[80%]">
            <Text className="text-white text-base">{message.content}</Text>
          </View>
          <View className="w-6 h-6 rounded-full bg-gray-400 dark:bg-gray-600 items-center justify-center mt-1">
            <Ionicons name="person" size={12} color="white" />
          </View>
        </View>
      </View>
    );
  }

  // AI Assistant message - full width, no bubble, document-style
  return (
    <View className="mb-6">
      {/* AI Header */}
      <View className="flex-row items-center gap-2 mb-3">
        <View className="w-6 h-6 rounded-full bg-blue-500 dark:bg-blue-600 items-center justify-center">
          <Ionicons name="sparkles" size={12} color="white" />
        </View>
        <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">
          AI Assistant
        </Text>
      </View>
      
      {/* AI Content - Full width, clean typography */}
      <View className="pl-8">
        <MarkdownComponent>{message.content}</MarkdownComponent>
      </View>
      
      {/* Separator line for readability */}
      {!isLast && (
        <View className="mt-4 border-b border-gray-100 dark:border-gray-800" />
      )}
    </View>
  );
} 