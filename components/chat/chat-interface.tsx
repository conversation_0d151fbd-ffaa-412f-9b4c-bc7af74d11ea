import React, { useRef, useEffect } from "react";
import {
  View,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from "react-native";
import {
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { ChatMessage, Message } from "./chat-message";
import { ChatInput } from "./chat-input";
import { TypingIndicator } from "./typing-indicator";

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  welcomeComponent?: React.ReactNode;
  placeholder?: string;
}

export function ChatInterface({
  messages,
  onSendMessage,
  isLoading = false,
  welcomeComponent,
  placeholder = "Type your message...",
}: ChatInterfaceProps) {
  const flatListRef = useRef<FlatList>(null);
  const insets = useSafeAreaInsets();

  // Extract complex expression for dependency array
  const lastMessageContent = messages.length > 0 ? messages[messages.length - 1]?.content : undefined;

  // Auto-scroll to bottom when new messages arrive or content updates
  useEffect(() => {
    if (messages.length > 0) {
      // ✅ IMMEDIATE scroll for streaming
      flatListRef.current?.scrollToEnd({ animated: true });
    }
  }, [messages.length, lastMessageContent]);

  // Handle keyboard events with better scroll timing
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        // ✅ FIXED: Faster scroll when keyboard appears
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 50);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        // Small delay to allow layout to settle
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const renderMessage = ({ item, index }: { item: Message; index: number }) => {
    const isLast = index === messages.length - 1;
    return <ChatMessage message={item} isLast={isLast} />;
  };

  const renderFooter = () => {
    if (isLoading) {
      return <TypingIndicator />;
    }
    return null;
  };

  return (
    <View
      className="flex-1 bg-background dark:bg-neutral-900"
      
    >
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        {messages.length === 0 ? (
          // Show welcome message when no messages, or empty list with typing indicator
          welcomeComponent ? (
            <View className="flex-1">{welcomeComponent}</View>
          ) : (
            // Show empty FlatList that can still render footer (typing indicator)
            <FlatList
              ref={flatListRef}
              data={[]}
              renderItem={() => null}
              contentContainerStyle={{
                padding: 16,
                paddingBottom: 16,
                flexGrow: 1,
              }}
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={renderFooter}
              keyboardShouldPersistTaps="handled"
            />
          )
        ) : (
          // Show messages list
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{
              padding: 16,
              paddingBottom: 16,
              flexGrow: 1,
            }}
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={renderFooter}
            onContentSizeChange={() => {
              // ✅ IMMEDIATE scroll on content change
              flatListRef.current?.scrollToEnd({ animated: true });
            }}
            keyboardShouldPersistTaps="handled"
            automaticallyAdjustKeyboardInsets={false}
            automaticallyAdjustContentInsets={false}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 10,
            }}
          />
        )}

        {/* Chat Input - Fixed positioning */}
        <View
          className="border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900"
          style={{
            paddingBottom: Platform.OS === "android" ? insets.bottom : 0,
          }}
        >
          <ChatInput
            onSendMessage={onSendMessage}
            disabled={isLoading}
            placeholder={placeholder}
          />
        </View>
      </KeyboardAvoidingView>
    </View>
  );
} 