import React, { useEffect, useRef } from "react";
import { View, Animated } from "react-native";

export function TypingIndicator() {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDot = (animatedValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animation = Animated.parallel([
      animateDot(dot1, 0),
      animateDot(dot2, 200),
      animateDot(dot3, 400),
    ]);

    animation.start();

    return () => animation.stop();
  }, [dot1, dot2, dot3]);

  const DotComponent = ({ animatedValue }: { animatedValue: Animated.Value }) => (
    <Animated.View
      className="w-2 h-2 rounded-full bg-muted-foreground dark:bg-neutral-500 mx-1"
      style={{
        opacity: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1],
        }),
        transform: [
          {
            scale: animatedValue.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 1.2],
            }),
          },
        ],
      }}
    />
  );

  return (
    <View className="flex-row items-center py-2">
      <View className="w-8 h-8 rounded-full bg-primary items-center justify-center mr-3">
        <View className="w-1 h-1 rounded-full bg-white" />
      </View>
      <View className="bg-muted dark:bg-neutral-800 rounded-2xl rounded-bl-md px-4 py-3 border border-border dark:border-neutral-700">
        <View className="flex-row items-center">
          <DotComponent animatedValue={dot1} />
          <DotComponent animatedValue={dot2} />
          <DotComponent animatedValue={dot3} />
        </View>
      </View>
    </View>
  );
} 