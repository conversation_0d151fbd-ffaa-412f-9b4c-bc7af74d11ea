import React from 'react'
import Markdown from 'react-native-markdown-display'
import { useColorScheme } from 'react-native'

interface MarkdownComponentProps {
  children: string
}

export function MarkdownComponent({ children }: MarkdownComponentProps) {
  const colorScheme = useColorScheme()
  const isDark = colorScheme === 'dark'

  const markdownStyles = {
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: isDark ? '#ffffff' : '#000000',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold' as const,
      marginTop: 16,
      marginBottom: 8,
      color: isDark ? '#ffffff' : '#000000',
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold' as const,
      marginTop: 14,
      marginBottom: 6,
      color: isDark ? '#ffffff' : '#000000',
    },
    heading3: {
      fontSize: 18,
      fontWeight: 'bold' as const,
      marginTop: 12,
      marginBottom: 4,
      color: isDark ? '#ffffff' : '#000000',
    },
    paragraph: {
      marginBottom: 12,
      fontSize: 16,
      lineHeight: 24,
      color: isDark ? '#ffffff' : '#000000',
    },
    strong: {
      fontWeight: 'bold' as const,
      color: isDark ? '#ffffff' : '#000000',
    },
    em: {
      fontStyle: 'italic' as const,
      color: isDark ? '#ffffff' : '#000000',
    },
    code_inline: {
      backgroundColor: isDark ? '#374151' : '#f3f4f6',
      color: isDark ? '#fbbf24' : '#d97706',
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
      fontSize: 14,
      fontFamily: 'monospace',
    },
    code_block: {
      backgroundColor: isDark ? '#374151' : '#f3f4f6',
      color: isDark ? '#e5e7eb' : '#374151',
      padding: 12,
      borderRadius: 8,
      marginVertical: 8,
      fontSize: 14,
      fontFamily: 'monospace',
    },
    fence: {
      backgroundColor: isDark ? '#374151' : '#f3f4f6',
      color: isDark ? '#e5e7eb' : '#374151',
      padding: 12,
      borderRadius: 8,
      marginVertical: 8,
      fontSize: 14,
      fontFamily: 'monospace',
    },
    list_item: {
      marginBottom: 4,
      color: isDark ? '#ffffff' : '#000000',
    },
    bullet_list: {
      marginBottom: 8,
    },
    ordered_list: {
      marginBottom: 8,
    },
    blockquote: {
      backgroundColor: isDark ? '#374151' : '#f9fafb',
      borderLeftWidth: 4,
      borderLeftColor: isDark ? '#60a5fa' : '#3b82f6',
      paddingLeft: 12,
      paddingVertical: 8,
      marginVertical: 8,
      fontStyle: 'italic' as const,
    },
    link: {
      color: isDark ? '#60a5fa' : '#3b82f6',
      textDecorationLine: 'underline' as const,
    },
    hr: {
      backgroundColor: isDark ? '#4b5563' : '#d1d5db',
      height: 1,
      marginVertical: 16,
    },
  }

  return (
    <Markdown style={markdownStyles}>
      {children}
    </Markdown>
  )
} 