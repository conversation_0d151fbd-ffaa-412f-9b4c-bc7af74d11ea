import React from 'react';
import { Text, Pressable, ActivityIndicator, View , StyleProp, ViewStyle, TextStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'outline';

// Use the correct type for Ionicons names
type IoniconsName = React.ComponentProps<typeof Ionicons>['name'];

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  isLoading?: boolean;
  disabled?: boolean;
  icon?: IoniconsName | React.ReactNode; // Allow Ionicons name or ReactNode
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

/**
 * A reusable button component that can be styled as primary, secondary, danger, or outline.
 */
export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  isLoading = false,
  disabled = false,
  icon,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const isDisabled = isLoading || disabled;

  // Define base styles
  const baseButtonClasses = "px-4 py-3 rounded-md border flex-row items-center justify-center";
  const baseTextClasses = "font-medium text-base";
  const baseIconContainerClasses = "mr-2";

  // Define variant specific styles
  let variantButtonClasses = '';
  let variantTextClasses = '';
  let activityIndicatorColor = ''; // Will be set by utility class
  let iconColorClass = ''; // Will be set by utility class

  switch (variant) {
    case 'primary':
      variantButtonClasses = 'bg-primary border-transparent dark:bg-primary'; // Assuming primary color is suitable for dark, adjust if needed
      variantTextClasses = 'text-primary-foreground dark:text-primary-foreground';
      iconColorClass = 'text-primary-foreground dark:text-primary-foreground';
      break;
    case 'secondary':
      variantButtonClasses = 'bg-secondary border-transparent dark:bg-neutral-700';
      variantTextClasses = 'text-secondary-foreground dark:text-neutral-200';
      iconColorClass = 'text-secondary-foreground dark:text-neutral-200';
      break;
    case 'danger':
      variantButtonClasses = 'bg-destructive border-transparent dark:bg-destructive'; // Assuming destructive color is suitable for dark
      variantTextClasses = 'text-destructive-foreground dark:text-destructive-foreground';
      iconColorClass = 'text-destructive-foreground dark:text-destructive-foreground';
      break;
    case 'outline':
      variantButtonClasses = 'bg-transparent border-input dark:border-neutral-600';
      variantTextClasses = 'text-foreground dark:text-neutral-200';
      iconColorClass = 'text-foreground dark:text-neutral-200';
      break;
    default:
      // Default to primary if variant is somehow unknown
      variantButtonClasses = 'bg-primary border-transparent dark:bg-primary';
      variantTextClasses = 'text-primary-foreground dark:text-primary-foreground';
      iconColorClass = 'text-primary-foreground dark:text-primary-foreground';
      break;
  }
  // ActivityIndicator color will match the text color of the button
  activityIndicatorColor = variantTextClasses;

  return (
    <Pressable
      onPress={isDisabled ? undefined : onPress}
      disabled={isDisabled}
      className={`
        ${baseButtonClasses}
        ${variantButtonClasses}
        ${isDisabled ? 'opacity-50' : 'opacity-100'}
        ${fullWidth ? 'w-full' : 'min-w-[100px]'}
      `}
      style={style}
    >
      {isLoading ? (
        <ActivityIndicator size="small" className={activityIndicatorColor} />
      ) : (
        <View className="flex-row items-center justify-center">
          {icon && typeof icon === 'string' && (
            <View className={baseIconContainerClasses}>
              <Ionicons name={icon as IoniconsName} size={20} className={iconColorClass} />
            </View>
          )}
          {icon && typeof icon !== 'string' && (
            <View className={baseIconContainerClasses}>{icon}</View>
          )}
          <Text 
            className={`
              ${baseTextClasses}
              ${variantTextClasses}
            `}
            style={textStyle}
          >
            {title}
          </Text>
        </View>
      )}
    </Pressable>
  );
};

export default Button; 