import React, { useState } from 'react';
import { TextInput, View, Text, TextInputProps, Pressable, Platform, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  error?: string;
  onChangeText?: (text: string) => void;
}

const Input = ({
  label,
  leftIcon,
  secureTextEntry,
  error,
  onChangeText,
  ...textInputProps
}: InputProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = secureTextEntry;

  return (
    <View className="mb-4">
      {label && (
        <Text className="mb-1 text-sm font-medium text-foreground dark:text-neutral-300">{label}</Text>
      )}

      <View className="flex-row items-center rounded-xl border border-input bg-background dark:border-neutral-700 dark:bg-neutral-800">
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            className="ml-3"
            color={error ? '#DC2626' : '#6B7280'}
          />
        )}

        <TextInput
          className="flex-1 px-3 text-base text-foreground dark:text-neutral-50"
          style={styles.input}
          onChangeText={onChangeText}
          secureTextEntry={isPassword && !showPassword}
          placeholderTextColor={error ? '#DC2626' : '#6B7280'}
          {...textInputProps}
        />

        {isPassword && (
          <Pressable
            className="pr-3"
            onPress={() => setShowPassword(!showPassword)}
          >
            <Ionicons
              name={showPassword ? 'eye-off' : 'eye'}
              size={20}
              color="#6B7280"
            />
          </Pressable>
        )}
      </View>

      {error && (
        <Text className="mt-1 text-sm text-destructive dark:text-red-400">
          {error}
        </Text>
      )}
    </View>
  );
};

// These styles specifically fix the text alignment issue
const styles = StyleSheet.create({
  input: {
    height: 48,
    lineHeight: Platform.OS === 'ios' ? 0 : 24,
    paddingVertical: 0,
    textAlignVertical: 'center',
  }
});

export default Input;