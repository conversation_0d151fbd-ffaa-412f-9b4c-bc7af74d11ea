import { useEffect } from 'react';
import { useMedicineExpirationPushNotifications } from '@/hooks/useMedicineExpirationPushNotifications';
import { useAuth } from '@clerk/clerk-expo';

/**
 * Component that initializes medication expiration notifications when the app starts.
 * This should be rendered once in the app's protected route tree.
 */
export function NotificationInitializer() {
  const { isSignedIn } = useAuth();
  const {
    checkAndRescheduleIfNeeded,
  } = useMedicineExpirationPushNotifications();

  useEffect(() => {
    if (!isSignedIn) return;

    // Initialize notifications when user is signed in
    const initializeNotifications = async () => {
      try {
        // Check if notifications need to be rescheduled
        await checkAndRescheduleIfNeeded();
      } catch (error) {
        console.error('Error initializing medicine expiration notifications:', error);
      }
    };

    // Delay initialization slightly to ensure app is fully loaded
    const timeoutId = setTimeout(initializeNotifications, 1000);

    return () => clearTimeout(timeoutId);
  }, [isSignedIn, checkAndRescheduleIfNeeded]); // Include the function in dependencies

  // This component doesn't render anything visible
  return null;
}
