import React from 'react';
import { View, Text, Alert } from 'react-native';
import { differenceInDays } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Card from '../ui/Card'; // Assuming a generic Card component exists
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

// Function to format dosage information
const formatDosage = (med: MedicineWithRelations): string | null => {
  let dosage = '';
  if (med.dosage_amount && med.dosage_unit) {
    dosage += `${med.dosage_amount} ${med.dosage_unit}`;
  }
  if (med.frequency_amount && med.frequency_unit) {
    dosage += dosage ? `, ${med.frequency_amount} ${med.frequency_unit}` : `${med.frequency_amount} ${med.frequency_unit}`; 
  }
  return dosage || null;
};

// Function to format duration
const formatDuration = (med: MedicineWithRelations): string | null => {
  if (med.duration_unit === 'Ongoing' || med.duration_unit === 'As needed') {
    return med.duration_unit;
  }
  if (med.duration_amount && med.duration_unit) {
    return `${med.duration_amount} ${med.duration_unit}`;
  }
  return null;
};

/**
 * A card component for displaying a medicine in a list.
 */
const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const { t } = useTranslation();
  const dosageText = formatDosage(medicine);
  const durationText = formatDuration(medicine);

  // Use TanStack Query hooks
  const { useFetchMedicationAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useCreateChatSession } = useChatSessions();

  // Query for existing analysis - THIS IS ALL WE NEED!
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchMedicationAnalysis(medicine.id);

  // Mutations
  const createAnalysisMutation = useCreateMedicationAnalysis();
  const createSessionMutation = useCreateChatSession();

  // Get image URL
  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Calculate days until expiration
  const today = new Date();
  const expirationDate = medicine.expiration_date ? new Date(medicine.expiration_date) : null;
  const daysUntilExpiration = expirationDate ? differenceInDays(expirationDate, today) : null;
  let expirationInfo: { text: string; color: string } | null = null;

  if (daysUntilExpiration !== null) {
    if (daysUntilExpiration < 0) {
      expirationInfo = { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-500' };
    } else if (daysUntilExpiration <= 30) {
      expirationInfo = { text: `Expires in ${daysUntilExpiration} days`, color: 'text-yellow-600' };
    } else {
      // Optionally show if not expiring soon, or hide
      // expirationInfo = { text: `Expires on ${format(expirationDate!, 'MMM d, yyyy')}`, color: 'text-gray-500' };
    }
  }

  const handleAnalyzeWithAI = async () => {
    try {
      // ✅ PREVENT DUPLICATE ANALYSIS
      if (createAnalysisMutation.isPending || createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      // If we already have analysis with session, navigate to it
      if (analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      // Create session first
      const session = await createSessionMutation.mutateAsync({
        type: 'medication',
        contextId: medicine.id,
        title: `${medicine.name} Analysis`,
      });

      // ✅ NAVIGATE IMMEDIATELY
      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  // Enhanced button logic with proper loading states
  const getAIButtonText = () => {
    if (isCheckingAnalysis) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (analysis?.session_id) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis) return "hourglass-outline";
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (analysis?.session_id) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           createAnalysisMutation.isPending || 
           createSessionMutation.isPending;
  };

  // Content for the card
  const content = (
    <View className="space-y-2">
      {/* Dosage and Duration */}
      {(dosageText || durationText) && (
        <View className="flex-row items-center space-x-3">
          {dosageText && (
            <View className="flex-row items-center">
              <Ionicons name="eyedrop-outline" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-600 ml-1">{dosageText}</Text>
            </View>
          )}
          {durationText && (
            <View className="flex-row items-center">
              <Ionicons name="calendar-outline" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-600 ml-1">{durationText}</Text>
            </View>
          )}
        </View>
      )}

      {/* Patient Info */}
      {medicine.patients && (
        <View className="flex-row items-center">
          <Ionicons name="person-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">For: {medicine.patients.name}</Text>
        </View>
      )}

      {/* Receipt Indicator */}
      {medicine.receipt_image_path && (
        <View className="flex-row items-center">
          <Ionicons name="receipt-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">From receipt</Text>
        </View>
      )}

      {/* Expiration Info */}
      {expirationInfo && (
        <View className="flex-row items-center">
          <Ionicons name="warning-outline" size={16} color={expirationInfo.color.includes('red') ? '#EF4444' : '#D97706'} />
          <Text className={`text-sm ml-1 ${expirationInfo.color}`}>{expirationInfo.text}</Text>
        </View>
      )}

      {/* Description Snippet (Optional) */}
      {medicine.description && (
        <Text className="text-sm text-gray-500 italic" numberOfLines={1}>
          {medicine.description}
        </Text>
      )}
    </View>
  );

  return (
    <Card
      title={medicine.name}
      content={content}
      onPress={onPress}
      imageUrl={imageUrl}
      imageLoading={isLoadingUrl}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    >
      {/* AI Analysis button */}
      <View className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <Button
          title={getAIButtonText()}
          onPress={handleAnalyzeWithAI}
          variant="outline"
          icon={<Ionicons name={getAIButtonIcon()} size={16} className="text-primary dark:text-sky-400" />}
          disabled={isAIButtonDisabled()}
        />
      </View>
    </Card>
  );
};

export default MedicineCard; 