import React from 'react';
import { View, Text, Alert, Pressable, ActivityIndicator } from 'react-native';
import { differenceInDays, format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

// Helper function to format dosage
const formatDosage = (med: MedicineWithRelations): string => {
  if (med.dosage_amount && med.dosage_unit) {
    return `${med.dosage_amount} ${med.dosage_unit}`;
  }
  return 'Not specified';
};

// Helper function to format frequency
const formatFrequency = (med: MedicineWithRelations): string => {
  if (med.frequency_amount && med.frequency_unit) {
    return `${med.frequency_amount} ${med.frequency_unit}`;
  }
  return 'Not specified';
};

// Helper function to format duration
const formatDuration = (med: MedicineWithRelations): string => {
  if (med.duration_unit === 'Ongoing' || med.duration_unit === 'As needed') {
    return med.duration_unit;
  }
  if (med.duration_amount && med.duration_unit) {
    return `${med.duration_amount} ${med.duration_unit}`;
  }
  return 'Not specified';
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'Not set';
  try {
    return format(new Date(dateString), 'MMM d, yyyy');
  } catch {
    return 'Invalid date';
  }
};

/**
 * A redesigned card component for displaying a medicine with all information fields.
 * Optimized for 2-card horizontal layout.
 */
const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const { t } = useTranslation();

  // Use TanStack Query hooks
  const { useFetchMedicationAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useCreateChatSession } = useChatSessions();

  // Query for existing analysis
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchMedicationAnalysis(medicine.id);

  // Mutations
  const createAnalysisMutation = useCreateMedicationAnalysis();
  const createSessionMutation = useCreateChatSession();

  // Get image URL
  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Calculate expiration status
  const getExpirationStatus = () => {
    if (!medicine.expiration_date) return null;

    const today = new Date();
    const expirationDate = new Date(medicine.expiration_date);
    const daysUntilExpiration = differenceInDays(expirationDate, today);

    if (daysUntilExpiration < 0) {
      return { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration === 0) {
      return { text: 'Expires today', color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration <= 30) {
      return { text: `Expires in ${daysUntilExpiration} days`, color: 'text-orange-600 dark:text-orange-400', icon: 'time' as const };
    } else {
      return { text: `Expires ${formatDate(medicine.expiration_date)}`, color: 'text-gray-600 dark:text-gray-400', icon: 'calendar' as const };
    }
  };

  const expirationStatus = getExpirationStatus();

  const handleAnalyzeWithAI = async () => {
    try {
      if (createAnalysisMutation.isPending || createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      if (analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      const session = await createSessionMutation.mutateAsync({
        type: 'medication',
        contextId: medicine.id,
        title: `${medicine.name} Analysis`,
      });

      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  const getAIButtonText = () => {
    if (isCheckingAnalysis) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (analysis?.session_id) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis) return "hourglass-outline";
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (analysis?.session_id) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           createAnalysisMutation.isPending || 
           createSessionMutation.isPending;
  };

  // Info row component for consistent styling
  const InfoRow = ({ icon, label, value, color = "text-gray-600 dark:text-gray-400" }: {
    icon: string;
    label: string;
    value: string;
    color?: string;
  }) => (
    <View className="flex-row items-center mb-1">
      <Ionicons name={icon as any} size={14} color="#6B7280" />
      <Text className="text-xs text-gray-500 dark:text-gray-400 ml-1 w-16">{label}:</Text>
      <Text className={`text-xs ml-1 flex-1 ${color}`} numberOfLines={1}>{value}</Text>
    </View>
  );

  return (
    <Pressable
      onPress={onPress}
      className="bg-card border border-border shadow-sm rounded-lg mb-3 overflow-hidden dark:bg-neutral-800 dark:border-neutral-700 flex-1 mx-1"
    >
      {/* Header with image and title */}
      <View className="p-3">
        <View className="flex-row items-start mb-2">
          {/* Medicine Image */}
          <View className="w-12 h-12 mr-3 justify-center items-center bg-muted rounded dark:bg-neutral-700">
            {isLoadingUrl ? (
              <ActivityIndicator size="small" className="text-muted-foreground dark:text-neutral-400" />
            ) : imageUrl ? (
              <Image
                source={{ uri: imageUrl }}
                style={{ width: 48, height: 48, borderRadius: 4 }}
                contentFit="cover"
                placeholder={{ blurhash: "LKO2?U%2Tw=w]~RBVZRi};RPxuwH" }}
                transition={500}
              />
            ) : (
              <Ionicons name="medkit-outline" size={20} className="text-muted-foreground dark:text-neutral-400" />
            )}
          </View>

          {/* Title and chevron */}
          <View className="flex-1">
            <Text className="text-sm font-semibold text-card-foreground dark:text-neutral-100" numberOfLines={2}>
              {medicine.name}
            </Text>
            {medicine.price && (
              <Text className="text-xs text-green-600 dark:text-green-400 mt-1">
                ${medicine.price.toFixed(2)}
              </Text>
            )}
          </View>

          <Ionicons name="chevron-forward" size={16} color="#6B7280" />
        </View>

        {/* All Medicine Information */}
        <View className="space-y-1">
          {/* Dosage Information */}
          <InfoRow
            icon="eyedrop-outline"
            label="Dosage"
            value={formatDosage(medicine)}
          />

          {/* Frequency Information */}
          <InfoRow
            icon="time-outline"
            label="Frequency"
            value={formatFrequency(medicine)}
          />

          {/* Duration Information */}
          <InfoRow
            icon="calendar-outline"
            label="Duration"
            value={formatDuration(medicine)}
          />

          {/* Expiration Date */}
          {expirationStatus ? (
            <InfoRow
              icon={expirationStatus.icon}
              label="Expires"
              value={expirationStatus.text}
              color={expirationStatus.color}
            />
          ) : (
            <InfoRow
              icon="calendar-outline"
              label="Expires"
              value="Not set"
            />
          )}

          {/* Opened Date */}
          <InfoRow
            icon="calendar-clear-outline"
            label="Opened"
            value={formatDate(medicine.opened_on_date)}
          />

          {/* Patient Information */}
          <InfoRow
            icon="person-outline"
            label="Patient"
            value={medicine.patients?.name || 'Not assigned'}
          />

          {/* Description */}
          {medicine.description && (
            <InfoRow
              icon="document-text-outline"
              label="Notes"
              value={medicine.description}
            />
          )}

          {/* Additional Notes */}
          {medicine.notes && (
            <InfoRow
              icon="reader-outline"
              label="Notes"
              value={medicine.notes}
            />
          )}

          {/* Receipt Indicator */}
          {medicine.receipt_image_path && (
            <InfoRow
              icon="receipt-outline"
              label="Source"
              value="From receipt"
              color="text-blue-600 dark:text-blue-400"
            />
          )}
        </View>

        {/* AI Analysis Button */}
        <View className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
          <Button
            title={getAIButtonText()}
            onPress={handleAnalyzeWithAI}
            variant="outline"
            icon={<Ionicons name={getAIButtonIcon()} size={14} className="text-primary dark:text-sky-400" />}
            disabled={isAIButtonDisabled()}
          />
        </View>
      </View>
    </Pressable>
  );
};

export default MedicineCard; 