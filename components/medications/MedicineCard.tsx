import React from 'react';
import { View, Text, Alert, Pressable, ActivityIndicator } from 'react-native';
import { differenceInDays, format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

// Helper function to format dosage
const formatDosage = (med: MedicineWithRelations): string => {
  if (med.dosage_amount && med.dosage_unit) {
    return `${med.dosage_amount} ${med.dosage_unit}`;
  }
  return 'Not specified';
};

// Helper function to format frequency
const formatFrequency = (med: MedicineWithRelations): string => {
  if (med.frequency_amount && med.frequency_unit) {
    return `${med.frequency_amount} ${med.frequency_unit}`;
  }
  return 'Not specified';
};

// Helper function to format duration
const formatDuration = (med: MedicineWithRelations): string => {
  if (med.duration_unit === 'Ongoing' || med.duration_unit === 'As needed') {
    return med.duration_unit;
  }
  if (med.duration_amount && med.duration_unit) {
    return `${med.duration_amount} ${med.duration_unit}`;
  }
  return 'Not specified';
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'Not set';
  try {
    return format(new Date(dateString), 'MMM d, yyyy');
  } catch {
    return 'Invalid date';
  }
};

/**
 * A redesigned card component for displaying a medicine with all information fields.
 * Optimized for 2-card horizontal layout.
 */
const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const { t } = useTranslation();

  // Use TanStack Query hooks
  const { useFetchMedicationAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useCreateChatSession } = useChatSessions();

  // Query for existing analysis
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchMedicationAnalysis(medicine.id);

  // Mutations
  const createAnalysisMutation = useCreateMedicationAnalysis();
  const createSessionMutation = useCreateChatSession();

  // Get image URL
  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Calculate expiration status
  const getExpirationStatus = () => {
    if (!medicine.expiration_date) return null;

    const today = new Date();
    const expirationDate = new Date(medicine.expiration_date);
    const daysUntilExpiration = differenceInDays(expirationDate, today);

    if (daysUntilExpiration < 0) {
      return { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration === 0) {
      return { text: 'Expires today', color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration <= 30) {
      return { text: `Expires in ${daysUntilExpiration} days`, color: 'text-orange-600 dark:text-orange-400', icon: 'time' as const };
    } else {
      return { text: `Expires ${formatDate(medicine.expiration_date)}`, color: 'text-gray-600 dark:text-gray-400', icon: 'calendar' as const };
    }
  };

  const expirationStatus = getExpirationStatus();

  const handleAnalyzeWithAI = async () => {
    try {
      if (createAnalysisMutation.isPending || createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      if (analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      const session = await createSessionMutation.mutateAsync({
        type: 'medication',
        contextId: medicine.id,
        title: `${medicine.name} Analysis`,
      });

      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  const getAIButtonText = () => {
    if (isCheckingAnalysis) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (analysis?.session_id) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis) return "hourglass-outline";
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (analysis?.session_id) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           createAnalysisMutation.isPending || 
           createSessionMutation.isPending;
  };

  // Info row component for consistent styling
  const InfoRow = ({ icon, label, value, color = "text-gray-600 dark:text-gray-400" }: {
    icon: string;
    label: string;
    value: string;
    color?: string;
  }) => (
    <View className="flex-row items-center mb-1">
      <Ionicons name={icon as any} size={14} color="#6B7280" />
      <Text className="text-xs text-gray-500 dark:text-gray-400 ml-1 w-16">{label}:</Text>
      <Text className={`text-xs ml-1 flex-1 ${color}`} numberOfLines={1}>{value}</Text>
    </View>
  );

  return (
    <Pressable
      onPress={onPress}
      className="bg-white dark:bg-neutral-800 rounded-xl shadow-sm border border-gray-200 dark:border-neutral-700 flex-1 mx-1 mb-2"
    >
      {/* Medicine Image */}
      <View className="relative">
        {isLoadingUrl ? (
          <View className="h-32 bg-gray-100 dark:bg-neutral-700 rounded-t-xl justify-center items-center">
            <ActivityIndicator size="small" />
          </View>
        ) : imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            style={{ height: 128, borderTopLeftRadius: 12, borderTopRightRadius: 12 }}
            contentFit="cover"
            placeholder={{ blurhash: "LKO2?U%2Tw=w]~RBVZRi};RPxuwH" }}
            transition={500}
          />
        ) : (
          <View className="h-32 bg-gray-100 dark:bg-neutral-700 rounded-t-xl justify-center items-center">
            <Ionicons name="medkit-outline" size={32} color="#9CA3AF" />
          </View>
        )}

        {/* Price Badge */}
        {medicine.price && (
          <View className="absolute top-2 right-2 bg-green-500 px-2 py-1 rounded-full">
            <Text className="text-white text-xs font-medium">${medicine.price.toFixed(2)}</Text>
          </View>
        )}

        {/* Receipt Badge */}
        {medicine.receipt_image_path && (
          <View className="absolute top-2 left-2 bg-blue-500 px-2 py-1 rounded-full">
            <Ionicons name="receipt" size={12} color="white" />
          </View>
        )}
      </View>

      {/* Card Content */}
      <View className="p-3">
        {/* Medicine Name */}
        <Text className="text-base font-semibold text-gray-900 dark:text-white mb-2" numberOfLines={2}>
          {medicine.name}
        </Text>

        {/* Key Information */}
        <View className="space-y-1 mb-3">
          {/* Dosage */}
          <View className="flex-row items-center">
            <Ionicons name="eyedrop-outline" size={14} color="#6B7280" />
            <Text className="text-xs text-gray-600 dark:text-gray-400 ml-1 flex-1" numberOfLines={1}>
              {formatDosage(medicine)}
            </Text>
          </View>

          {/* Frequency */}
          <View className="flex-row items-center">
            <Ionicons name="time-outline" size={14} color="#6B7280" />
            <Text className="text-xs text-gray-600 dark:text-gray-400 ml-1 flex-1" numberOfLines={1}>
              {formatFrequency(medicine)}
            </Text>
          </View>

          {/* Duration */}
          <View className="flex-row items-center">
            <Ionicons name="calendar-outline" size={14} color="#6B7280" />
            <Text className="text-xs text-gray-600 dark:text-gray-400 ml-1 flex-1" numberOfLines={1}>
              {formatDuration(medicine)}
            </Text>
          </View>

          {/* Patient */}
          {medicine.patients && (
            <View className="flex-row items-center">
              <Ionicons name="person-outline" size={14} color="#6B7280" />
              <Text className="text-xs text-gray-600 dark:text-gray-400 ml-1 flex-1" numberOfLines={1}>
                {medicine.patients.name}
              </Text>
            </View>
          )}

          {/* Expiration Status */}
          {expirationStatus && (
            <View className="flex-row items-center">
              <Ionicons name={expirationStatus.icon} size={14} color={expirationStatus.color.includes('red') ? '#EF4444' : expirationStatus.color.includes('orange') ? '#F59E0B' : '#6B7280'} />
              <Text className={`text-xs ml-1 flex-1 ${expirationStatus.color}`} numberOfLines={1}>
                {expirationStatus.text}
              </Text>
            </View>
          )}
        </View>

        {/* AI Analysis Button */}
        <Button
          title={getAIButtonText()}
          onPress={handleAnalyzeWithAI}
          variant="outline"
          icon={<Ionicons name={getAIButtonIcon()} size={12} />}
          disabled={isAIButtonDisabled()}
        />
      </View>
    </Pressable>
  );
};

export default MedicineCard; 