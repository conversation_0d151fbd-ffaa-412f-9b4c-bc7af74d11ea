import React from 'react';
import { View, Text, Alert, Pressable, ActivityIndicator } from 'react-native';
import { differenceInDays, format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

// Helper function to format dosage
const formatDosage = (med: MedicineWithRelations): string => {
  if (med.dosage_amount && med.dosage_unit) {
    return `${med.dosage_amount} ${med.dosage_unit}`;
  }
  return 'Not specified';
};

// Helper function to format frequency
const formatFrequency = (med: MedicineWithRelations): string => {
  if (med.frequency_amount && med.frequency_unit) {
    return `${med.frequency_amount} ${med.frequency_unit}`;
  }
  return 'Not specified';
};

// Helper function to format duration
const formatDuration = (med: MedicineWithRelations): string => {
  if (med.duration_unit === 'Ongoing' || med.duration_unit === 'As needed') {
    return med.duration_unit;
  }
  if (med.duration_amount && med.duration_unit) {
    return `${med.duration_amount} ${med.duration_unit}`;
  }
  return 'Not specified';
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'Not set';
  try {
    return format(new Date(dateString), 'MMM d, yyyy');
  } catch {
    return 'Invalid date';
  }
};

/**
 * A redesigned card component for displaying a medicine with all information fields.
 * Optimized for 2-card horizontal layout.
 */
const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const { t } = useTranslation();

  // Use TanStack Query hooks
  const { useFetchMedicationAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useCreateChatSession } = useChatSessions();

  // Query for existing analysis
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchMedicationAnalysis(medicine.id);

  // Mutations
  const createAnalysisMutation = useCreateMedicationAnalysis();
  const createSessionMutation = useCreateChatSession();

  // Get image URL
  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Calculate expiration status
  const getExpirationStatus = () => {
    if (!medicine.expiration_date) return null;

    const today = new Date();
    const expirationDate = new Date(medicine.expiration_date);
    const daysUntilExpiration = differenceInDays(expirationDate, today);

    if (daysUntilExpiration < 0) {
      return { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration === 0) {
      return { text: 'Expires today', color: 'text-red-600 dark:text-red-400', icon: 'warning' as const };
    } else if (daysUntilExpiration <= 30) {
      return { text: `Expires in ${daysUntilExpiration} days`, color: 'text-orange-600 dark:text-orange-400', icon: 'time' as const };
    } else {
      return { text: `Expires ${formatDate(medicine.expiration_date)}`, color: 'text-gray-600 dark:text-gray-400', icon: 'calendar' as const };
    }
  };

  const expirationStatus = getExpirationStatus();

  const handleAnalyzeWithAI = async () => {
    try {
      if (createAnalysisMutation.isPending || createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      if (analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      const session = await createSessionMutation.mutateAsync({
        type: 'medication',
        contextId: medicine.id,
        title: `${medicine.name} Analysis`,
      });

      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  const getAIButtonText = () => {
    if (isCheckingAnalysis) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (analysis?.session_id) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis) return "hourglass-outline";
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (analysis?.session_id) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           createAnalysisMutation.isPending || 
           createSessionMutation.isPending;
  };



  // Content for the card - ALL FIELDS FROM MEDICINEFORM.TSX
  const content = (
    <View className="space-y-2">
      {/* Price */}
      <View className="flex-row items-center">
        <Ionicons name="cash-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Price:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {medicine.price ? `$${medicine.price.toFixed(2)}` : 'Not specified'}
        </Text>
      </View>

      {/* Description */}
      <View className="flex-row items-center">
        <Ionicons name="document-text-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Description:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {medicine.description || 'Not specified'}
        </Text>
      </View>

      {/* Dosage */}
      <View className="flex-row items-center">
        <Ionicons name="eyedrop-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Dosage:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {formatDosage(medicine)}
        </Text>
      </View>

      {/* Frequency */}
      <View className="flex-row items-center">
        <Ionicons name="time-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Frequency:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {formatFrequency(medicine)}
        </Text>
      </View>

      {/* Duration */}
      <View className="flex-row items-center">
        <Ionicons name="calendar-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Duration:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {formatDuration(medicine)}
        </Text>
      </View>

      {/* Patient */}
      <View className="flex-row items-center">
        <Ionicons name="person-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Patient:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {medicine.patients?.name || 'Not assigned'}
        </Text>
      </View>

      {/* Expiration Date */}
      <View className="flex-row items-center">
        <Ionicons name="calendar-clear-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Expires:</Text>
        <Text className={`text-sm ml-1 flex-1 ${expirationStatus ? expirationStatus.color : 'text-gray-600'}`} numberOfLines={1} ellipsizeMode="tail">
          {expirationStatus ? expirationStatus.text : formatDate(medicine.expiration_date)}
        </Text>
      </View>

      {/* Opened Date */}
      <View className="flex-row items-center">
        <Ionicons name="calendar-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Opened:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {formatDate(medicine.opened_on_date)}
        </Text>
      </View>

      {/* Notes */}
      <View className="flex-row items-center">
        <Ionicons name="reader-outline" size={16} color="#6B7280" />
        <Text className="text-sm text-gray-500 ml-1 w-16">Notes:</Text>
        <Text className="text-sm text-gray-600 ml-1 flex-1" numberOfLines={1} ellipsizeMode="tail">
          {medicine.notes || 'Not specified'}
        </Text>
      </View>

      {/* Receipt Indicator */}
      {medicine.receipt_image_path && (
        <View className="flex-row items-center">
          <Ionicons name="receipt-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1 w-16">Source:</Text>
          <Text className="text-sm text-blue-600 ml-1 flex-1">From receipt</Text>
        </View>
      )}
    </View>
  );

  return (
    <Card
      title={medicine.name}
      content={content}
      onPress={onPress}
      imageUrl={imageUrl}
      imageLoading={isLoadingUrl}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    >
      {/* AI Analysis button */}
      <View className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <Button
          title={getAIButtonText()}
          onPress={handleAnalyzeWithAI}
          variant="outline"
          icon={<Ionicons name={getAIButtonIcon()} size={16} className="text-primary dark:text-sky-400" />}
          disabled={isAIButtonDisabled()}
        />
      </View>
    </Card>
  );
};

export default MedicineCard; 