import React, { useState, useLayoutEffect, useEffect, useCallback } from 'react';
import { View, Text, Pressable, Dimensions } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Constants } from '@/types/database.types';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput';
import DatePicker from '../ui/DatePicker';
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Image } from 'expo-image';
import { Medicine, useMedicines } from '@/hooks/entities/useMedicines';
import { usePatients, Patient } from '@/hooks/entities/usePatients';
import { medicineFormSchema, MedicineFormData } from '@/schema/medicine';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import ImageCapture, { ImageCaptureResult } from '../common/ImageCapture';
import { useImage } from '@/store/imageStore';
import Animated, { interpolate, SlideInDown, useAnimatedRef, useAnimatedStyle, useScrollViewOffset } from 'react-native-reanimated';
import { useTranslation } from '@/hooks/useTranslation';
import { MedicineOcrResults } from '@/hooks/useSingleMedicineOcr';

export type InitialMedicineFormValues = Omit<Medicine, 'created_at' | 'updated_at' | 'user_id'> & {
  image_path?: string | null;
  image_bucket_id?: string | null;
  receipt_image_path?: string | null;
  receipt_image_bucket_id?: string | null;
};

interface MedicineFormProps {
  initialValues?: Partial<InitialMedicineFormValues>;
  onSubmit: (data: MedicineFormData, fileUri?: string, deleteFile?: boolean) => void;
  isLoading?: boolean;
  isEditing: boolean;
  medicineId?: string;
  capturedImageUri?: string;
}

const MedicineForm: React.FC<MedicineFormProps> = ({
  initialValues,
  onSubmit,
  isLoading: isFormSubmissionLoading = false,
  isEditing,
  medicineId,
  capturedImageUri,
}) => {
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeleteMedicine } = useMedicines();
  const deleteMutation = useDeleteMedicine();
  const { t } = useTranslation();

  // Image state from store
  const storeKey = `medicine-image-${medicineId || 'new'}`;
  const {
    actions: imageActions
  } = useImage(storeKey);
  
  // Local state for tracking new URI and delete flag
  const [newLocalFileUri, setNewLocalFileUri] = useState<string | null>(null);
  const [deleteFile, setDeleteFile] = useState(false);

  const { control, handleSubmit, formState: { errors }, setValue, getValues, watch } = useForm<MedicineFormData>({
    resolver: zodResolver(medicineFormSchema),
    defaultValues: initialValues ? {
      name: initialValues.name || '',
      description: initialValues.description || '',
      dosage_amount: initialValues.dosage_amount ?? undefined,
      dosage_unit: initialValues.dosage_unit ?? undefined,
      frequency_amount: initialValues.frequency_amount ?? undefined,
      frequency_unit: initialValues.frequency_unit ?? undefined,
      duration_amount: initialValues.duration_amount ?? undefined,
      duration_unit: initialValues.duration_unit ?? undefined,
      expiration_date: initialValues.expiration_date ?? undefined,
      opened_on_date: initialValues.opened_on_date ?? undefined,
      patient_id: initialValues.patient_id ?? undefined,
      prescription_id: initialValues.prescription_id ?? undefined,
      notes: initialValues.notes || '',
      price: initialValues.price ?? undefined,
      med_id: initialValues.med_id ?? undefined,
      is_custom: initialValues.is_custom === undefined 
                     ? (initialValues.med_id ? false : true)
                     : initialValues.is_custom,
    } : {
      name: '',
      description: '',
      dosage_amount: undefined,
      dosage_unit: undefined,
      frequency_amount: undefined,
      frequency_unit: undefined,
      duration_amount: undefined,
      duration_unit: undefined,
      expiration_date: undefined,
      opened_on_date: undefined,
      patient_id: undefined,
      prescription_id: undefined,
      notes: '',
      price: undefined,
      med_id: undefined,
      is_custom: true,
    },
  });

  const { useFetchPatients } = usePatients();
  const patientsQuery = useFetchPatients();
  
  const patientOptions = React.useMemo(() => {
    if (!patientsQuery.data) return [];
    return patientsQuery.data.map((patient: Patient): RouterSelectOption => ({
      id: patient.id,
      label: patient.name,
              subtitle: patient.bloodtype ? `${t('misc.bloodType')}: ${patient.bloodtype}` : undefined,
      itemType: 'patient',
      data: patient,
    }));
  }, [patientsQuery.data, t]);

  const dosageUnitRouterOptions = Constants.public.Enums.dosage_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);
  const frequencyUnitRouterOptions = Constants.public.Enums.frequency_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);
  const durationUnitRouterOptions = Constants.public.Enums.duration_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);

  const { 
    url: initialAttachmentUrl, 
    isLoading: isLoadingInitialUrl, 
    error: initialUrlError 
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.image_bucket_id || 'user-meds',
    path: initialValues?.image_path,
  });

  // Receipt image URL
  const { 
    url: receiptImageUrl, 
    isLoading: isLoadingReceiptUrl 
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.receipt_image_bucket_id || 'user-meds',
    path: initialValues?.receipt_image_path,
  });


  const handleImageCapturedForForm = (result: ImageCaptureResult) => {
    setNewLocalFileUri(result.uri);
    setDeleteFile(false);
    
    // Apply OCR results if available
    if (result.ocrResults) {
      const medicineData = result.ocrResults as MedicineOcrResults;
      if (medicineData.name) setValue('name', medicineData.name);
      if (medicineData.description) setValue('description', medicineData.description);
      if (medicineData.dosage_amount) setValue('dosage_amount', medicineData.dosage_amount);
      if (medicineData.dosage_unit) setValue('dosage_unit', medicineData.dosage_unit);
      if (medicineData.frequency_amount) setValue('frequency_amount', medicineData.frequency_amount);
      if (medicineData.frequency_unit) setValue('frequency_unit', medicineData.frequency_unit);
      if (medicineData.duration_amount) setValue('duration_amount', medicineData.duration_amount);
      if (medicineData.duration_unit) setValue('duration_unit', medicineData.duration_unit);
      if (medicineData.price) setValue('price', medicineData.price);
      if (medicineData.notes) setValue('notes', medicineData.notes);
      if (medicineData.expiration_date) {
        // Convert YYYY-MM-DD to ISO string for the form
        setValue('expiration_date', new Date(medicineData.expiration_date).toISOString());
      }
      if (medicineData.opened_on_date) {
        // Convert YYYY-MM-DD to ISO string for the form
        setValue('opened_on_date', new Date(medicineData.opened_on_date).toISOString());
      }
      showSuccess('Medicine data extracted from image. Please review the information.', 'OCR Success');
    }
  };

  const handleImageRemovedForForm = () => {
    imageActions.setUri(null);
    setNewLocalFileUri(null);
    if (initialValues?.image_path) {
      setDeleteFile(true);
    }
  };

  const handleImageErrorForForm = (error: Error) => {
    showError(error.message, 'Image Error');
  };

  // Smart form features using setValue and getValues
  const watchExpirationDate = watch('expiration_date');
  const watchOpenedOnDate = watch('opened_on_date');
  
  // Auto-set opened_on_date to today when expiration_date is first set
  useEffect(() => {
    if (watchExpirationDate && !watchOpenedOnDate && !initialValues?.opened_on_date) {
      setValue('opened_on_date', new Date().toISOString(), { shouldValidate: true });
    }
  }, [watchExpirationDate, watchOpenedOnDate, setValue, initialValues?.opened_on_date]);
  
  // Initialize image from initial values or captured image
  useEffect(() => {
    if (capturedImageUri && !isEditing) {
      // Handle pre-captured image from header
      imageActions.setUri(capturedImageUri);
      setNewLocalFileUri(capturedImageUri);
      setDeleteFile(false);
    } else if (initialValues?.image_path && initialAttachmentUrl) {
      imageActions.setUri(initialAttachmentUrl);
    } else if (!initialValues?.image_path) {
      imageActions.setUri(null);
    }
  }, [capturedImageUri, initialValues?.image_path, initialAttachmentUrl, isEditing, imageActions]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      imageActions.cleanup();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Form validation helper using getValues
  const validateFormData = useCallback(() => {
    const currentValues = getValues();
    const warnings: string[] = [];
    
    // Check if expiration date is in the past
    if (currentValues.expiration_date) {
      const expDate = new Date(currentValues.expiration_date);
      if (expDate < new Date()) {
        warnings.push(t('validation.medicineExpired'));
      }
    }
    
    // Check if dosage is specified without frequency or vice versa
    if ((currentValues.dosage_amount && !currentValues.frequency_amount) || 
        (!currentValues.dosage_amount && currentValues.frequency_amount)) {
      warnings.push(t('validation.dosageFrequencyMismatch'));
    }
    
    return warnings;
  }, [getValues, t]);

  const onFormSubmit = (data: MedicineFormData) => {
    // Validate and show warnings before submission
    const warnings = validateFormData();
    if (warnings.length > 0 && !isEditing) {
      // Show warnings but still allow submission
      console.log('Form warnings:', warnings);
    }
    onSubmit(data, newLocalFileUri || undefined, deleteFile);
  };


  const IMG_HEIGHT = 300;
  const width = Dimensions.get('window').width;
  const scrollRef = useAnimatedRef<Animated.ScrollView>();

  const scrolOffset = useScrollViewOffset(scrollRef);
  const imageAnimatedStyle = useAnimatedStyle(() => {
    return {  
      transform: [{ 
        translateY: interpolate(scrolOffset.value, 
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [IMG_HEIGHT/2, 0, IMG_HEIGHT*0.75]
          
          ,)
      },
      {
        scale: interpolate(scrolOffset.value, 
        [-IMG_HEIGHT, 0, IMG_HEIGHT],
        [2, 1, 2]
      ),
      }
    ],
    };
  });

  // Wrap handleDeletePress in useCallback to make it stable for useLayoutEffect
  const handleDeletePressCallback = useCallback(() => {
    if (!medicineId) return;
    confirm({
      title: t('dialogs.confirmAction'),
      message: t('alerts.confirmDelete'),
      confirmText: t('common.delete'),
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deleteMutation.mutateAsync({ id: medicineId });
          showSuccess(t('alerts.deleteSuccess'));
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from Form:', error);
          showError(error.message || t('medicines.saveFailed', {action: 'delete'}));
        }
      },
    });
  }, [medicineId, confirm, deleteMutation, showSuccess, router, showError, t]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? t('buttons.editMedicine') : t('buttons.addMedicine'),
      headerRight: () => (
        isEditing && medicineId && (
          <Pressable onPress={handleDeletePressCallback} disabled={deleteMutation.isPending || isFormSubmissionLoading} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash"
              size={24}
              color={(deleteMutation.isPending || isFormSubmissionLoading) ? 'gray' : 'red'} 
              
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, medicineId, deleteMutation.isPending, isFormSubmissionLoading, handleDeletePressCallback, t]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900" >
    <Animated.ScrollView 
        ref={scrollRef}
        className="flex-1  bg-background dark:bg-neutral-900"
        contentContainerStyle={{ paddingBottom: 100 }}
        keyboardShouldPersistTaps="handled"
        scrollEventThrottle={16}
      >
        {/* <View className="mb-4 w-full"> */}
            <ImageCapture
                storeKey={storeKey}
                initialImage={capturedImageUri || initialAttachmentUrl}
                onImageCaptured={handleImageCapturedForForm}
                onRemove={handleImageRemovedForForm}
                onError={handleImageErrorForForm}
                enableOcr={true}
                ocrType="medicine"
                triggerOcrOnInitial={!!capturedImageUri}
                placeholderText={t('messages.tapToAddImage')}
                placeholderImageSource={require('@/assets/images/icon.png')}
                imageHeight={IMG_HEIGHT}
                imageWidth={width}
                animatedImageStyle={imageAnimatedStyle}
            />
            
            {isLoadingInitialUrl && <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1 text-center">{t('messages.loadingInitialImage')}</Text>}
            {initialUrlError && !newLocalFileUri && initialValues?.image_path && (
                <Text className="text-sm text-destructive dark:text-red-400 mt-1 text-center">
                    {t('messages.imageLoadError')}
                </Text>
            )}
        {/* </View> */}
        
        {/* Receipt Image Section */}
        {receiptImageUrl && (
          <View className="px-4 py-4 bg-background dark:bg-neutral-900">
            <View className="bg-card dark:bg-neutral-800 rounded-lg p-4 border border-border dark:border-neutral-700">
              <View className="flex-row items-center mb-3">
                <Ionicons name="receipt-outline" size={20} color="#6B7280" style={{ marginRight: 8 }} />
                <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">{t('fields.receiptImage')}</Text>
              </View>
              <Text className="text-sm text-muted-foreground dark:text-neutral-400 mb-3">
                {t('ocr.extractedFromReceipt')}
              </Text>
              {isLoadingReceiptUrl ? (
                <View className="w-full h-48 bg-muted dark:bg-neutral-700 rounded-lg flex items-center justify-center">
                  <Text className="text-muted-foreground dark:text-neutral-400">{t('messages.loading')}</Text>
                </View>
              ) : (
                <Image
                  source={{ uri: receiptImageUrl }}
                  style={{ width: '100%', height: 200, borderRadius: 8 }}
                  contentFit="contain"
                />
              )}
            </View>
          </View>
        )}
        
        <View className="flex-1 px-4 bg-background dark:bg-neutral-900 py-4">
        {/* Rest of the form inputs */}
        <ControlledInput
          control={control}
          name="name"
          label={t('fields.name')}
          placeholder={t('placeholders.medicineName')}
          leftIcon="medkit"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="price"
          label={t('common.price')}
          placeholder={t('placeholders.price')}
          keyboardType="numeric"
          leftIcon="cash-outline"
        />
        <ControlledInput
          control={control}
          name="description"
          label={t('common.description')}
          placeholder={t('placeholders.description')}
          leftIcon="document-text-outline"
          multiline
        />

        <View className="flex-row space-x-2">
          <View className="flex-1">
            <ControlledInput
              control={control}
              name="dosage_amount"
              label={t('fields.dosage')}
              placeholder={t('placeholders.dosage')}
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
              <Controller
                control={control}
                name="dosage_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label={t('common.unit')}
                    options={dosageUnitRouterOptions}
                    selectedOption={dosageUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle={t('medicines.selectDosageUnit')}
                    placeholder={t('common.selectUnit')}
                    error={errors.dosage_unit?.message}
                    clearable={true}
                  />
                )}
              />
          </View>
        </View>

        <View className="flex-row space-x-2">
           <View className="flex-1">
            <ControlledInput
              control={control}
              name="frequency_amount"
              label={t('fields.frequency')}
              placeholder={t('placeholders.frequency')}
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
             <Controller
                control={control}
                name="frequency_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label={t('fields.frequency')}
                    options={frequencyUnitRouterOptions}
                    selectedOption={frequencyUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle={t('medicines.selectFrequencyUnit')}
                    placeholder={t('common.selectFrequency')}
                    error={errors.frequency_unit?.message}
                    clearable={true}
                    showSearchInModal={false}
                  />
                )}
              />
          </View>
        </View>

        <View className="flex-row space-x-2 gap-4">
          <View className="flex-1">
            <ControlledInput
              control={control}
              name="duration_amount"
              label={t('fields.duration')}
              placeholder={t('placeholders.duration')}
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
              <Controller
                control={control}
                name="duration_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label={t('fields.duration')}
                    options={durationUnitRouterOptions}
                    selectedOption={durationUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle={t('medicines.selectDurationUnit')}
                    placeholder={t('common.selectDuration')}
                    error={errors.duration_unit?.message}
                    clearable={true}
                    showSearchInModal={false}
                  />
                )}
              />
          </View>
        </View>

        <View className="mb-4">
          <Controller
            control={control}
            name="patient_id" 
            render={({ field: { onChange, value } }) => (
              <RouterSelectInput
                label="Assign to Patient (Optional)"
                options={patientOptions}
                selectedOption={patientOptions.find(option => option.id === value)}
                onSelect={(option) => onChange(option?.id ?? null)}
                isLoading={patientsQuery.isLoading}
                modalTitle="Select Patient"
                placeholder="Select a patient (optional)"
                error={errors.patient_id?.message}
                showCreateNewButton={true} 
                onCreateNew={() => router.push('/modals/modal-patient')} 
                createText="Create New Patient"
                modalPlaceholder="Search patients..."
                modalEmptyResultsMessage={
                  patientsQuery.isError
                    ? 'Error loading patients'
                    : 'No patients found. You can create one from the Patients screen.'
                }
                clearable={true} 
              />
            )}
          />
        </View>
        <View className="flex-row space-x-2 gap-4">
         <View className="flex-1">
          <Controller
            control={control}
            name="expiration_date"
            render={({ field: { onChange, value } }) => (
              <DatePicker
                label="Expiration Date (Optional)"
                value={value ? new Date(value) : undefined}
                onChange={(date) => onChange(date ? date.toISOString() : null)} 
                placeholder="Select expiration date"
              />
            )}
          />
        </View>

         <View className="flex-1">
          <Controller
            control={control}
            name="opened_on_date"
            render={({ field: { onChange, value } }) => (
              <DatePicker
                label="Opened On Date (Optional)"
                value={value ? new Date(value) : undefined}
                onChange={(date) => onChange(date ? date.toISOString() : null)} 
                placeholder="Select date opened"
                maximumDate={new Date()} 
              />
            )}
          />
        </View>
        </View>
        <View className="flex-1 bg-background dark:bg-neutral-900">
        <ControlledInput
          control={control}
          name="notes"
          label="Notes (Optional)"
          placeholder="e.g., Take with food"
          leftIcon="reader-outline"
          multiline
        />
      </View>
       </View>
      </Animated.ScrollView>
      <Animated.View className="absolute bottom-5 left-4 right-4 h-20 py-3 px-4" entering={SlideInDown.delay(300)}>
        <Button
          title={initialValues ? 'Save Changes' : 'Add Medicine'}
          onPress={handleSubmit(onFormSubmit)}
          variant="primary"
          isLoading={isFormSubmissionLoading}
          icon={initialValues ? "save" : "add-circle"}
          fullWidth={false}
        />
        </Animated.View>
    </View>
  );
};

export default MedicineForm; 