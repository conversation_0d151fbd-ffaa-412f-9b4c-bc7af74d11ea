import React, { useLayoutEffect, useCallback } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { useForm, Resolver, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput'; 
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import { patientFormSchema, PatientFormData } from '@/schema/patient';
import { Patient, usePatients } from '@/hooks/entities/usePatients';
import { Constants } from '@/types/database.types';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { useTranslation } from '@/hooks/useTranslation';

// This will be created inside the component to have access to the translation function
const createBloodTypeOptions = (t: any): RouterSelectOption[] => [
  // Add an option for 'Unknown' or similar to represent null/empty
  { id: '', label: t('bloodTypes.unknown') },
  ...Constants.public.Enums.blood_type_enum.map(type => ({ id: type, label: t(`bloodTypes.${type}`) }))
];

interface PatientFormProps {
  initialValues?: Patient; // Use Patient type for initial values
  onSubmit: (data: PatientFormData) => void;
  isLoading?: boolean;
  isEditing: boolean;
  patientId?: string; // For delete action and to know if delete UI should be shown
}

const PatientForm: React.FC<PatientFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  isEditing,
  patientId,
}) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeletePatient } = usePatients();
  const deletePatientMutation = useDeletePatient();

  // Create blood type options with translations
  const bloodTypeOptions = createBloodTypeOptions(t);

  const { control, handleSubmit } = useForm<PatientFormData>({
    // Explicitly cast the resolver type
    resolver: zodResolver(patientFormSchema) as Resolver<PatientFormData>,
    defaultValues: initialValues ? {
      name: initialValues.name,
      age: initialValues.age ?? undefined,
      // Default to empty string if bloodtype is null/undefined for the Select component
      bloodtype: initialValues.bloodtype ?? '',
    } : {
      name: '',
      age: undefined,
      bloodtype: '', // Default to empty string for Select
    },
  });

  const handleDeletePress = useCallback(() => {
    if (!patientId) return;

    confirm({
      title: t('alerts.confirmDelete'),
      message: t('alerts.deletePatientMessage'),
      confirmText: t('common.delete'),
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deletePatientMutation.mutateAsync({ id: patientId });
          showSuccess(t('alerts.deleteSuccess'), t('common.delete'));
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from PatientForm:', error);
          showError(error.message || t('patients.deleteError'));
        }
      },
    });
  }, [patientId, confirm, deletePatientMutation, showSuccess, showError, router, t]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? t('buttons.editPatient') : t('buttons.addPatient'),
      headerRight: () => (
        isEditing && patientId && (
          <Pressable onPress={handleDeletePress} disabled={deletePatientMutation.isPending} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash-outline"
              size={24}
              color={deletePatientMutation.isPending ? 'gray' : 'red'} 
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, patientId, deletePatientMutation.isPending, handleDeletePress, t]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <ScrollView 
        className="flex-1 px-4" 
        contentContainerStyle={{ paddingVertical: 24 }}
        keyboardShouldPersistTaps="handled"
      >
        {/* Name - ControlledInput */}
        <ControlledInput
          control={control}
          name="name"
          label={t('fields.fullName')}
          placeholder={t('placeholders.patientName')}
          leftIcon="person"
          autoCapitalize="words"
        />

        {/* Age - ControlledInput */}
        <ControlledInput
          control={control}
          name="age"
          label={t('fields.ageOptional')}
          placeholder={t('placeholders.age')}
          leftIcon="calendar-outline"
          keyboardType="number-pad"
        />

        {/* Blood Type */}
        <Controller
          control={control}
          name="bloodtype"
          render={({ field, fieldState: { error } }) => (
            <RouterSelectInput
              label={t('fields.bloodTypeOptional')}
              options={bloodTypeOptions}
              selectedOption={bloodTypeOptions.find(opt => opt.id === field.value) || undefined}
              onSelect={(option) => field.onChange(option ? option.id : '')}
              placeholder={t('patients.selectBloodType')}
              leftIcon="water-outline"
              modalTitle={t('patients.selectBloodType')}
              error={error?.message}
              showSearchInModal={false}
            />
          )}
        />
      </ScrollView>
      <View className="px-4 py-3 border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900">
        <Button
          title={isEditing ? t('buttons.editPatient') : t('buttons.addPatient')}
          onPress={handleSubmit(onSubmit)}
          isLoading={isLoading}
          icon={isEditing ? "save" : "add-circle"}
          fullWidth
        />
      </View>
    </View>
  );
};

export default PatientForm; 