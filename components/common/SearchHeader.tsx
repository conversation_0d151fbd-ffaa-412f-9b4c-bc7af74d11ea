import React from 'react';
import { View, TextInput, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '@react-navigation/native';
import { useTranslation } from '@/hooks/useTranslation';

interface SearchHeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  placeholder?: string;
  theme: Theme; // Pass theme for icon color consistency
  showCreateButton?: boolean;
  onCreateButtonPress?: () => void;
}

export function SearchHeader({
  searchQuery,
  setSearchQuery,
  placeholder,
  theme,
  showCreateButton,
  onCreateButtonPress,
}: SearchHeaderProps) {
  const { t } = useTranslation();
  return (
    <View className="flex-1 flex-row items-center ml-0">
      {/* Using View instead of relative positioning for header integration */}
      <Ionicons
        name="search"
        size={20}
        color={theme.dark ? theme.colors.text : '#6B7280'} // Use theme color
        style={{ marginRight: 8 }} // Removed marginLeft, keep marginRight
      />
      <TextInput
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder={placeholder || t('common.search')}
        className="flex-1 bg-transparent border-b border-border dark:border-neutral-700 py-1 text-foreground dark:text-neutral-100 placeholder-muted-foreground dark:placeholder-neutral-400 text-base mr-2"
        placeholderTextColor={theme.dark ? theme.colors.border : theme.colors.border} // Match placeholder color to theme
        autoFocus={false}
        returnKeyType="search"
        style={{ color: theme.colors.text }} // Ensure text color matches header title color
      />
      {showCreateButton && onCreateButtonPress && (
        <Pressable onPress={onCreateButtonPress} style={{ padding: 4 }}>
          <Ionicons name="add-circle" size={24} color={theme.colors.primary} />
        </Pressable>
      )}
    </View>
  );
} 