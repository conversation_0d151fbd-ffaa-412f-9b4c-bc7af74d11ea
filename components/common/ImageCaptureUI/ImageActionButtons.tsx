import React from 'react';
import { View } from 'react-native';
import { Button } from '../../ui/Button';
import type { ButtonVariant } from '../../ui/Button'; // Import the type
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '@/hooks/useTranslation';

export interface ImageActionButtonsProps {
  onTakePhoto: () => void;
  onChooseFromLibrary: () => void;
  disabled?: boolean;
  captureButtonText?: string;
  libraryButtonText?: string;
  captureButtonIcon?: keyof typeof Ionicons.glyphMap;
  libraryButtonIcon?: keyof typeof Ionicons.glyphMap;
  containerClassName?: string;
  buttonVariant?: ButtonVariant; // Use imported ButtonVariant type
  // Individual styling if needed, though often handled by variant
  takePhotoButtonStyle?: object;
  choosePhotoButtonStyle?: object;
}

const ImageActionButtons: React.FC<ImageActionButtonsProps> = ({
  onTakePhoto,
  onChooseFromLibrary,
  disabled = false,
  captureButtonText,
  libraryButtonText,
  captureButtonIcon = 'camera',
  libraryButtonIcon = 'image',
  containerClassName = "flex-row mb-3",
  buttonVariant = "outline", // 'outline' is a valid ButtonVariant
  takePhotoButtonStyle,
  choosePhotoButtonStyle,
}) => {
  const { t } = useTranslation();
  return (
    <View className={containerClassName}>
      <Button
        title={captureButtonText || t('misc.takePhoto')}
        onPress={onTakePhoto}
        variant={buttonVariant} // This will now be a valid type
        icon={captureButtonIcon}
        style={[{ flex: 1, marginRight: 8 }, takePhotoButtonStyle]}
        disabled={disabled}
      />
      <Button
        title={libraryButtonText || t('misc.chooseFromLibrary')}
        onPress={onChooseFromLibrary}
        variant={buttonVariant} // This will now be a valid type
        icon={libraryButtonIcon}
        style={[{ flex: 1 }, choosePhotoButtonStyle]}
        disabled={disabled}
      />
    </View>
  );
};

export default ImageActionButtons; 