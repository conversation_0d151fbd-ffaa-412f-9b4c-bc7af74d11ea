import React from 'react';
import { View, Text, Pressable, StyleSheet, DimensionValue } from 'react-native';
import { Image as ExpoImage, ImageContentFit, ImageSource } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import Animated, { AnimateStyle } from 'react-native-reanimated';
import { useTranslation } from '@/hooks/useTranslation';

const AnimatedExpoImage = Animated.createAnimatedComponent(ExpoImage);

export interface ImagePreviewProps {
  uri: string | null;
  onPressPreview?: () => void;
  onRemoveImage?: () => void;
  onChangeImage?: () => void;
  showRemoveButton?: boolean;
  showChangeButton?: boolean;
  imageHeight?: number;
  imageWidth?: string | number;
  contentFit?: ImageContentFit;
  aspectRatio?: number;
  placeholderText?: string;
  placeholderImageSource?: ImageSource | number;
  previewLoadError?: boolean;
  onImageLoadError?: () => void;
  onErrorRetry?: () => void;
  containerClassName?: string;
  imageStyleProps?: Partial<StyleSheet.AbsoluteFillStyle>;
  animatedImageStyle?: AnimateStyle<any>;
  controlsContainerClassName?: string;
  controlButtonClassName?: string;
}

const DEFAULT_IMAGE_HEIGHT = 220;
const DEFAULT_PLACEHOLDER_ICON_SIZE = 48;
const DEFAULT_CONTROL_ICON_SIZE = 22;
const DEFAULT_CONTROL_ICON_COLOR = "white";
const DEFAULT_PLACEHOLDER_ICON_NAME: keyof typeof Ionicons.glyphMap = 'image-outline';
const DEFAULT_PLACEHOLDER_ICON_COLOR_CLASS = "text-muted-foreground dark:text-neutral-500";

const ImagePreview: React.FC<ImagePreviewProps> = ({
  uri,
  onPressPreview,
  onRemoveImage,
  onChangeImage,
  showRemoveButton = true,
  showChangeButton = true,
  imageHeight = DEFAULT_IMAGE_HEIGHT,
  imageWidth = '100%',
  contentFit = 'cover',
  aspectRatio,
  placeholderText,
  placeholderImageSource,
  previewLoadError = false,
  onImageLoadError,
  onErrorRetry,
  containerClassName,
  imageStyleProps,
  animatedImageStyle,
  controlsContainerClassName = "absolute bottom-3 gap-3 left-0 right-3 flex-row justify-end space-x-4 z-10",
  controlButtonClassName = "bg-black/60 dark:bg-neutral-900/70 p-2 rounded-full",
}) => {
  const { t } = useTranslation();
  
  // Use translated text if no placeholderText provided
  const displayPlaceholderText = placeholderText ?? t('misc.noImageSelected');

  const imageStyle: import('react-native').ViewStyle = {};
  if (typeof imageWidth === 'number') {
    imageStyle.width = imageWidth;
  } else if (typeof imageWidth === 'string') {
    imageStyle.width = imageWidth as DimensionValue;
  }

  if (aspectRatio) {
    imageStyle.aspectRatio = aspectRatio;
    if (imageStyle.width === '100%' && !imageHeight && typeof imageWidth === 'string') {
    } else if (!imageHeight) {
    } else {
        imageStyle.height = imageHeight 
    }
  } else {
    imageStyle.height = imageHeight;
  }
  
  const actualImageSource = uri ? { uri } : placeholderImageSource;

  if (previewLoadError && !uri) {
    return (
      <View 
        className={`justify-center items-center bg-muted dark:bg-neutral-800 rounded-lg p-4 border-2 border-dashed border-border dark:border-neutral-700 ${containerClassName || ''}`}
        style={imageStyle}
      >
        <Ionicons name={DEFAULT_PLACEHOLDER_ICON_NAME} size={DEFAULT_PLACEHOLDER_ICON_SIZE} className={DEFAULT_PLACEHOLDER_ICON_COLOR_CLASS} />
        <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-2 text-center">{displayPlaceholderText}</Text>
      </View>
    );
  }

  return (
    <View 
      className={`relative bg-muted dark:bg-neutral-800 rounded-lg overflow-hidden ${containerClassName || ''}`}
      style={imageStyle}
    >
      <Pressable onPress={onPressPreview} style={StyleSheet.absoluteFill} disabled={!onPressPreview}>
        {previewLoadError ? (
          <View className="flex-1 justify-center items-center bg-destructive/10 dark:bg-destructive/20 p-4">
            <Ionicons name="warning-outline" size={DEFAULT_PLACEHOLDER_ICON_SIZE} className="text-destructive dark:text-red-400" />
            <Text className="text-xs text-center text-muted-foreground dark:text-neutral-400 mt-1">
              {t('misc.previewUnavailable')}
            </Text>
            {onErrorRetry && (
                <Pressable onPress={onErrorRetry} className="mt-2 p-2 bg-primary/20 rounded-md">
                    <Text className="text-sm text-primary dark:text-blue-300">{t('misc.tryAgain')}</Text>
                </Pressable>
            )}
          </View>
        ) : (
          <AnimatedExpoImage
            source={actualImageSource}
            style={[StyleSheet.absoluteFill, imageStyleProps, animatedImageStyle]}
            contentFit={contentFit}
            onError={onImageLoadError}
            placeholder={{ blurhash: 'LKO2?U%2Tw=w]~RBVZRi};RPxuwH' }}
            transition={300}
          />
        )}
      </Pressable>

      {(showRemoveButton || showChangeButton) && (
        <View className={controlsContainerClassName}>
          {showChangeButton && onChangeImage && (
            <Pressable 
              onPress={onChangeImage}
              className={controlButtonClassName}
            >
              <Ionicons name="camera-reverse-outline" size={DEFAULT_CONTROL_ICON_SIZE} color={DEFAULT_CONTROL_ICON_COLOR} />
            </Pressable>
          )}
          {showRemoveButton && onRemoveImage && (
            <Pressable 
              onPress={onRemoveImage} 
              className={controlButtonClassName}
            >
              <Ionicons name="close-circle-outline" size={DEFAULT_CONTROL_ICON_SIZE} color={DEFAULT_CONTROL_ICON_COLOR} />
            </Pressable>
          )}
        </View>
      )}
    </View>
  );
};

export default ImagePreview; 