import React, { useEffect } from 'react';
import { View, Alert, Pressable } from 'react-native';
import { ImageContentFit, ImageSource } from 'expo-image';
import ImageViewing from 'react-native-image-viewing';
import { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSpring,
  AnimatedStyle 
} from 'react-native-reanimated';

import { useImage, useImageStore, ImageCaptureResult } from '@/store/imageStore';
import { useOpenAIImageOcr } from '@/hooks/useOpenAIImageOcr';
import { useSingleMedicineOcr } from '@/hooks/useSingleMedicineOcr';
import ImagePreview from './ImageCaptureUI/ImagePreview';
import FullScreenStatusIndicator from './FullScreenStatusIndicator';
import { useTranslation } from '@/hooks/useTranslation';

// Re-export the type from the store
export type { ImageCaptureResult } from '@/store/imageStore';

interface ImageCaptureProps {
  storeKey: string; // Key to identify this image in the store
  onImageCaptured: (result: ImageCaptureResult) => void;
  onError?: (error: Error) => void;
  onRemove?: () => void;
  initialImage?: string | null;
  enableOcr?: boolean;
  ocrType?: 'lab' | 'medicine'; // Specify which OCR to use
  triggerOcrOnInitial?: boolean;
  imageHeight?: number;
  imageWidth?: string | number;
  contentFit?: ImageContentFit;
  aspectRatio?: number;
  containerClassName?: string;
  placeholderText?: string;
  placeholderImageSource?: ImageSource | number;
  animatedImageStyle?: AnimatedStyle<any>;
  manipulationOptions?: {
    resizeWidth?: number;
    compressQuality?: number;
  };
  pickerOptions?: {
    allowsEditing?: boolean;
    quality?: number;
    aspect?: [number, number];
  };
}

const ImageCapture: React.FC<ImageCaptureProps> = ({
  storeKey,
  onImageCaptured,
  onError,
  onRemove,
  initialImage,
  enableOcr = true,
  ocrType = 'lab',
  triggerOcrOnInitial = false,
  imageHeight,
  imageWidth,
  contentFit,
  aspectRatio,
  containerClassName,
  placeholderText,
  placeholderImageSource,
  animatedImageStyle: customAnimatedImageStyle,
  manipulationOptions,
  pickerOptions,
}) => {
  const { t } = useTranslation();
  const imageStore = useImageStore();
  const { performOcr: performLabOcr, isProcessingOcr: isProcessingLabOcr } = useOpenAIImageOcr();
  const { performMedicineOcr, isProcessingOcr: isProcessingMedicineOcr } = useSingleMedicineOcr();
  
  // Select the appropriate OCR function and loading state based on ocrType
  const performOcr = ocrType === 'medicine' ? performMedicineOcr : performLabOcr;
  const isProcessingOcr = ocrType === 'medicine' ? isProcessingMedicineOcr : isProcessingLabOcr;
  
  // Get image state from store
  const {
    uri: imageUri,
    isProcessing,
    error: globalError,
    isViewerVisible,
    previewLoadError,
    hasTriggeredInitialOcr,
    actions
  } = useImage(storeKey);
  
  // Initialize image when component mounts or initialImage changes
  React.useEffect(() => {
    if (!imageStore.images[storeKey]) {
      imageStore.initializeImage(storeKey, initialImage);
    } else if (initialImage && imageStore.images[storeKey]?.uri !== initialImage) {
      actions.setUri(initialImage);
      actions.setError(null);
      actions.setOcrResult(null);
      imageStore.setHasTriggeredInitialOcr(storeKey, false);
    }
  }, [storeKey, initialImage, imageStore, actions]);
  
  // Handle OCR for initial image
  React.useEffect(() => {
    if (initialImage && triggerOcrOnInitial && enableOcr && !hasTriggeredInitialOcr) {
      imageStore.setHasTriggeredInitialOcr(storeKey, true);
      performOcr(initialImage)
        .then((result) => {
          if (result) {
            actions.setOcrResult(result);
          }
          // Always trigger callback once, with or without OCR results
          onImageCaptured({
            uri: initialImage,
            ocrText: result?.rawText,
            ocrResults: result?.extractedData,
          });
        })
        .catch((error) => {
          console.error('OCR failed for initial image:', error);
          const ocrError = error instanceof Error ? error : new Error('OCR processing failed');
          actions.setError(ocrError);
          onError?.(ocrError);
          // Trigger callback with just the image
          onImageCaptured({ uri: initialImage });
        });
    }
  }, [initialImage, triggerOcrOnInitial, enableOcr, hasTriggeredInitialOcr, performOcr, onImageCaptured, onError, actions, storeKey, imageStore]);
  
  const isGloballyLoading = isProcessing || isProcessingOcr;

  // --- Default Animation Logic --- 
  const defaultImageOpacity = useSharedValue(0);
  const defaultImageScale = useSharedValue(0.95);

  const defaultAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: defaultImageOpacity.value,
      transform: [{ scale: defaultImageScale.value }],
    };
  });

  useEffect(() => {
    if (imageUri) {
      defaultImageOpacity.value = withTiming(1, { duration: 400 });
      defaultImageScale.value = withSpring(1, { damping: 15, stiffness: 100 });
    } else {
      // Reset instantly if no image, or if image is removed
      defaultImageOpacity.value = 0;
      defaultImageScale.value = 0.95;
    }
  }, [imageUri, defaultImageOpacity, defaultImageScale]); // Added missing dependencies

  const finalAnimatedImageStyle = customAnimatedImageStyle || defaultAnimatedStyle;

  const handleImagePicked = async (result: ImageCaptureResult | null) => {
    if (!result) return;
    
    // Process OCR if enabled
    if (enableOcr) {
      try {
        const ocrResult = await performOcr(result.uri);
        if (ocrResult) {
          actions.setOcrResult(ocrResult);
          result.ocrText = ocrResult.rawText;
          result.ocrResults = ocrResult.extractedData;
        }
      } catch (error) {
        console.error('OCR failed:', error);
        const ocrError = error instanceof Error ? error : new Error('OCR processing failed');
        actions.setError(ocrError);
        onError?.(ocrError);
      }
    }
    
    onImageCaptured(result);
  };
  
  const showImageSourceOptions = () => {
    Alert.alert(
      t('misc.chooseImageSource'),
      t('misc.selectImageSourceMessage'),
      [
        {
          text: t('misc.takePhoto'),
          onPress: async () => {
            try {
              const result = await actions.takePhoto({
                manipulationOptions: manipulationOptions || { resizeWidth: 1024, compressQuality: 0.7 },
                pickerOptions: pickerOptions || { allowsEditing: true, quality: 0.8 },
              });
              await handleImagePicked(result);
            } catch (error) { 
              console.error('Error taking photo:', error);
              onError?.(error instanceof Error ? error : new Error('Failed to take photo'));
            }
          },
        },
        {
          text: t('misc.chooseFromLibrary'),
          onPress: async () => {
            try {
              const result = await actions.chooseFromLibrary({
                manipulationOptions: manipulationOptions || { resizeWidth: 1024, compressQuality: 0.7 },
                pickerOptions: pickerOptions || { allowsEditing: true, quality: 0.8 },
              });
              await handleImagePicked(result);
            } catch (error) { 
              console.error('Error choosing from library:', error);
              onError?.(error instanceof Error ? error : new Error('Failed to choose from library'));
            }
          },
        },
        {
          text: t('common.cancel'),
          style: "cancel",
        },
      ],
      { cancelable: true }
    );
  };

  const handleImagePreviewLoadError = () => {
    actions.clearError(); 
    actions.setPreviewLoadError(true);
    // Propagate this specific error if the main onError prop is provided
    onError?.(new Error('Failed to load image preview. The image might be corrupt or inaccessible.')); 
  };
  
  const handleRemoveImage = () => {
    actions.setUri(null);
    actions.setPreviewLoadError(false);
    actions.setChangingImage(false);
    actions.clearError();
    actions.setOcrResult(null);
    onRemove?.();
  };

  const loadingMessage = isGloballyLoading ? (enableOcr && imageUri ? t('misc.processingWithOcr') : t('misc.processingImage')) : undefined;

  return (
    <View className={containerClassName}>
      <FullScreenStatusIndicator 
        isLoading={isGloballyLoading} 
        error={globalError} 
        loadingMessage={loadingMessage}
        onRetry={globalError ? actions.clearError : undefined}
      />

      {/* Render ImagePreview: either the image, or a pressable placeholder */}
      {/* We don't show this part if a global error is already displayed from the hook, or if globally loading */}
      {!isGloballyLoading && !globalError && (
        <Pressable onPress={!imageUri ? showImageSourceOptions : undefined} disabled={!!imageUri || isGloballyLoading}>
          <ImagePreview
            uri={imageUri} // This will be null for placeholder, or the image URI
            // For placeholder state (imageUri is null):
            // - onPressPreview is not set, the outer Pressable handles it.
            // - onRemoveImage/onChangeImage are irrelevant.
            // For image present state (imageUri is not null):
            // - The outer Pressable is disabled.
            // - onPressPreview opens the viewer.
            // - onRemoveImage calls hook's remove action.
            // - onChangeImage shows source options.
            onPressPreview={imageUri ? () => actions.setViewerVisible(true) : undefined}
            onRemoveImage={imageUri && onRemove ? handleRemoveImage : undefined}
            onChangeImage={imageUri ? showImageSourceOptions : undefined}
            showRemoveButton={!!(imageUri && onRemove)}
            showChangeButton={!!imageUri}
            
            imageHeight={imageHeight}
            imageWidth={imageWidth}
            contentFit={contentFit}
            aspectRatio={aspectRatio}
            placeholderText={placeholderText || t('misc.tapToAddImage')}
            placeholderImageSource={placeholderImageSource}
            previewLoadError={previewLoadError}
            onImageLoadError={handleImagePreviewLoadError}
            onErrorRetry={previewLoadError ? () => actions.setPreviewLoadError(false) : undefined}
            animatedImageStyle={finalAnimatedImageStyle} // Pass the determined style
          />
        </Pressable>
      )}

      {imageUri && (
        <ImageViewing
          images={[{ uri: imageUri }]}
          imageIndex={0}
          visible={isViewerVisible}
          onRequestClose={() => actions.setViewerVisible(false)}
        />
      )}
    </View>
  );
};

export default ImageCapture; 