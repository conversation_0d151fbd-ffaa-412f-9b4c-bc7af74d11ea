import React from 'react';
import { Controller, Control, FieldValues, Path } from 'react-hook-form';
import DatePicker from './ui/DatePicker';

interface ControlledDatePickerProps<TFieldValues extends FieldValues> {
  control: Control<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  placeholder?: string;
  maximumDate?: Date;
  minimumDate?: Date;
}

function ControlledDatePicker<TFieldValues extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  maximumDate,
  minimumDate,
}: ControlledDatePickerProps<TFieldValues>) {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        // Safer check and conversion for date value using getTime check
        let dateValue: Date | undefined = undefined;
        if (typeof value === 'string' && value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            dateValue = parsedDate;
          }
        } else if (value && typeof (value as any).getTime === 'function') {
           // Check if it has a getTime method (likely a Date object)
           const potentialDate = value as Date;
           if (!isNaN(potentialDate.getTime())) { // Final check for validity
              dateValue = potentialDate;
           }
        }
        
        return (
          <DatePicker
            label={label}
            value={dateValue} 
            onChange={(date) => onChange(date ? date.toISOString() : undefined)}
            placeholder={placeholder}
            error={error?.message}
            maximumDate={maximumDate}
            minimumDate={minimumDate}
          />
        );
      }}
    />
  );
}

export default ControlledDatePicker; 