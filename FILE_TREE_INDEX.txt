./AI_CHAT_IMPLEMENTATION_REVIEW.md
./AI_CHAT_REFACTOR_PLAN.md
./android/build/generated/autolinking/autolinking.json
./app.json
./app/_layout.tsx
./app/(protected)/_layout.tsx
./app/(protected)/(ai-chat)/_layout.tsx
./app/(protected)/(ai-chat)/[sessionId].tsx
./app/(protected)/(ai-chat)/welcome.tsx
./app/(protected)/(tabs)/_layout.tsx
./app/(protected)/(tabs)/(doctors-tabs)/_layout.tsx
./app/(protected)/(tabs)/(doctors-tabs)/all-doctors.tsx
./app/(protected)/(tabs)/(doctors-tabs)/doctors.tsx
./app/(protected)/(tabs)/(doctors-tabs)/patients.tsx
./app/(protected)/(tabs)/(meds-tabs)/_layout.tsx
./app/(protected)/(tabs)/(meds-tabs)/all-meds.tsx
./app/(protected)/(tabs)/(meds-tabs)/meds.tsx
./app/(protected)/(tabs)/index.tsx
./app/(protected)/(tabs)/lab-results.tsx
./app/(protected)/(tabs)/notifications.tsx
./app/(protected)/modals/lab-result-webview.tsx
./app/(protected)/modals/modal-doctor.tsx
./app/(protected)/modals/modal-filter.tsx
./app/(protected)/modals/modal-labresult.tsx
./app/(protected)/modals/modal-medicine-ocr.tsx
./app/(protected)/modals/modal-medicine.tsx
./app/(protected)/modals/modal-patient.tsx
./app/(protected)/modals/modal-prescription.tsx
./app/(protected)/modals/router-select-screen.tsx
./app/(protected)/settings.tsx
./app/(public)/_layout.tsx
./app/(public)/forgot-password.tsx
./app/(public)/reset-password.tsx
./app/(public)/sign-in.tsx
./app/(public)/sign-up-email.tsx
./app/(public)/verify-email.tsx
./babel.config.js
./components/chat/chat-input.tsx
./components/chat/chat-interface.tsx
./components/chat/chat-message.tsx
./components/chat/typing-indicator.tsx
./components/chat/welcome-message.tsx
./components/common/DocumentPicker.tsx
./components/common/Filter.tsx
./components/common/FullScreenStatusIndicator.tsx
./components/common/ImageCapture.tsx
./components/common/ImageCaptureUI/ImageActionButtons.tsx
./components/common/ImageCaptureUI/ImagePreview.tsx
./components/common/RouterSelectInput.tsx
./components/common/SearchHeader.tsx
./components/ControlledDatePicker.tsx
./components/ControlledInput.tsx
./components/doctors/DoctorForm.tsx
./components/doctors/UserDoctorCard.tsx
./components/HeaderDropDown.tsx
./components/labresults/LabResultCard.tsx
./components/labresults/LabResultForm.tsx
./components/medications/MedicineCard.tsx
./components/medications/MedicineForm.tsx
./components/medications/MedicineReviewForm.tsx
./components/patients/PatientCard.tsx
./components/patients/PatientForm.tsx
./components/prescriptions/PrescriptionCard.tsx
./components/prescriptions/PrescriptionForm.tsx
./components/ui/Button.tsx
./components/ui/Card.tsx
./components/ui/DatePicker.tsx
./components/ui/Input.tsx
./components/ui/markdown.tsx
./components/WorkingHoursSelector.tsx
./docs/AI_LAB_ANALYSIS_FINAL_SUMMARY.md
./docs/AI_LAB_ANALYSIS_IMPLEMENTATION_PLAN.md
./docs/AI_LAB_ANALYSIS_README.md
./docs/AUTH_IMPLEMENTATION.md
./docs/CLERK_SUPABASE_INTEGRATION.md
./docs/CODE_REVIEW_NEXT_STEPS.md
./docs/codebase_review_summary.md
./docs/feature-plan-additional-pdf-attachment.md
./docs/feature-plan-lab-results-webview.md
./docs/feature-plan-save-webview-as-pdf.md
./docs/IMAGE_DISPLAY_ENHANCEMENT_PLAN.md
./docs/image-handling-improvements.md
./docs/IMPLEMENTATION_PLAN.md
./docs/inline-creation-plan.md
./docs/lab_result_pdf_handling_review.md
./docs/PLAN_ADD_MULTIPLE_MEDICINES_TO_PRESCRIPTION.md
./docs/PROGRESS.md
./docs/query_optimization_guide.md
./eas.json
./eslint.config.js
./GLOBAL_FTS_IMPLEMENTATION_PLAN.md
./hooks/entities/useAllDoctors.ts
./hooks/entities/useAllMedicines.ts
./hooks/entities/useAnalyses.ts
./hooks/entities/useChatSessions.ts
./hooks/entities/useLabResults.ts
./hooks/entities/useMedicines.ts
./hooks/entities/usePatients.ts
./hooks/entities/usePrescriptions.ts
./hooks/entities/useUserDoctors.ts
./hooks/useAIChat.ts
./hooks/useAppAlerts.ts
./hooks/useImageCapture.ts
./hooks/useMedicineReceiptOcr.ts
./hooks/useOpenAIImageOcr.ts
./hooks/useSupabaseClient.ts
./hooks/useSupabaseQuery.ts
./hooks/useSupabaseStorageUpload.ts
./hooks/useSupabaseStorageUrl.ts
./hooks/useWarmBrowser.tsx
./i18n/index.ts
./i18n/locales/bg.json
./i18n/locales/en.json
./IMAGE_CAPTURE_REFACTOR_PLAN.md
./ios/Podfile.properties.json
./ios/Pods/boost/README.md
./ios/Pods/ComputableLayout/LICENSE.md
./ios/Pods/ComputableLayout/README.md
./ios/Pods/ContextMenuAuxiliaryPreview/README.md
./ios/Pods/fast_float/README.md
./ios/Pods/fmt/README.md
./ios/Pods/libavif/README.md
./ios/Pods/libdav1d/dav1d/CONTRIBUTING.md
./ios/Pods/libdav1d/dav1d/README.md
./ios/Pods/libdav1d/dav1d/THANKS.md
./ios/Pods/libdav1d/README.md
./ios/Pods/libwebp/README.md
./ios/Pods/Local Podspecs/boost.podspec.json
./ios/Pods/Local Podspecs/DoubleConversion.podspec.json
./ios/Pods/Local Podspecs/EXApplication.podspec.json
./ios/Pods/Local Podspecs/EXConstants.podspec.json
./ios/Pods/Local Podspecs/EXImageLoader.podspec.json
./ios/Pods/Local Podspecs/EXJSONUtils.podspec.json
./ios/Pods/Local Podspecs/EXManifests.podspec.json
./ios/Pods/Local Podspecs/Expo.podspec.json
./ios/Pods/Local Podspecs/ExpoAsset.podspec.json
./ios/Pods/Local Podspecs/ExpoBlur.podspec.json
./ios/Pods/Local Podspecs/ExpoCamera.podspec.json
./ios/Pods/Local Podspecs/ExpoClipboard.podspec.json
./ios/Pods/Local Podspecs/ExpoCrypto.podspec.json
./ios/Pods/Local Podspecs/ExpoDocumentPicker.podspec.json
./ios/Pods/Local Podspecs/ExpoFileSystem.podspec.json
./ios/Pods/Local Podspecs/ExpoFont.podspec.json
./ios/Pods/Local Podspecs/ExpoHaptics.podspec.json
./ios/Pods/Local Podspecs/ExpoHead.podspec.json
./ios/Pods/Local Podspecs/ExpoImage.podspec.json
./ios/Pods/Local Podspecs/ExpoImageManipulator.podspec.json
./ios/Pods/Local Podspecs/ExpoImagePicker.podspec.json
./ios/Pods/Local Podspecs/ExpoKeepAwake.podspec.json
./ios/Pods/Local Podspecs/ExpoLinking.podspec.json
./ios/Pods/Local Podspecs/ExpoLocalization.podspec.json
./ios/Pods/Local Podspecs/ExpoMediaLibrary.podspec.json
./ios/Pods/Local Podspecs/ExpoModulesCore.podspec.json
./ios/Pods/Local Podspecs/ExpoPrint.podspec.json
./ios/Pods/Local Podspecs/ExpoSecureStore.podspec.json
./ios/Pods/Local Podspecs/ExpoSharing.podspec.json
./ios/Pods/Local Podspecs/ExpoSplashScreen.podspec.json
./ios/Pods/Local Podspecs/ExpoSymbols.podspec.json
./ios/Pods/Local Podspecs/ExpoSystemUI.podspec.json
./ios/Pods/Local Podspecs/ExpoWebBrowser.podspec.json
./ios/Pods/Local Podspecs/EXUpdatesInterface.podspec.json
./ios/Pods/Local Podspecs/fast_float.podspec.json
./ios/Pods/Local Podspecs/FBLazyVector.podspec.json
./ios/Pods/Local Podspecs/fmt.podspec.json
./ios/Pods/Local Podspecs/glog.podspec.json
./ios/Pods/Local Podspecs/hermes-engine.podspec.json
./ios/Pods/Local Podspecs/RCT-Folly.podspec.json
./ios/Pods/Local Podspecs/RCTDeprecation.podspec.json
./ios/Pods/Local Podspecs/RCTRequired.podspec.json
./ios/Pods/Local Podspecs/RCTTypeSafety.podspec.json
./ios/Pods/Local Podspecs/React-callinvoker.podspec.json
./ios/Pods/Local Podspecs/React-Core.podspec.json
./ios/Pods/Local Podspecs/React-CoreModules.podspec.json
./ios/Pods/Local Podspecs/React-cxxreact.podspec.json
./ios/Pods/Local Podspecs/React-debug.podspec.json
./ios/Pods/Local Podspecs/React-defaultsnativemodule.podspec.json
./ios/Pods/Local Podspecs/React-domnativemodule.podspec.json
./ios/Pods/Local Podspecs/React-Fabric.podspec.json
./ios/Pods/Local Podspecs/React-FabricComponents.podspec.json
./ios/Pods/Local Podspecs/React-FabricImage.podspec.json
./ios/Pods/Local Podspecs/React-featureflags.podspec.json
./ios/Pods/Local Podspecs/React-featureflagsnativemodule.podspec.json
./ios/Pods/Local Podspecs/React-graphics.podspec.json
./ios/Pods/Local Podspecs/React-hermes.podspec.json
./ios/Pods/Local Podspecs/React-idlecallbacksnativemodule.podspec.json
./ios/Pods/Local Podspecs/React-ImageManager.podspec.json
./ios/Pods/Local Podspecs/React-jserrorhandler.podspec.json
./ios/Pods/Local Podspecs/React-jsi.podspec.json
./ios/Pods/Local Podspecs/React-jsiexecutor.podspec.json
./ios/Pods/Local Podspecs/React-jsinspector.podspec.json
./ios/Pods/Local Podspecs/React-jsinspectortracing.podspec.json
./ios/Pods/Local Podspecs/React-jsitooling.podspec.json
./ios/Pods/Local Podspecs/React-jsitracing.podspec.json
./ios/Pods/Local Podspecs/React-logger.podspec.json
./ios/Pods/Local Podspecs/React-Mapbuffer.podspec.json
./ios/Pods/Local Podspecs/React-microtasksnativemodule.podspec.json
./ios/Pods/Local Podspecs/react-native-ios-context-menu.podspec.json
./ios/Pods/Local Podspecs/react-native-ios-utilities.podspec.json
./ios/Pods/Local Podspecs/react-native-menu.podspec.json
./ios/Pods/Local Podspecs/react-native-pager-view.podspec.json
./ios/Pods/Local Podspecs/react-native-safe-area-context.podspec.json
./ios/Pods/Local Podspecs/react-native-webview.podspec.json
./ios/Pods/Local Podspecs/React-NativeModulesApple.podspec.json
./ios/Pods/Local Podspecs/React-oscompat.podspec.json
./ios/Pods/Local Podspecs/React-perflogger.podspec.json
./ios/Pods/Local Podspecs/React-performancetimeline.podspec.json
./ios/Pods/Local Podspecs/React-RCTActionSheet.podspec.json
./ios/Pods/Local Podspecs/React-RCTAnimation.podspec.json
./ios/Pods/Local Podspecs/React-RCTAppDelegate.podspec.json
./ios/Pods/Local Podspecs/React-RCTBlob.podspec.json
./ios/Pods/Local Podspecs/React-RCTFabric.podspec.json
./ios/Pods/Local Podspecs/React-RCTFBReactNativeSpec.podspec.json
./ios/Pods/Local Podspecs/React-RCTImage.podspec.json
./ios/Pods/Local Podspecs/React-RCTLinking.podspec.json
./ios/Pods/Local Podspecs/React-RCTNetwork.podspec.json
./ios/Pods/Local Podspecs/React-RCTRuntime.podspec.json
./ios/Pods/Local Podspecs/React-RCTSettings.podspec.json
./ios/Pods/Local Podspecs/React-RCTText.podspec.json
./ios/Pods/Local Podspecs/React-RCTVibration.podspec.json
./ios/Pods/Local Podspecs/React-rendererconsistency.podspec.json
./ios/Pods/Local Podspecs/React-renderercss.podspec.json
./ios/Pods/Local Podspecs/React-rendererdebug.podspec.json
./ios/Pods/Local Podspecs/React-rncore.podspec.json
./ios/Pods/Local Podspecs/React-RuntimeApple.podspec.json
./ios/Pods/Local Podspecs/React-RuntimeCore.podspec.json
./ios/Pods/Local Podspecs/React-runtimeexecutor.podspec.json
./ios/Pods/Local Podspecs/React-RuntimeHermes.podspec.json
./ios/Pods/Local Podspecs/React-runtimescheduler.podspec.json
./ios/Pods/Local Podspecs/React-timing.podspec.json
./ios/Pods/Local Podspecs/React-utils.podspec.json
./ios/Pods/Local Podspecs/React.podspec.json
./ios/Pods/Local Podspecs/ReactAppDependencyProvider.podspec.json
./ios/Pods/Local Podspecs/ReactCodegen.podspec.json
./ios/Pods/Local Podspecs/ReactCommon.podspec.json
./ios/Pods/Local Podspecs/RNCAsyncStorage.podspec.json
./ios/Pods/Local Podspecs/RNDateTimePicker.podspec.json
./ios/Pods/Local Podspecs/RNFlashList.podspec.json
./ios/Pods/Local Podspecs/RNGestureHandler.podspec.json
./ios/Pods/Local Podspecs/RNReanimated.podspec.json
./ios/Pods/Local Podspecs/RNScreens.podspec.json
./ios/Pods/Local Podspecs/Yoga.podspec.json
./ios/Pods/RCT-Folly/README.md
./ios/Pods/SDWebImage/README.md
./ios/Pods/SDWebImageAVIFCoder/README.md
./ios/Pods/SDWebImageSVGCoder/README.md
./ios/Pods/SDWebImageWebPCoder/README.md
./ios/Pods/SocketRocket/README.md
./ios/Pods/ZXingObjC/README.md
./ios/recepturko/Images.xcassets/AppIcon.appiconset/Contents.json
./ios/recepturko/Images.xcassets/Contents.json
./ios/recepturko/Images.xcassets/SplashScreenBackground.colorset/Contents.json
./ios/recepturko/Images.xcassets/SplashScreenLogo.imageset/Contents.json
./MEDICINE_OCR_IMPLEMENTATION_PLAN.md
./metro.config.js
./nativewind-env.d.ts
./OPENAI_OCR_EDGE_FUNCTION_IMPLEMENTATION_PLAN.md
./package.json
./PROJECT_INDEX.md
./README.md
./REFACTOR_PLAN.md
./schema/auth.ts
./schema/doctor.ts
./schema/lab-result.ts
./schema/medicine.ts
./schema/patient.ts
./schema/prescription.ts
./schema/working-hours.ts
./scripts/create-full-backup.md
./store/modalCreationStore.ts
./store/ocrStore.ts
./store/routerSelectStore.ts
./supabase/functions/analyze-lab-result/deno.json
./supabase/functions/analyze-lab-result/index.ts
./supabase/functions/analyze-medication/index.ts
./supabase/functions/chat-universal/index.ts
./supabase/functions/image-ocr-openai/deno.json
./supabase/functions/image-ocr-openai/index.ts
./supabase/functions/medicine-receipt-ocr/deno.json
./supabase/functions/medicine-receipt-ocr/index.ts
./tailwind.config.js
./tsconfig.index.json
./tsconfig.json
./types/database.types.ts
./utils/cache.ts
