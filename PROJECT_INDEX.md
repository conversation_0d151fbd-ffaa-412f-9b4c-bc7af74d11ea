# Recepturko Project Index

Generated on: 2025-06-24

## Project Overview
- **Type**: Expo React Native Application with TypeScript
- **Framework**: Expo 53.0.12, React 19.0.0, React Native 0.79.4
- **Architecture**: File-based routing with Expo Router
- **Backend**: Supabase with Edge Functions
- **Authentication**: Clerk
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **State Management**: Zustand, React Query (TanStack Query)
- **Total Lines of Code**: 13,663 (excluding Supabase functions, node_modules)

## Key Technologies
- **UI Components**: Custom UI components with NativeWind
- **Database**: Supabase PostgreSQL with RLS policies
- **File Storage**: Supabase Storage
- **AI Integration**: OpenAI API for OCR and chat functionality
- **Image Processing**: Expo Image Picker, Image Manipulator
- **Internationalization**: i18next for multi-language support (BG/EN)

## Project Structure

### Application Routes (`/app`)
#### Protected Routes (`/(protected)`)
- **Main Tabs**:
  - `index.tsx` - Home/Dashboard
  - `lab-results.tsx` - Lab results management
  - `notifications.tsx` - Notifications center
  - `settings.tsx` - Application settings

- **Doctor Management (`/(doctors-tabs)`)**:
  - `doctors.tsx` - User's doctors list
  - `all-doctors.tsx` - All available doctors
  - `patients.tsx` - Patient management
  - `_layout.tsx` - Tab layout

- **Medication Management (`/(meds-tabs)`)**:
  - `meds.tsx` - User's medications
  - `all-meds.tsx` - All available medications
  - `_layout.tsx` - Tab layout

- **AI Chat System (`/(ai-chat)`)**:
  - `welcome.tsx` - Chat welcome screen
  - `[sessionId].tsx` - Dynamic chat session
  - `_layout.tsx` - Chat layout

- **Modal Screens (`/modals`)**:
  - `modal-doctor.tsx` - Doctor creation/editing
  - `modal-patient.tsx` - Patient management
  - `modal-medicine.tsx` - Medicine management
  - `modal-medicine-ocr.tsx` - OCR medicine recognition
  - `modal-prescription.tsx` - Prescription management
  - `modal-labresult.tsx` - Lab result management
  - `modal-filter.tsx` - Filtering interface
  - `lab-result-webview.tsx` - WebView for lab results
  - `router-select-screen.tsx` - Navigation selection

#### Public Routes (`/(public)`)
- `sign-in.tsx` - User authentication
- `sign-up-email.tsx` - Email registration
- `reset-password.tsx` - Password reset
- `forgot-password.tsx` - Password recovery
- `verify-email.tsx` - Email verification

### Components (`/components`)
#### UI Components (`/ui`)
- `Button.tsx` - Custom button component
- `Card.tsx` - Card container component
- `Input.tsx` - Form input component
- `DatePicker.tsx` - Date selection component
- `markdown.tsx` - Markdown rendering

#### Feature Components
- **Chat System (`/chat`)**:
  - `chat-interface.tsx` - Main chat interface
  - `chat-input.tsx` - Message input component
  - `chat-message.tsx` - Message display component
  - `typing-indicator.tsx` - Typing animation
  - `welcome-message.tsx` - Chat welcome

- **Common Components (`/common`)**:
  - `ImageCapture.tsx` - Image capture functionality
  - `DocumentPicker.tsx` - File picker component
  - `Filter.tsx` - Filtering component
  - `SearchHeader.tsx` - Search interface
  - `FullScreenStatusIndicator.tsx` - Status display

- **Entity-Specific Components**:
  - **Doctors**: `DoctorForm.tsx`, `UserDoctorCard.tsx`
  - **Patients**: `PatientForm.tsx`, `PatientCard.tsx`
  - **Medications**: `MedicineForm.tsx`, `MedicineCard.tsx`, `MedicineReviewForm.tsx`
  - **Prescriptions**: `PrescriptionForm.tsx`, `PrescriptionCard.tsx`
  - **Lab Results**: `LabResultForm.tsx`, `LabResultCard.tsx`

### Data Layer

#### Hooks (`/hooks`)
- **Entity Hooks (`/entities`)**:
  - `useAllDoctors.ts` - All doctors data management
  - `useUserDoctors.ts` - User's doctors
  - `usePatients.ts` - Patient data
  - `useAllMedicines.ts` - All medicines
  - `useMedicines.ts` - User's medicines
  - `usePrescriptions.ts` - Prescription management
  - `useLabResults.ts` - Lab results
  - `useAnalyses.ts` - Analysis data
  - `useChatSessions.ts` - Chat session management

- **Feature Hooks**:
  - `useAIChat.ts` - AI chat functionality
  - `useImageCapture.ts` - Image capture logic
  - `useMedicineReceiptOcr.ts` - Medicine OCR processing
  - `useOpenAIImageOcr.ts` - OpenAI OCR integration
  - `useSupabaseClient.ts` - Supabase client
  - `useSupabaseQuery.ts` - Database queries
  - `useSupabaseStorageUpload.ts` - File uploads
  - `useSupabaseStorageUrl.ts` - Storage URLs
  - `useAppAlerts.ts` - Alert system
  - `useWarmBrowser.tsx` - Browser warming

#### State Management (`/store`)
- `modalCreationStore.ts` - Modal state management
- `ocrStore.ts` - OCR processing state
- `routerSelectStore.ts` - Navigation state

#### Schema Definitions (`/schema`)
- `auth.ts` - Authentication schemas
- `doctor.ts` - Doctor data validation
- `patient.ts` - Patient data validation
- `medicine.ts` - Medicine data validation
- `prescription.ts` - Prescription validation
- `lab-result.ts` - Lab result validation
- `working-hours.ts` - Working hours validation

#### Types (`/types`)
- `database.types.ts` - Supabase database type definitions

### Backend (`/supabase`)
#### Edge Functions (`/functions`)
- `analyze-lab-result/` - Lab result AI analysis
- `analyze-medication/` - Medication analysis
- `chat-universal/` - Universal chat endpoint
- `image-ocr-openai/` - OpenAI OCR processing
- `medicine-receipt-ocr/` - Medicine receipt OCR

#### Database (`/migrations`)
- `20250127_add_cascade_delete_for_analyses.sql`
- `20250127_add_user_id_to_analyses.sql`
- `20250202000000_optimize_rls_policies.sql`
- `20250523_add_ai_analysis_tables.sql`
- `20250601184646_unified_ai_chat_system.sql`

### Configuration Files
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `app.json` - Expo configuration
- `eas.json` - Expo Application Services
- `babel.config.js` - Babel configuration
- `metro.config.js` - Metro bundler configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `eslint.config.js` - ESLint configuration
- `global.css` - Global styles

### Internationalization (`/i18n`)
- `index.ts` - i18n configuration
- `locales/bg.json` - Bulgarian translations
- `locales/en.json` - English translations

### Documentation (`/docs`)
- Implementation plans and technical documentation
- Code review summaries
- Feature plans and architectural decisions

### Utilities (`/utils`)
- `cache.ts` - Caching utilities

## Key Features
1. **Medical Data Management**: Doctors, patients, medicines, prescriptions, lab results
2. **AI-Powered OCR**: Medicine receipt scanning and lab result analysis
3. **AI Chat System**: Universal chat interface with context-aware responses
4. **Multi-language Support**: Bulgarian and English localization
5. **Image Processing**: Camera integration and image manipulation
6. **Real-time Data**: Supabase real-time subscriptions
7. **Offline Support**: Caching and offline-first approach
8. **Authentication**: Secure user management with Clerk
9. **File Management**: Document picker and storage integration
10. **WebView Integration**: In-app browser for external content

## Development Scripts
- `npm start` - Start Expo development server
- `npm run android` - Run on Android
- `npm run ios` - Run on iOS
- `npm run web` - Run on web
- `npm test` - Run tests
- `npm run lint` - Run ESLint

## Database Schema
The application uses Supabase PostgreSQL with Row Level Security (RLS) policies for:
- User management and authentication
- Medical entities (doctors, patients, medicines, prescriptions)
- Lab results and analyses
- AI chat sessions and messages
- File storage and metadata

This index represents the current state of the Recepturko project as of the indexing date.
