import { useState, useCallback } from 'react';
import * as FileSystem from 'expo-file-system';
import { useSupabaseClient } from './useSupabaseClient'; // Adjust path if needed

interface UploadFileParams {
  fileUri: string;
  bucketId: string;
  path: string;
  fileType?: string;
}

interface UseSupabaseStorageUploadResult {
  uploadFile: (params: UploadFileParams) => Promise<string>; // Returns the path on success
  isLoading: boolean;
  error: Error | null;
}

// Helper function (unexported) to get content type from file URI
const getContentTypeFromUri = (uri: string): string => {
  const extension = uri.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'pdf':
      return 'application/pdf';
    // Add other types as needed
    default:
      return 'application/octet-stream';
  }
};

/**
 * Custom hook to upload a file to Supabase storage using FormData and signed URLs.
 */
export function useSupabaseStorageUpload(): UseSupabaseStorageUploadResult {
  const { supabase } = useSupabaseClient();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const uploadFile = useCallback(async (params: UploadFileParams): Promise<string> => {
    if (!supabase) {
      throw new Error('Supabase client is not available');
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(`useSupabaseStorageUpload: Starting FormData upload: ${params.fileUri} to bucket: ${params.bucketId}, path: ${params.path}`);
      
      const fileInfo = await FileSystem.getInfoAsync(params.fileUri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }
      console.log(`useSupabaseStorageUpload: File size: ${fileInfo.size} bytes`);
      
      const formData = new FormData();
      const fileName = params.path.split('/').pop();
      // Use provided fileType if available, otherwise determine from URI
      const determinedFileType = params.fileType || getContentTypeFromUri(params.fileUri);
      const appendName = fileName || 'upload.dat';
      const appendType = determinedFileType || 'application/octet-stream';

      formData.append('file', {
        uri: params.fileUri,
        name: appendName,
        type: appendType,
      } as any);
      
      const { data: urlData, error: urlError } = await supabase.storage
        .from(params.bucketId)
        .createSignedUploadUrl(params.path);

      if (urlError) {
        throw new Error(`Error getting upload URL: ${urlError.message}`);
      }
      if (!urlData?.signedUrl) {
        throw new Error('Failed to get signed upload URL from Supabase');
      }
      const signedUrl = urlData.signedUrl;

      // Fetch PUT request to upload
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout
      
      let uploadError: Error | null = null;
      try {
        const response = await fetch(signedUrl, {
          method: 'PUT',
          body: formData,
          headers: { /* Let fetch set Content-Type for FormData */ },
          signal: controller.signal,
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorBody = await response.text();
          uploadError = new Error(`Upload failed with status: ${response.status}. Response: ${errorBody}`);
        } else {
          console.log('useSupabaseStorageUpload: Upload appears successful, status:', response.status);
        }
      } catch (fetchError: any) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          uploadError = new Error('Upload timed out after 60 seconds');
        } else {
          uploadError = fetchError;
        }
      }

      if (uploadError) {
        throw uploadError;
      }

      console.log(`useSupabaseStorageUpload: FormData upload successful to ${params.path}`);
      setIsLoading(false);
      return params.path; // Return the path on success

    } catch (err: any) {
      console.error('useSupabaseStorageUpload: File upload error:', err);
      setError(err instanceof Error ? err : new Error('An unexpected error occurred during upload.'));
      setIsLoading(false);
      // Re-throw the error so the calling mutation can handle it
      throw err; 
    }
  }, [supabase]); // Dependency: Supabase client instance

  return { uploadFile, isLoading, error };
}

export default useSupabaseStorageUpload; 