import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSupabaseQuery from '../useSupabaseQuery';
import { Tables, TablesInsert, TablesUpdate } from '@/types/database.types';
import { patientFormSchema, PatientFormData } from '@/schema/patient';

// Types
export type Patient = Tables<'patients'>;

// Hooks for patient operations
export function usePatients() {
  const { executeQuery } = useSupabaseQuery();
  const queryClient = useQueryClient();

  // Fetch all patients for the current user
  const fetchAll = () => {
    return useQuery<Patient[], Error>({
      queryKey: ['patients'],
      queryFn: () => executeQuery(async (supabase, userId) => {
        const { data, error } = await supabase
          .from('patients')
          .select('*')
          .eq('user_id', userId)
          .order('name');

        if (error) {
          throw new Error(`Error fetching patients: ${error.message}`);
        }

        return data as Patient[];
      }),
    });
  };

  // Fetch a single patient by ID
  const fetchById = (id?: string) => {
    return useQuery<Patient, Error>({
      queryKey: ['patient', id],
      queryFn: () => executeQuery(async (supabase, userId) => {
        if (!id) {
          throw new Error('Patient ID is required');
        }

        const { data, error } = await supabase
          .from('patients')
          .select('*')
          .eq('id', id)
          .eq('user_id', userId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') { 
            throw new Error('Patient not found or access denied.');
          }
          throw new Error(`Error fetching patient: ${error.message}`);
        }

        return data;
      }),
      enabled: !!id,
    });
  };

  // Create a new patient
  const create = () => {
    return useMutation<Patient, Error, PatientFormData>({
      mutationFn: async (data) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = patientFormSchema.parse(data);

          const patientData: TablesInsert<'patients'> = {
            ...validatedData,
            bloodtype: validatedData.bloodtype === '' ? null : validatedData.bloodtype,
            age: validatedData.age,
            user_id: userId,
          };

          const { data: newPatient, error } = await supabase
            .from('patients')
            .insert(patientData)
            .select()
            .single();

          if (error) {
            throw new Error(`Error creating patient: ${error.message}`);
          }

          return newPatient as Patient;
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['patients'] });
      }
    });
  };

  // Update an existing patient
  const update = () => {
    return useMutation<Patient, Error, { id: string } & PatientFormData>({
      mutationFn: async ({ id, ...data }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = patientFormSchema.parse(data);

          const patientData: TablesUpdate<'patients'> = {
            ...validatedData,
            bloodtype: validatedData.bloodtype === '' ? null : validatedData.bloodtype,
            age: validatedData.age,
            updated_at: new Date().toISOString(),
          };

          const { data: updatedPatient, error } = await supabase
            .from('patients')
            .update(patientData)
            .eq('id', id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error updating patient: ${error.message}`);
          }

          return updatedPatient as Patient;
        });
      },
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['patients'] });
        queryClient.invalidateQueries({ queryKey: ['patient', variables.id] });
      }
    });
  };

  // Delete a patient
  const remove = () => {
    return useMutation<{ success: boolean; id: string }, Error, { id: string }>({
      mutationFn: async ({ id }) => {
        return executeQuery(async (supabase, userId) => {
          const { count: labResultsCount, error: labResultsError } = await supabase
            .from('labresults')
            .select('id', { count: 'exact', head: true })
            .eq('patient_reference_id', id);

          if (labResultsError) {
            throw new Error(`Error checking lab results: ${labResultsError.message}`);
          }

          const { count: prescriptionsCount, error: prescriptionsError } = await supabase
            .from('prescriptions')
            .select('id', { count: 'exact', head: true })
            .eq('patient_id', id);

          if (prescriptionsError) {
            throw new Error(`Error checking prescriptions: ${prescriptionsError.message}`);
          }

          const { count: medicationsCount, error: medicationsError } = await supabase
            .from('user_meds')
            .select('id', { count: 'exact', head: true })
            .eq('patient_id', id);

          if (medicationsError) {
            throw new Error(`Error checking medications: ${medicationsError.message}`);
          }

          const totalRelatedRecords = (labResultsCount || 0) + (prescriptionsCount || 0) + (medicationsCount || 0);
          
          if (totalRelatedRecords > 0) {
            throw new Error(
              `Cannot delete patient with related records. ` +
              `This patient has ${labResultsCount || 0} lab results, ` +
              `${prescriptionsCount || 0} prescriptions, and ` +
              `${medicationsCount || 0} medications.`
            );
          }

          const { error } = await supabase
            .from('patients')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (error) {
            if (error.code === 'PGRST116' || (error.details && error.details.includes('Results contain 0 rows'))) {
              console.warn(`Attempted to delete non-existent or unauthorized patient: ${id}`);
              return { success: false, id };
            }
            throw new Error(`Error deleting patient: ${error.message}`);
          }

          return { success: true, id };
        });
      },
      onSuccess: (result, variables) => {
        if (result.success) {
           queryClient.invalidateQueries({ queryKey: ['patients'] });
           queryClient.removeQueries({ queryKey: ['patient', variables.id] });
        }
      }
    });
  };

  return {
    useFetchPatients: fetchAll,
    useFetchPatient: fetchById,
    useCreatePatient: create,
    useUpdatePatient: update,
    useDeletePatient: remove
  };
}

export default usePatients; 