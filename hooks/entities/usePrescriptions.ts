import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSupabaseQuery from '../useSupabaseQuery';
import useSupabaseStorageUpload from '../useSupabaseStorageUpload';
import { Tables, TablesInsert, TablesUpdate, Json } from '@/types/database.types';
import {
  prescriptionInsertSchema,
  prescriptionUpdateSchema,
  PrescriptionInsert, // Form data type for create
  PrescriptionUpdate, // Form data type for update
  Prescription as PrescriptionSchemaType, // Schema-based type
} from '@/schema/prescription';

// Constants
const PRESCRIPTIONS_TABLE = 'prescriptions';
const PRESCRIPTIONS_BUCKET = 'prescription_images'; // Or your actual bucket name

// Types
export type Prescription = Tables<'prescriptions'>;
export type PrescriptionWithDetails = Prescription & {
  patients: Pick<Tables<'patients'>, 'id' | 'name'> | null;
  user_doctors: Pick<Tables<'user_doctors'>, 'id' | 'name'> | null;
};

// Type for create mutation input (combining form data and optional file info)
interface PrescriptionMutationData {
  data: PrescriptionInsert; // Use Zod schema type for form data
  frontImageUri?: string;
  backImageUri?: string;
}

// Type for update mutation input (Redefined, does not extend)
interface PrescriptionUpdateMutationData {
  id: string; // Need the ID for update
  data: PrescriptionUpdate; // Use partial update schema type
  frontImageUri?: string;
  backImageUri?: string;
  deleteFrontImage?: boolean;
  deleteBackImage?: boolean;
}

// Helper to upload a single image
async function handleImageUpload(
  uploadFile: ReturnType<typeof useSupabaseStorageUpload>['uploadFile'],
  userId: string,
  imageUri?: string
): Promise<string | undefined> {
  if (!imageUri) return undefined;
  const fileExt = imageUri.split('.').pop()?.toLowerCase() || 'jpg';
  const filePath = `${userId}/${PRESCRIPTIONS_TABLE}/${Date.now()}.${fileExt}`;
  return uploadFile({ fileUri: imageUri, bucketId: PRESCRIPTIONS_BUCKET, path: filePath, fileType: 'image/jpeg' });
}

// Helper to remove a single image
async function handleImageRemove(
  supabase: any, // SupabaseClient type might be complex here, using any for simplicity
  filePath?: string | null
) {
  if (filePath) {
    try {
      await supabase.storage.from(PRESCRIPTIONS_BUCKET).remove([filePath]);
    } catch (error) {
      console.error(`Failed to delete storage file ${PRESCRIPTIONS_BUCKET}/${filePath}:`, error);
      // Optionally re-throw or handle
    }
  }
}

// --- Main Hook Pattern ---
export function usePrescriptions() {
  const { executeQuery } = useSupabaseQuery();
  const queryClient = useQueryClient();
  const { uploadFile } = useSupabaseStorageUpload();

  // Consistent query key generation
  const getListQueryKey = () => [PRESCRIPTIONS_TABLE, 'list'];
  const getDetailQueryKey = (id: string) => [PRESCRIPTIONS_TABLE, 'detail', id];

  // --- Internal Hook Definitions ---

  // Fetch all prescriptions for the current user
  const useFetchPrescriptions = () => {
    return useQuery<PrescriptionWithDetails[], Error>({
      queryKey: getListQueryKey(),
      queryFn: () => executeQuery(async (supabase, userId) => {
        const { data, error } = await supabase
          .from(PRESCRIPTIONS_TABLE)
          .select('*, patients(id, name), user_doctors(id, name)')
          .eq('user_id', userId)
          .order('prescription_date', { ascending: false });

        if (error) {
          throw new Error(`Error fetching prescriptions: ${error.message}`);
        }
        return (data as PrescriptionWithDetails[]) || [];
      }),
    });
  };

  // Fetch a single prescription by ID
  const useFetchPrescription = (id?: string) => {
    return useQuery<PrescriptionWithDetails | null, Error>({
      queryKey: getDetailQueryKey(id || '__placeholder__'), // Use placeholder if id is undefined initially
      queryFn: () => executeQuery(async (supabase, userId) => {
        if (!id) return null;

        const { data, error } = await supabase
          .from(PRESCRIPTIONS_TABLE)
          .select('*, patients(id, name), user_doctors(id, name)')
          .eq('id', id)
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          throw new Error(`Error fetching prescription ${id}: ${error.message}`);
        }
        return (data as PrescriptionWithDetails | null) || null;
      }),
      enabled: !!id, // Only run query if id is truthy
    });
  };

  // Create a new prescription
  const useCreatePrescription = () => {
    return useMutation<Prescription, Error, PrescriptionMutationData>({
      mutationFn: async ({ data, frontImageUri, backImageUri }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = prescriptionInsertSchema.parse(data);
          let frontPath: string | undefined;
          let backPath: string | undefined;
          try {
            [frontPath, backPath] = await Promise.all([
              handleImageUpload(uploadFile, userId, frontImageUri),
              handleImageUpload(uploadFile, userId, backImageUri),
            ]);
            const { image_metadata, ...restValidatedData } = validatedData;
            const insertData: TablesInsert<'prescriptions'> = {
              ...restValidatedData,
              user_id: userId,
              front_image_path: frontPath,
              back_image_path: backPath,
              image_bucket_id: frontPath || backPath ? PRESCRIPTIONS_BUCKET : null,
              notes: validatedData.notes || null,
              patient_id: validatedData.patient_id || null,
              doctor_id: validatedData.doctor_id || null,
            };
            const { data: newPrescription, error } = await supabase
              .from(PRESCRIPTIONS_TABLE)
              .insert(insertData)
              .select()
              .single();
            if (error) throw error;
            return newPrescription as Prescription;
          } catch (error: any) {
            await Promise.all([
              handleImageRemove(supabase, frontPath),
              handleImageRemove(supabase, backPath),
            ]);
            throw new Error(`Error creating prescription: ${error.message}`);
          }
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: getListQueryKey() });
      },
    });
  };

  // Update an existing prescription
  const useUpdatePrescription = () => {
    return useMutation<Prescription, Error, PrescriptionUpdateMutationData>({
      mutationFn: async ({
        id,
        data,
        frontImageUri,
        backImageUri,
        deleteFrontImage = false,
        deleteBackImage = false,
      }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = prescriptionUpdateSchema.parse(data);
          const { data: existing, error: fetchError } = await supabase
            .from(PRESCRIPTIONS_TABLE)
            .select('front_image_path, back_image_path')
            .eq('id', id)
            .eq('user_id', userId)
            .single();

          if (fetchError) throw new Error(`Failed to fetch existing prescription: ${fetchError.message}`);
          if (!existing) throw new Error('Prescription not found or access denied.');

          const oldFrontPath = existing.front_image_path;
          const oldBackPath = existing.back_image_path;
          let newFrontPath: string | undefined | null = oldFrontPath;
          let newBackPath: string | undefined | null = oldBackPath;
          let frontUploadPromise: Promise<string | undefined> = Promise.resolve(undefined);
          let backUploadPromise: Promise<string | undefined> = Promise.resolve(undefined);

          if (deleteFrontImage || frontImageUri) {
            await handleImageRemove(supabase, oldFrontPath);
            newFrontPath = null;
            if (frontImageUri) {
              frontUploadPromise = handleImageUpload(uploadFile, userId, frontImageUri);
            }
          }
          if (deleteBackImage || backImageUri) {
            await handleImageRemove(supabase, oldBackPath);
            newBackPath = null;
            if (backImageUri) {
              backUploadPromise = handleImageUpload(uploadFile, userId, backImageUri);
            }
          }

          try {
            const uploadedFrontPath = await frontUploadPromise;
            const uploadedBackPath = await backUploadPromise;
            if (uploadedFrontPath) newFrontPath = uploadedFrontPath;
            if (uploadedBackPath) newBackPath = uploadedBackPath;

            const updateData: TablesUpdate<'prescriptions'> = {
              ...validatedData,
              front_image_path: newFrontPath,
              back_image_path: newBackPath,
              image_bucket_id: newFrontPath || newBackPath ? PRESCRIPTIONS_BUCKET : null,
              updated_at: new Date().toISOString(),
              notes: validatedData.notes !== undefined ? validatedData.notes : undefined,
              patient_id: validatedData.patient_id !== undefined ? validatedData.patient_id : undefined,
              doctor_id: validatedData.doctor_id !== undefined ? validatedData.doctor_id : undefined,
              image_metadata: validatedData.image_metadata !== undefined ? (validatedData.image_metadata as Json | null | undefined) : undefined,
            };
            const { data: updatedPrescription, error } = await supabase
              .from(PRESCRIPTIONS_TABLE)
              .update(updateData)
              .eq('id', id)
              .eq('user_id', userId)
              .select()
              .single();
            if (error) throw error;
            return updatedPrescription as Prescription;
          } catch (error: any) {
            await Promise.all([
              newFrontPath && newFrontPath !== oldFrontPath ? handleImageRemove(supabase, newFrontPath) : Promise.resolve(),
              newBackPath && newBackPath !== oldBackPath ? handleImageRemove(supabase, newBackPath) : Promise.resolve(),
            ]);
            throw new Error(`Error updating prescription: ${error.message}`);
          }
        });
      },
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries({ queryKey: getListQueryKey() });
        queryClient.invalidateQueries({ queryKey: getDetailQueryKey(variables.id) }); // Use consistent key
      },
    });
  };

  // Delete a prescription
  const useDeletePrescription = () => {
    return useMutation<{ success: boolean; id: string }, Error, { id: string }>({ // Return success status
      mutationFn: async ({ id }) => {
        return executeQuery(async (supabase, userId) => {
          const { data: prescription, error: fetchError } = await supabase
            .from(PRESCRIPTIONS_TABLE)
            .select('front_image_path, back_image_path')
            .eq('id', id)
            .eq('user_id', userId)
            .maybeSingle();

          if (fetchError) throw new Error(`Error fetching prescription for deletion: ${fetchError.message}`);

          if (prescription) {
            try {
              await Promise.all([
                handleImageRemove(supabase, prescription.front_image_path),
                handleImageRemove(supabase, prescription.back_image_path),
              ]);
            } catch (storageError) {
              console.error("Error deleting storage files, proceeding with DB deletion:", storageError);
            }
          }

          const { error: deleteError } = await supabase
            .from(PRESCRIPTIONS_TABLE)
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (deleteError) {
            if (deleteError.code === 'PGRST116' || (deleteError.details && deleteError.details.includes('0 rows'))) {
              console.warn(`Attempted to delete non-existent or unauthorized prescription: ${id}`);
              return { success: false, id };
            }
            throw new Error(`Error deleting prescription: ${deleteError.message}`);
          }
          return { success: true, id };
        });
      },
      onSuccess: (result, variables) => {
        if (result.success) {
          queryClient.invalidateQueries({ queryKey: getListQueryKey() });
          queryClient.removeQueries({ queryKey: getDetailQueryKey(variables.id) }); // Use consistent key
        }
      },
    });
  };

  // --- Return object containing hooks ---
  return {
    useFetchPrescriptions,
    useFetchPrescription,
    useCreatePrescription,
    useUpdatePrescription,
    useDeletePrescription,
  };
}

// Default export the main hook function
export default usePrescriptions;

// --- Standalone helper re-exports (if needed elsewhere, though pattern is to use the main hook) ---
// export { handleImageUpload, handleImageRemove };

// --- Re-export types for convenience ---
export type { PrescriptionMutationData, PrescriptionUpdateMutationData }; 