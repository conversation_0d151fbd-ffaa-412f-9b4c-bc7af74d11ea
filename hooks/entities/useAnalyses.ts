import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSupabaseClient } from "@/hooks/useSupabaseClient";
import { useAuth } from "@clerk/clerk-expo";
import { Database } from "@/types/database.types";

export type LabResultAnalysis =
  Database["public"]["Tables"]["lab_result_analyses"]["Row"];
export type MedicationAnalysis =
  Database["public"]["Tables"]["medication_analyses"]["Row"];

export function useAnalyses() {
  const { supabase } = useSupabaseClient();
  const { userId, getToken } = useAuth();
  const queryClient = useQueryClient();

  // Lab Result Analysis Queries
  const useFetchLabResultAnalysis = (labResultId?: string) => {
    return useQuery({
      queryKey: ["lab-analysis", labResultId],
      queryFn: async (): Promise<LabResultAnalysis | null> => {
        if (!labResultId || !supabase || !userId) return null;

        const { data, error } = await supabase
          .from("lab_result_analyses")
          .select("*")
          .eq("lab_result_id", labResultId)
          .eq("user_id", userId)
          .single();

        if (error && error.code !== "PGRST116") {
          throw new Error(`Error fetching lab analysis: ${error.message}`);
        }

        return data || null;
      },
      enabled: !!labResultId && !!supabase && !!userId,
    });
  };

  // Medication Analysis Queries
  const useFetchMedicationAnalysis = (medicationId?: string) => {
    return useQuery({
      queryKey: ["medication-analysis", medicationId],
      queryFn: async (): Promise<MedicationAnalysis | null> => {
        if (!medicationId || !supabase || !userId) return null;

        const { data, error } = await supabase
          .from("medication_analyses")
          .select("*")
          .eq("medication_id", medicationId)
          .eq("user_id", userId)
          .single();

        if (error && error.code !== "PGRST116") {
          throw new Error(
            `Error fetching medication analysis: ${error.message}`
          );
        }

        return data || null;
      },
      enabled: !!medicationId && !!supabase && !!userId,
    });
  };

  // Create Lab Analysis Mutation
  const useCreateLabAnalysis = () => {
    return useMutation({
      mutationFn: async ({ labResultId, sessionId }: { labResultId: string; sessionId?: string }): Promise<LabResultAnalysis> => {
        if (!supabase || !userId) throw new Error("User not authenticated");

        const token = await getToken();
        if (!token) throw new Error("No access token");

        const response = await globalThis.fetch(
          `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/analyze-lab-result`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ labResultId, sessionId }),
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.analysis;
      },
      onSuccess: (data, { labResultId }) => {
        // Invalidate and update related queries
        queryClient.invalidateQueries({
          queryKey: ["lab-analysis", labResultId],
        });
        queryClient.invalidateQueries({ queryKey: ["chat-sessions"] });

        // Optionally set the data directly in cache
        queryClient.setQueryData(["lab-analysis", labResultId], data);
      },
    });
  };

  // Create Medication Analysis Mutation
  const useCreateMedicationAnalysis = () => {
    return useMutation({
      mutationFn: async ({ medicationId, sessionId }: { medicationId: string; sessionId?: string }): Promise<MedicationAnalysis> => {
        if (!supabase || !userId) throw new Error("User not authenticated");

        const token = await getToken();
        if (!token) throw new Error("No access token");

        const response = await globalThis.fetch(
          `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/analyze-medication`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ medicationId, sessionId }),
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.analysis;
      },
      onSuccess: (data, { medicationId }) => {
        // Invalidate and update related queries
        queryClient.invalidateQueries({
          queryKey: ["medication-analysis", medicationId],
        });
        queryClient.invalidateQueries({ queryKey: ["chat-sessions"] });

        // Optionally set the data directly in cache
        queryClient.setQueryData(["medication-analysis", medicationId], data);
      },
    });
  };

  return {
    useFetchLabResultAnalysis,
    useFetchMedicationAnalysis,
    useCreateLabAnalysis,
    useCreateMedicationAnalysis,
  };
} 