import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSupabaseQuery from '../useSupabaseQuery';
import { Tables, TablesInsert, TablesUpdate } from '@/types/database.types';
import { doctorFormSchema, DoctorFormData } from '@/schema/doctor';

// Define the detailed type including related alldoctors data
export type UserDoctorWithDetails = Tables<'user_doctors'> & {
  alldoctors: Pick<Tables<'alldoctors'>, 'fee' | 'workplace_address'> | null;
};

// Renamed types for consistency
export type UserDoctor = Tables<'user_doctors'>;

// Hooks
export function useUserDoctors() {
  const { executeQuery } = useSupabaseQuery();
  const queryClient = useQueryClient();

  // Fetch all user doctors with potential related alldoctors info
  const fetchAll = () => {
    return useQuery<UserDoctorWithDetails[], Error>({
      queryKey: ['userDoctors'],
      queryFn: () => executeQuery(async (supabase, userId) => {
        const { data, error } = await supabase
          .from('user_doctors')
          .select('*, alldoctors(fee, workplace_address)')
          .eq('user_id', userId)
          .order('name');

        if (error) {
          throw new Error(`Error fetching user doctors: ${error.message}`);
        }
        return data as UserDoctorWithDetails[]; 
      }),
    });
  };

  // Fetch a single user doctor by ID
  const fetchById = (id?: string) => {
    return useQuery<UserDoctorWithDetails, Error>({
      queryKey: ['userDoctor', id],
      queryFn: () => executeQuery(async (supabase, userId) => {
        if (!id) {
          throw new Error('Doctor ID is required');
        }
        const { data, error } = await supabase
          .from('user_doctors')
          .select('*, alldoctors(fee, workplace_address)')
          .eq('id', id)
          .eq('user_id', userId)
          .single();

        if (error) {
           if (error.code === 'PGRST116') { 
             throw new Error('Doctor not found or access denied.'); 
           }
          throw new Error(`Error fetching user doctor: ${error.message}`);
        }
        return data as UserDoctorWithDetails;
      }),
      enabled: !!id,
    });
  };

  // Create a new user doctor
  const create = () => {
    return useMutation<UserDoctor, Error, DoctorFormData>({
      mutationFn: async (data) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = doctorFormSchema.parse(data);
          
          const doctorData: TablesInsert<'user_doctors'> = {
            ...validatedData,
            user_id: userId,
            is_custom: !validatedData.doctor_id,
            specialty: validatedData.specialty || null,
            workplace: validatedData.workplace || null,
            workplace_address: validatedData.workplace_address || null,
            phone_number: validatedData.phone_number || null,
            city: validatedData.city || null,
            notes: validatedData.notes || null,
            fee: validatedData.fee,
            doctor_id: validatedData.doctor_id || null,
          };

          const { data: newUserDoctor, error } = await supabase
            .from('user_doctors')
            .insert(doctorData)
            .select()
            .single();

          if (error) {
            throw new Error(`Error creating user doctor: ${error.message}`);
          }
          return newUserDoctor as UserDoctor;
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userDoctors'] });
      },
    });
  };

  // Update an existing user doctor
  const update = () => {
    return useMutation<UserDoctor, Error, { id: string } & DoctorFormData>({
      mutationFn: async ({ id, ...data }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = doctorFormSchema.parse(data);

          const doctorData: TablesUpdate<'user_doctors'> = {
            ...validatedData,
            specialty: validatedData.specialty || null,
            workplace: validatedData.workplace || null,
            workplace_address: validatedData.workplace_address || null,
            phone_number: validatedData.phone_number || null,
            city: validatedData.city || null,
            notes: validatedData.notes || null,
            fee: validatedData.fee,
            updated_at: new Date().toISOString(),
          };

          const { data: updatedUserDoctor, error } = await supabase
            .from('user_doctors')
            .update(doctorData)
            .eq('id', id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error updating user doctor: ${error.message}`);
          }
          return updatedUserDoctor as UserDoctor;
        });
      },
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['userDoctors'] });
        queryClient.invalidateQueries({ queryKey: ['userDoctor', variables.id] });
      },
    });
  };

  // Delete a user doctor
  const remove = () => {
    return useMutation<{ success: boolean; id: string }, Error, { id: string }>({
      mutationFn: async ({ id }) => {
        return executeQuery(async (supabase, userId) => {
          const { count: prescriptionCount, error: checkError } = await supabase
            .from('prescriptions')
            .select('id', { count: 'exact', head: true })
            .eq('doctor_id', id);
            
          if (checkError) {
            throw new Error(`Error checking related prescriptions: ${checkError.message}`);
          }
          if (prescriptionCount && prescriptionCount > 0) {
            throw new Error(`Cannot delete doctor with ${prescriptionCount} associated prescription(s). Please reassign or delete them first.`);
          }

          const { error } = await supabase
            .from('user_doctors')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (error) {
             if (error.code === 'PGRST116' || (error.details && error.details.includes('Results contain 0 rows'))) {
              console.warn(`Attempted to delete non-existent or unauthorized doctor: ${id}`);
              return { success: false, id };
            }
            throw new Error(`Error deleting user doctor: ${error.message}`);
          }
          return { success: true, id };
        });
      },
       onSuccess: (result, variables) => {
        if (result.success) {
           queryClient.invalidateQueries({ queryKey: ['userDoctors'] });
           queryClient.removeQueries({ queryKey: ['userDoctor', variables.id] });
        }
      },
    });
  };

  return {
    useFetchUserDoctors: fetchAll,
    useFetchUserDoctor: fetchById,
    useCreateUserDoctor: create,
    useUpdateUserDoctor: update,
    useDeleteUserDoctor: remove,
  };
}

export default useUserDoctors; 