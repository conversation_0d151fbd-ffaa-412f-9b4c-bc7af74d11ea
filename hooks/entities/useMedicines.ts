import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSupabaseQuery from '../useSupabaseQuery';
import { Tables, TablesInsert, TablesUpdate } from '@/types/database.types';
import useSupabaseStorageUpload from '../useSupabaseStorageUpload';
import { medicineFormSchema, MedicineFormData } from '@/schema/medicine'; // Import the medicine schema

// Types
export type Medicine = Tables<'user_meds'>;
export type MedicineWithRelations = Medicine & {
  patients: Pick<Tables<'patients'>, 'id' | 'name'> | null; // Assuming you might want patient info
  // Add other relations like prescriptions if needed
};

// Type for mutation input (combining form data and optional file info)
interface MedicineMutationData {
  data: MedicineFormData;
  fileUri?: string; // For image upload
}

interface MedicineUpdateMutationData extends MedicineMutationData {
  id: string; // Need the ID for update
  deleteFile?: boolean;
}

interface BulkReceiptMedicineData {
  medicines: Array<{
    name: string;
    price?: number;
    description?: string;
    patient_id?: string;
    notes?: string;
  }>;
  receiptImageUri?: string;
}

// Hooks for medicine operations
export function useMedicines() {
  const { executeQuery } = useSupabaseQuery();
  const queryClient = useQueryClient();
  const { uploadFile } = useSupabaseStorageUpload();
  const imageBucketId = 'user-meds'; // Correct bucket name

  // Fetch all medicines for the current user
  const fetchAll = () => {
    return useQuery<MedicineWithRelations[], Error>({
      queryKey: ['medicines'],
      queryFn: () => executeQuery(async (supabase, userId) => {
        const { data, error } = await supabase
          .from('user_meds')
          .select('*, patients(id, name)') // Select related patient data
          .eq('user_id', userId)
          .order('created_at', { ascending: false }); // Or order by name, expiration_date etc.

        if (error) {
          throw new Error(`Error fetching medicines: ${error.message}`);
        }
        return data as MedicineWithRelations[];
      }),
    });
  };

  // Fetch a single medicine by ID
  const fetchById = (id?: string) => {
    return useQuery<MedicineWithRelations, Error>({
      queryKey: ['medicine', id],
      queryFn: () => executeQuery(async (supabase, userId) => {
        if (!id) {
          throw new Error('Medicine ID is required');
        }
        const { data, error } = await supabase
          .from('user_meds')
          .select('*, patients(id, name)') // Select related patient data
          .eq('id', id)
          .eq('user_id', userId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') { 
            throw new Error('Medicine not found or access denied.'); 
          }
          throw new Error(`Error fetching medicine: ${error.message}`);
        }
        return data as MedicineWithRelations;
      }),
      enabled: !!id,
    });
  };

  // Create a new medicine
  const create = () => {
    return useMutation<Medicine, Error, MedicineMutationData>({
      mutationFn: async ({ data, fileUri }) => {
        return executeQuery(async (supabase, userId) => {
          // Validate data using the medicine schema
          const validatedData = medicineFormSchema.parse(data);

          // Upload file logic
          let filePath: string | undefined;
          if (fileUri) {
            const fileExt = fileUri.split('.').pop()?.toLowerCase() || 'jpg';
            filePath = `${userId}/${Date.now()}.${fileExt}`;
            filePath = await uploadFile({ fileUri, bucketId: imageBucketId, path: filePath, fileType: 'image/jpeg' });
          }

          // Prepare data, converting empty strings to null, etc.
          const medicineData: TablesInsert<'user_meds'> = {
            ...validatedData,
            description: validatedData.description || null,
            dosage_amount: validatedData.dosage_amount,
            dosage_unit: validatedData.dosage_unit,
            frequency_amount: validatedData.frequency_amount,
            frequency_unit: validatedData.frequency_unit,
            duration_amount: validatedData.duration_amount,
            duration_unit: validatedData.duration_unit,
            expiration_date: validatedData.expiration_date || null,
            opened_on_date: validatedData.opened_on_date || null,
            patient_id: validatedData.patient_id || null,
            prescription_id: validatedData.prescription_id || null,
            notes: validatedData.notes || null,
            price: validatedData.price,
            med_id: validatedData.med_id || null,
            is_custom: validatedData.med_id ? false : true, // Set is_custom based on med_id presence
            user_id: userId,
            image_path: filePath,
            image_bucket_id: filePath ? imageBucketId : null,
          };

          // Insert the medicine
          const { data: newMedicine, error } = await supabase
            .from('user_meds')
            .insert(medicineData)
            .select()
            .single();

          if (error) {
            // Clean up uploaded file if db insert fails
            if (filePath) {
              await supabase.storage.from(imageBucketId).remove([filePath]);
            }
            throw new Error(`Error creating medicine: ${error.message}`);
          }
          return newMedicine as Medicine;
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['medicines'] });
      }
    });
  };

  // Update an existing medicine
  const update = () => {
    return useMutation<Medicine, Error, MedicineUpdateMutationData>({
      mutationFn: async ({ id, data, fileUri, deleteFile = false }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = medicineFormSchema.parse(data);

          const { data: existingMedicine, error: fetchError } = await supabase
            .from('user_meds')
            .select('image_path, image_bucket_id')
            .eq('id', id)
            .eq('user_id', userId)
            .single();

          if (fetchError) {
            if (fetchError.code === 'PGRST116') {
                throw new Error('Medicine not found or access denied.');
            }
            throw new Error(`Error fetching existing medicine: ${fetchError.message}`);
          }
          if (!existingMedicine) {
            throw new Error('Medicine not found or access denied after fetch.');
          }

          const oldFilePath = existingMedicine.image_path;
          const oldBucketId = existingMedicine.image_bucket_id || imageBucketId;
          let filePath: string | null = oldFilePath; 
          let currentImageBucketId: string | null = oldBucketId;

          if (deleteFile || fileUri) {
            // Delete old file if requested or replacing
            if (oldFilePath && currentImageBucketId) {
                try {
                  await supabase.storage.from(currentImageBucketId).remove([oldFilePath]);
                } catch (storageError) {
                  console.error(`Failed to delete old storage file ${currentImageBucketId}/${oldFilePath}:`, storageError);
                  // Decide if deletion failure should prevent update
                }
              filePath = null;
              currentImageBucketId = null; 
            }
            // Upload new file if provided
            if (fileUri) {
              currentImageBucketId = imageBucketId; 
              const fileExt = fileUri.split('.').pop()?.toLowerCase() || 'jpg';
              const newFilePath = `${userId}/${Date.now()}.${fileExt}`;
              filePath = await uploadFile({ fileUri, bucketId: currentImageBucketId, path: newFilePath, fileType: 'image/jpeg' });
            }
          }

          // Prepare update data
          const medicineUpdateData: TablesUpdate<'user_meds'> = {
            ...validatedData,
            description: validatedData.description || null,
            dosage_amount: validatedData.dosage_amount,
            dosage_unit: validatedData.dosage_unit,
            frequency_amount: validatedData.frequency_amount,
            frequency_unit: validatedData.frequency_unit,
            duration_amount: validatedData.duration_amount,
            duration_unit: validatedData.duration_unit,
            expiration_date: validatedData.expiration_date || null,
            opened_on_date: validatedData.opened_on_date || null,
            patient_id: validatedData.patient_id || null,
            prescription_id: validatedData.prescription_id || null,
            notes: validatedData.notes || null,
            price: validatedData.price,
            med_id: validatedData.med_id || null,
            is_custom: validatedData.med_id ? false : true,
            updated_at: new Date().toISOString(),
            image_path: filePath,
            image_bucket_id: currentImageBucketId, // Use the determined bucket ID
          };

          // Update the medicine
          const { data: updatedMedicine, error } = await supabase
            .from('user_meds')
            .update(medicineUpdateData)
            .eq('id', id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) {
            // Clean up newly uploaded file if update fails
            if (fileUri && filePath && filePath !== oldFilePath && currentImageBucketId) {
               try {
                 await supabase.storage.from(currentImageBucketId).remove([filePath]);
               } catch (cleanupError) {
                 console.error(`Failed to cleanup failed update upload ${currentImageBucketId}/${filePath}:`, cleanupError);
               }
            }
            throw new Error(`Error updating medicine: ${error.message}`);
          }
          return updatedMedicine as Medicine;
        });
      },
      onSuccess: (_, variables) => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['medicines'] });
        queryClient.invalidateQueries({ queryKey: ['medicine', variables.id] });
      }
    });
  };

  // Delete a medicine
  const remove = () => {
    return useMutation<{ success: boolean; id: string }, Error, { id: string }>({
      mutationFn: async ({ id }) => {
        return executeQuery(async (supabase, userId) => {
          // Fetch the medicine to get the file path
          const { data: medicine, error: fetchError } = await supabase
            .from('user_meds')
            .select('image_path, image_bucket_id')
            .eq('id', id)
            .eq('user_id', userId)
            .single();

          if (fetchError) {
             if (fetchError.code === 'PGRST116') {
              console.warn(`Attempted to delete non-existent or unauthorized medicine: ${id}`);
              return { success: false, id }; 
            } 
            throw new Error(`Error fetching medicine for deletion: ${fetchError.message}`);
          }
          
          // Delete the file if it exists
          if (medicine.image_path && medicine.image_bucket_id) {
            try {
              await supabase.storage.from(medicine.image_bucket_id).remove([medicine.image_path]);
            } catch (storageError) {
                console.error(`Failed to delete storage file ${medicine.image_bucket_id}/${medicine.image_path}:`, storageError);
                // Decide if you want to proceed with DB deletion despite storage error
            }
          }

          // Delete the medicine record
          const { error } = await supabase
            .from('user_meds')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (error) {
             if (error.code === 'PGRST116' || (error.details && error.details.includes('Results contain 0 rows'))) {
               console.warn(`Attempted to delete non-existent or unauthorized medicine (delete step): ${id}`);
              return { success: false, id };
            }
            throw new Error(`Error deleting medicine: ${error.message}`);
          }
          return { success: true, id };
        });
      },
      onSuccess: (result, variables) => {
         if (result.success) {
            // Invalidate list and remove specific item query
            queryClient.invalidateQueries({ queryKey: ['medicines'] });
            queryClient.removeQueries({ queryKey: ['medicine', variables.id] });
         }
      }
    });
  };

  // Bulk create medicines from receipt
  const bulkCreateFromReceipt = () => {
    return useMutation<Medicine[], Error, BulkReceiptMedicineData>({
      mutationFn: async ({ medicines, receiptImageUri }) => {
        return executeQuery(async (supabase, userId) => {
          // Upload receipt image once (shared by all medicines)
          let receiptImagePath: string | null = null;
          if (receiptImageUri) {
            const fileExt = receiptImageUri.split('.').pop()?.toLowerCase() || 'jpg';
            const receiptPath = `${userId}/receipts/${Date.now()}.${fileExt}`;
            receiptImagePath = await uploadFile({ 
              fileUri: receiptImageUri, 
              bucketId: imageBucketId, 
              path: receiptPath, 
              fileType: 'image/jpeg' 
            });
          }

          // Prepare bulk insert data
          const medicineInserts: TablesInsert<'user_meds'>[] = medicines.map(med => ({
            name: med.name,
            price: med.price || null,
            description: med.description || null,
            opened_on_date: new Date().toISOString(),
            patient_id: med.patient_id || null,
            notes: med.notes || null,
            is_custom: true,
            user_id: userId,
            receipt_image_path: receiptImagePath, // Receipt image in dedicated column
            receipt_image_bucket_id: receiptImagePath ? imageBucketId : null,
            // Main image fields remain null (separate from receipt)
            image_path: null,
            image_bucket_id: null,
          }));

          // Bulk insert all medicines
          const { data: newMedicines, error } = await supabase
            .from('user_meds')
            .insert(medicineInserts)
            .select();

          if (error) {
            // Clean up receipt image if bulk insert fails
            if (receiptImagePath) {
              await supabase.storage.from(imageBucketId).remove([receiptImagePath]);
            }
            throw new Error(`Error creating medicines: ${error.message}`);
          }

          return newMedicines as Medicine[];
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['medicines'] });
      }
    });
  };

  return {
    useFetchMedicines: fetchAll,
    useFetchMedicine: fetchById,
    useCreateMedicine: create,
    useUpdateMedicine: update,
    useDeleteMedicine: remove,
    useBulkCreateFromReceipt: bulkCreateFromReceipt
  };
}

export default useMedicines; 