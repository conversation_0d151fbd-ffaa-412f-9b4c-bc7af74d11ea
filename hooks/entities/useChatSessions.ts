import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSupabaseClient } from "@/hooks/useSupabaseClient";
import { useAuth } from "@clerk/clerk-expo";
import { Database } from "@/types/database.types";

export type ChatSession =
  Database["public"]["Tables"]["ai_chat_sessions"]["Row"];
export type ChatMessage =
  Database["public"]["Tables"]["ai_chat_messages"]["Row"];

export function useChatSessions() {
  const { supabase } = useSupabaseClient();
  const { userId } = useAuth();
  const queryClient = useQueryClient();

  // Fetch all chat sessions
  const useFetchChatSessions = (filters?: {
    type?: string;
    search?: string;
  }) => {
    return useQuery({
      queryKey: ["chat-sessions", filters],
      queryFn: async (): Promise<ChatSession[]> => {
        if (!supabase || !userId) return [];

        let query = supabase
          .from("ai_chat_sessions")
          .select("*")
          .eq("user_id", userId)
          .order("updated_at", { ascending: false });

        if (filters?.type && filters.type !== "all") {
          query = query.eq("chat_type", filters.type);
        }

        if (filters?.search) {
          query = query.ilike("title", `%${filters.search}%`);
        }

        const { data, error } = await query;

        if (error) {
          throw new Error(`Error fetching chat sessions: ${error.message}`);
        }

        return data || [];
      },
      enabled: !!supabase && !!userId,
    });
  };

  // Fetch single session by ID
  const useFetchSession = (sessionId?: string) => {
    return useQuery({
      queryKey: ["chat-session", sessionId],
      queryFn: async (): Promise<ChatSession | null> => {
        if (!sessionId || !supabase || !userId) return null;

        const { data, error } = await supabase
          .from("ai_chat_sessions")
          .select("*")
          .eq("id", sessionId)
          .eq("user_id", userId)
          .single();

        if (error && error.code !== "PGRST116") {
          throw new Error(`Error fetching session: ${error.message}`);
        }

        return data || null;
      },
      enabled: !!sessionId && !!supabase && !!userId,
    });
  };

  // Fetch session by context
  const useFetchSessionByContext = (type: string, contextId?: string) => {
    return useQuery({
      queryKey: ["chat-session", type, contextId],
      queryFn: async (): Promise<ChatSession | null> => {
        if (!contextId || !supabase || !userId) return null;

        const { data, error } = await supabase
          .from("ai_chat_sessions")
          .select("*")
          .eq("user_id", userId)
          .eq("chat_type", type)
          .eq("context_id", contextId)
          .single();

        if (error && error.code !== "PGRST116") {
          throw new Error(`Error fetching session: ${error.message}`);
        }

        return data || null;
      },
      enabled: !!contextId && !!supabase && !!userId,
    });
  };

  // Fetch messages for a session
  const useFetchMessages = (sessionId?: string) => {
    return useQuery({
      queryKey: ["chat-messages", sessionId],
      queryFn: async (): Promise<ChatMessage[]> => {
        if (!sessionId || !supabase || !userId) return [];

        const { data, error } = await supabase
          .from("ai_chat_messages")
          .select("*")
          .eq("session_id", sessionId)
          .order("created_at", { ascending: true });

        if (error) {
          throw new Error(`Error fetching messages: ${error.message}`);
        }

        return data || [];
      },
      enabled: !!sessionId && !!supabase && !!userId,
    });
  };

  // Create chat session
  const useCreateChatSession = () => {
    return useMutation({
      mutationFn: async ({
        type,
        contextId,
        title,
      }: {
        type: "general" | "lab_result" | "medication";
        contextId?: string;
        title?: string;
      }): Promise<ChatSession> => {
        if (!supabase || !userId) throw new Error("User not authenticated");

        // Generate title based on type if not provided
        let sessionTitle = title;
        if (!sessionTitle) {
          switch (type) {
            case "lab_result":
              sessionTitle = "Lab Results Analysis";
              break;
            case "medication":
              sessionTitle = "Medication Analysis";
              break;
            default:
              sessionTitle = "General Chat";
          }
        }

        const { data, error } = await supabase
          .from("ai_chat_sessions")
          .insert({
            user_id: userId,
            chat_type: type,
            title: sessionTitle,
            context_id: contextId,
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Error creating session: ${error.message}`);
        }

        return data;
      },
      onSuccess: (data, variables) => {
        // Invalidate sessions list
        queryClient.invalidateQueries({ queryKey: ["chat-sessions"] });

        // Set the new session in cache
        queryClient.setQueryData(
          ["chat-session", variables.type, variables.contextId],
          data
        );
        queryClient.setQueryData(["chat-session", data.id], data);

        // Initialize empty messages for this session
        queryClient.setQueryData(["chat-messages", data.id], []);
      },
    });
  };

  // Create chat message
  const useCreateMessage = () => {
    return useMutation({
      mutationFn: async ({
        sessionId,
        role,
        content,
      }: {
        sessionId: string;
        role: "user" | "assistant" | "system";
        content: string;
      }): Promise<ChatMessage> => {
        if (!supabase || !userId) throw new Error("User not authenticated");

        const { data, error } = await supabase
          .from("ai_chat_messages")
          .insert({
            session_id: sessionId,
            role,
            content,
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Error creating message: ${error.message}`);
        }

        return data;
      },
      onSuccess: (data, variables) => {
        // Invalidate and refetch messages for this session
        queryClient.invalidateQueries({ 
          queryKey: ["chat-messages", variables.sessionId] 
        });
        
        // Update session timestamp
        queryClient.invalidateQueries({ 
          queryKey: ["chat-session", variables.sessionId] 
        });
        queryClient.invalidateQueries({ 
          queryKey: ["chat-sessions"] 
        });
      },
    });
  };

  // Delete chat session
  // Note: This also invalidates related analysis and list queries to ensure
  // card button states update properly in tabs after session deletion
  const useDeleteChatSession = () => {
    return useMutation({
      mutationFn: async (sessionId: string): Promise<void> => {
        if (!supabase || !userId) throw new Error("User not authenticated");

        // ✅ SIMPLIFIED: Database CASCADE DELETE will handle related analyses automatically
        // Just delete the session - analyses will be cascade deleted by foreign key constraint
        const { error } = await supabase
          .from("ai_chat_sessions")
          .delete()
          .eq("id", sessionId)
          .eq("user_id", userId);

        if (error) {
          throw new Error(`Error deleting session: ${error.message}`);
        }
      },
      onMutate: async (sessionId) => {
        // ✅ OPTIMISTIC UPDATE: Remove from cache immediately
        
        // Get the session data before deletion to know its context
        const sessionData = queryClient.getQueryData<ChatSession>(["chat-session", sessionId]);
        
        if (sessionData?.context_id) {
          // Remove context-specific session cache immediately
          queryClient.removeQueries({ 
            queryKey: ["chat-session", sessionData.chat_type, sessionData.context_id] 
          });
        }

        // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
        await queryClient.cancelQueries({ queryKey: ["chat-sessions"] });
        await queryClient.cancelQueries({ queryKey: ["chat-session"] });

        // Optimistically remove from sessions list
        queryClient.setQueriesData<ChatSession[]>(
          { queryKey: ["chat-sessions"] },
          (old) => old?.filter(session => session.id !== sessionId) || []
        );

        return { sessionData };
      },
      onSuccess: (_, sessionId, context) => {
        // ✅ COMPREHENSIVE CACHE CLEANUP after successful deletion
        
        // Remove messages cache for this session
        queryClient.removeQueries({ queryKey: ["chat-messages", sessionId] });
        
        // Remove specific session cache
        queryClient.removeQueries({ queryKey: ["chat-session", sessionId] });
        
        // If we have the session context from onMutate, clear context cache
        if (context?.sessionData?.context_id) {
          queryClient.removeQueries({ 
            queryKey: ["chat-session", context.sessionData.chat_type, context.sessionData.context_id] 
          });

          // ✅ SIMPLIFIED: Just invalidate everything related to analysis and lists
          queryClient.invalidateQueries({ queryKey: ["lab-analysis"] });
          queryClient.invalidateQueries({ queryKey: ["medication-analysis"] });
          queryClient.invalidateQueries({ queryKey: ["labresults"] });
          queryClient.invalidateQueries({ queryKey: ["user_meds"] });
        }
        
        // Invalidate all session lists to refresh components
        queryClient.invalidateQueries({ queryKey: ["chat-sessions"] });
        
        // Invalidate all remaining session queries
        queryClient.invalidateQueries({ 
          queryKey: ["chat-session"], 
          exact: false 
        });
      },
      onError: (error, sessionId, context) => {
        // ✅ REVERT OPTIMISTIC UPDATE on error
        if (context?.sessionData) {
          // Restore the session data
          queryClient.setQueryData(["chat-session", sessionId], context.sessionData);
          
          if (context.sessionData.context_id) {
            queryClient.setQueryData(
              ["chat-session", context.sessionData.chat_type, context.sessionData.context_id], 
              context.sessionData
            );
          }
        }
        
        // Refresh to get current state from server
        queryClient.invalidateQueries({ queryKey: ["chat-sessions"] });
      },
    });
  };

  return {
    useFetchChatSessions,
    useFetchSession,
    useFetchSessionByContext,
    useFetchMessages,
    useCreateChatSession,
    useCreateMessage,
    useDeleteChatSession,
  };
} 