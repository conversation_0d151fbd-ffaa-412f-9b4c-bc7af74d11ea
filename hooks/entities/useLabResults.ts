import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSupabaseQuery from '../useSupabaseQuery';
import { Tables, TablesInsert, TablesUpdate } from '@/types/database.types';
import useSupabaseStorageUpload from '../useSupabaseStorageUpload';
import { labResultFormSchema, LabResultFormData } from '@/schema/lab-result';

// Types
export type LabResult = Tables<'labresults'>;
export type LabResultWithPatient = LabResult & {
  patients: Pick<Tables<'patients'>, 'id' | 'name'> | null;
};

// Type for mutation input (combining form data and optional file info)
interface LabResultCreateMutationData {
  data: LabResultFormData;
  fileUri?: string;
  capturedPdfFileUri?: string;
}

interface LabResultUpdateMutationData {
  id: string;
  data: LabResultFormData;
  fileUri?: string;
  deleteFile?: boolean;
  capturedPdfFileUri?: string;
  deleteExistingCapturedPdf?: boolean;
}

// Hooks for lab result operations
export function useLabResults() {
  const { executeQuery } = useSupabaseQuery();
  const queryClient = useQueryClient();
  const { uploadFile } = useSupabaseStorageUpload();

  // Fetch all lab results for the current user
  const fetchAll = () => {
    return useQuery<LabResultWithPatient[], Error>({
      queryKey: ['labResults'],
      queryFn: () => executeQuery(async (supabase, userId) => {
        const { data, error } = await supabase
          .from('labresults')
          .select('*, patients(id, name)')
          .eq('user_id', userId)
          .order('result_date', { ascending: false });

        if (error) {
          throw new Error(`Error fetching lab results: ${error.message}`);
        }
        return data as LabResultWithPatient[];
      }),
    });
  };

  // Fetch a single lab result by ID
  const fetchById = (id?: string) => {
    return useQuery<LabResultWithPatient, Error>({
      queryKey: ['labResult', id],
      queryFn: () => executeQuery(async (supabase, userId) => {
        if (!id) {
          throw new Error('Lab result ID is required');
        }
        const { data, error } = await supabase
          .from('labresults')
          .select('*, patients(id, name)')
          .eq('id', id)
          .eq('user_id', userId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') { 
            throw new Error('Lab result not found or access denied.'); 
          }
          throw new Error(`Error fetching lab result: ${error.message}`);
        }
        return data as LabResultWithPatient;
      }),
      enabled: !!id,
    });
  };

  // Create a new lab result
  const create = () => {
    return useMutation<LabResult, Error, LabResultCreateMutationData>({
      mutationFn: async ({ data, fileUri, capturedPdfFileUri }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = labResultFormSchema.parse(data);

          // Upload primary attachment (image_path)
          let primaryFilePath: string | undefined;
          let primaryFileBucketId = 'labresults';
          if (fileUri) {
            const primaryFileExt = fileUri.split('.').pop()?.toLowerCase() || 'jpg';
            const primaryMimeType = primaryFileExt === 'pdf' ? 'application/pdf' : `image/${primaryFileExt === 'jpg' ? 'jpeg' : primaryFileExt}`;
            primaryFilePath = `${userId}/primary_attachments/${Date.now()}.${primaryFileExt}`;
            primaryFilePath = await uploadFile({
              fileUri,
              bucketId: primaryFileBucketId,
              path: primaryFilePath,
              fileType: primaryMimeType
            });
          }

          // Upload captured/local PDF attachment (captured_pdf_path)
          let capturedPdfPath: string | undefined;
          let capturedPdfBucket = 'labresults';
          if (capturedPdfFileUri) {
            const capturedPdfExt = capturedPdfFileUri.split('.').pop()?.toLowerCase() || 'pdf';
            capturedPdfPath = `${userId}/captured_pdfs/${Date.now()}.${capturedPdfExt}`;
            capturedPdfPath = await uploadFile({
              fileUri: capturedPdfFileUri,
              bucketId: capturedPdfBucket,
              path: capturedPdfPath,
              fileType: 'application/pdf'
            });
          }

          const labResultData: TablesInsert<'labresults'> = {
            ...validatedData,
            patient_name: validatedData.patient_name || null,
            website: validatedData.website || null,
            phone_number1: validatedData.phone_number1 || null,
            phone_number2: validatedData.phone_number2 || null,
            phone_number3: validatedData.phone_number3 || null,
            user_id: userId,
            image_path: primaryFilePath,
            image_bucket_id: primaryFilePath ? primaryFileBucketId : null,
            captured_pdf_path: capturedPdfPath,
            captured_pdf_bucket_id: capturedPdfPath ? capturedPdfBucket : null,
          };

          const { data: newLabResult, error } = await supabase
            .from('labresults')
            .insert(labResultData)
            .select()
            .single();

          if (error) {
            if (primaryFilePath) {
              await supabase.storage.from(primaryFileBucketId).remove([primaryFilePath]);
            }
            if (capturedPdfPath) {
              await supabase.storage.from(capturedPdfBucket).remove([capturedPdfPath]);
            }
            throw new Error(`Error creating lab result: ${error.message}`);
          }
          return newLabResult as LabResult;
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['labResults'] });
      }
    });
  };

  // Update an existing lab result
  const update = () => {
    return useMutation<LabResult, Error, LabResultUpdateMutationData>({
      mutationFn: async ({ id, data, fileUri, deleteFile = false, capturedPdfFileUri, deleteExistingCapturedPdf = false }) => {
        return executeQuery(async (supabase, userId) => {
          const validatedData = labResultFormSchema.parse(data);
          const { id: _discardId, ...updateData } = validatedData as any; 

          const { data: existingLabResult, error: fetchError } = await supabase
            .from('labresults')
            .select('image_path, image_bucket_id, captured_pdf_path, captured_pdf_bucket_id')
            .eq('id', id)
            .single();

          if (fetchError) {
            throw new Error(`Error fetching existing lab result for update: ${fetchError.message}`);
          }

          // --- Handle Primary Attachment (image_path) --- 
          let primaryFilePath: string | null = existingLabResult.image_path;
          let primaryImageBucketId: string | null = existingLabResult.image_bucket_id || 'labresults';

          if (deleteFile || fileUri) {
            if (primaryFilePath && primaryImageBucketId) {
              try {
                await supabase.storage.from(primaryImageBucketId).remove([primaryFilePath]);
              } catch (e) { console.warn('Failed to delete old primary file during update:', e); }
              primaryFilePath = null;
              primaryImageBucketId = null;
            }
            if (fileUri) {
              primaryImageBucketId = 'labresults'; 
              const fileExt = fileUri.split('.').pop()?.toLowerCase() || 'jpg';
              const newPrimaryPath = `${userId}/primary_attachments/${Date.now()}.${fileExt}`;
              primaryFilePath = await uploadFile({ fileUri, bucketId: primaryImageBucketId, path: newPrimaryPath, fileType: fileUri.endsWith('.pdf') ? 'application/pdf' : `image/${fileExt === 'jpg' ? 'jpeg' : fileExt}` });
            }
          }

          // --- Handle Captured PDF Attachment (captured_pdf_path) --- 
          let capturedPdfPathValue: string | null = existingLabResult.captured_pdf_path;
          let capturedPdfBucketIdValue: string | null = existingLabResult.captured_pdf_bucket_id || 'labresults';

          if (capturedPdfFileUri) {
            if (capturedPdfPathValue && capturedPdfBucketIdValue) {
              try {
                await supabase.storage.from(capturedPdfBucketIdValue).remove([capturedPdfPathValue]);
              } catch (e) { console.warn('Failed to delete old captured PDF during update:', e); }
            }
            capturedPdfBucketIdValue = 'labresults';
            const capturedPdfExt = capturedPdfFileUri.split('.').pop()?.toLowerCase() || 'pdf';
            const newCapturedPath = `${userId}/captured_pdfs/${Date.now()}.${capturedPdfExt}`;
            capturedPdfPathValue = await uploadFile({ fileUri: capturedPdfFileUri, bucketId: capturedPdfBucketIdValue, path: newCapturedPath, fileType: 'application/pdf' });
          } else if (deleteExistingCapturedPdf && capturedPdfPathValue && capturedPdfBucketIdValue) {
            try {
              await supabase.storage.from(capturedPdfBucketIdValue).remove([capturedPdfPathValue]);
            } catch (e) { console.warn('Failed to delete existing captured PDF:', e); }
            capturedPdfPathValue = null;
            capturedPdfBucketIdValue = null;
          }

          const labResultData: TablesUpdate<'labresults'> = {
            ...updateData,
            patient_name: updateData.patient_name || null,
            website: updateData.website || null,
            phone_number1: updateData.phone_number1 || null,
            phone_number2: updateData.phone_number2 || null,
            phone_number3: updateData.phone_number3 || null,
            updated_at: new Date().toISOString(),
            image_path: primaryFilePath,
            image_bucket_id: primaryFilePath ? primaryImageBucketId : null,
            captured_pdf_path: capturedPdfPathValue,
            captured_pdf_bucket_id: capturedPdfPathValue ? capturedPdfBucketIdValue : null,
          };

          const { data: updatedLabResult, error } = await supabase
            .from('labresults')
            .update(labResultData)
            .eq('id', id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) {
            if (fileUri && primaryFilePath && primaryFilePath !== existingLabResult.image_path && primaryImageBucketId) {
              await supabase.storage.from(primaryImageBucketId).remove([primaryFilePath]);
            }
            if (capturedPdfFileUri && capturedPdfPathValue && capturedPdfPathValue !== existingLabResult.captured_pdf_path && capturedPdfBucketIdValue){
                 await supabase.storage.from(capturedPdfBucketIdValue).remove([capturedPdfPathValue]);
            }
            throw new Error(`Error updating lab result: ${error.message}`);
          }
          return updatedLabResult as LabResult;
        });
      },
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['labResults'] });
        queryClient.invalidateQueries({ queryKey: ['labResult', variables.id] });
      }
    });
  };

  // Delete a lab result
  const remove = () => {
    return useMutation<{ success: boolean; id: string }, Error, { id: string }>({
      mutationFn: async ({ id }) => {
        return executeQuery(async (supabase, userId) => {
          const { data: labResult, error: fetchError } = await supabase
            .from('labresults')
            .select('image_path, image_bucket_id, captured_pdf_path, captured_pdf_bucket_id')
            .eq('id', id)
            .eq('user_id', userId)
            .single();

          if (fetchError) {
             if (fetchError.code === 'PGRST116') { 
              console.warn(`Attempted to delete non-existent or unauthorized lab result: ${id}`);
              return { success: false, id }; 
            } 
            throw new Error(`Error fetching lab result for deletion: ${fetchError.message}`);
          }
          
          if (labResult.image_path && labResult.image_bucket_id) {
            try {
              await supabase.storage.from(labResult.image_bucket_id).remove([labResult.image_path]);
            } catch (storageError) {
                console.warn(`Failed to delete primary storage file ${labResult.image_bucket_id}/${labResult.image_path}:`, storageError);
            }
          }

          if (labResult.captured_pdf_path && labResult.captured_pdf_bucket_id) {
            try {
              await supabase.storage.from(labResult.captured_pdf_bucket_id).remove([labResult.captured_pdf_path]);
            } catch (storageError) {
                console.warn(`Failed to delete captured PDF storage file ${labResult.captured_pdf_bucket_id}/${labResult.captured_pdf_path}:`, storageError);
            }
          }

          const { error } = await supabase
            .from('labresults')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (error) {
             if (error.code === 'PGRST116' || (error.details && error.details.includes('Results contain 0 rows'))) {
               console.warn(`Attempted to delete non-existent or unauthorized lab result (again?): ${id}`);
              return { success: false, id };
            }
            throw new Error(`Error deleting lab result: ${error.message}`);
          }
          return { success: true, id };
        });
      },
      onSuccess: (result, variables) => {
         if (result.success) {
            queryClient.invalidateQueries({ queryKey: ['labResults'] });
            queryClient.removeQueries({ queryKey: ['labResult', variables.id] });
         }
      }
    });
  };

  return {
    useFetchLabResults: fetchAll,
    useFetchLabResult: fetchById,
    useCreateLabResult: create,
    useUpdateLabResult: update,
    useDeleteLabResult: remove
  };
}

export default useLabResults; 