import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '../useSupabaseClient'; // Corrected relative path
import { WorkingHours } from '@/schema/working-hours'; // Assuming this path is correct

// Interface for the structure of an AllDoctor object based on alldoctors table
export interface AllDoctor {
  id: string;
  name: string;
  specialty?: string | null;
  workplace?: string | null;
  city?: string | null;
  workplace_address?: string | null;
  working_hours?: WorkingHours | null; // From schema/working-hours.ts
  created_at?: string;
  phone_number?: string | null;
  fee?: number | null;
}

// Keys for react-query
const allDoctorsQueryKey = ['allDoctors'];

/**
 * Custom hook to manage fetching 'alldoctors' data.
 */
export function useAllDoctors() {
  const { supabase } = useSupabaseClient(); // Get Supabase client via the hook
  const queryClient = useQueryClient();

  /**
   * Fetches all doctors from the 'alldoctors' table.
   */
  async function fetchAllDoctors(): Promise<AllDoctor[]> {
    if (!supabase) {
      throw new Error('Supabase client is not available.');
    }
    const { data, error } = await supabase.from('alldoctors').select('*');

    if (error) {
      console.error('Error fetching all doctors:', error);
      throw new Error(error.message || 'Could not fetch all doctors');
    }
    return data as AllDoctor[];
  }

  // React Query hook for fetching all doctors
  const useFetchAllDoctors = () =>
    useQuery<AllDoctor[], Error>({
      queryKey: allDoctorsQueryKey,
      queryFn: fetchAllDoctors,
      enabled: !!supabase, // Only run query if supabase client is available
    });

  return { useFetchAllDoctors, allDoctorsQueryKey };
} 