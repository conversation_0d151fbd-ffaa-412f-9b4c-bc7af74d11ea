import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '../useSupabaseClient';

// Enum types based on database schema
export type DosageUnitEnum = 
  | "mg" | "g" | "mcg" | "kg" | "ng" | "IU" | "mEq" | "mmol" | "mL" | "L" 
  | "tsp" | "tbsp" | "drop" | "cc" | "tablet" | "capsule" | "puff" | "suppository" 
  | "patch" | "ampoule" | "vial" | "unit" | "spray" | "dropperful" | "lozenge" 
  | "troche" | "film";

export type DurationUnitEnum = "days" | "weeks" | "months" | "Ongoing" | "As needed";

export type FrequencyUnitEnum = 
  | "daily" | "times daily" | "hours" | "weekly" | "Monthly" | "As needed" 
  | "Before meals" | "After meals" | "With meals" | "At bedtime";

// Interface for the structure of an AllMedicine object based on allmeds table
export interface AllMedicine {
  id: string;
  name: string;
  image_bucket_id?: string | null;
  image_path?: string | null;
  image_metadata?: any | null; // JSONB, consider a more specific type if available
  price?: number | null;
  description?: string | null;
  created_at?: string;
  dosage_amount?: number | null;
  dosage_unit?: DosageUnitEnum | null;
  frequency_amount?: number | null;
  frequency_unit?: FrequencyUnitEnum | null;
  duration_amount?: number | null;
  duration_unit?: DurationUnitEnum | null;
}

// Keys for react-query
const allMedicinesQueryKey = ['allMedicines'];

/**
 * Custom hook to manage fetching 'allmeds' data.
 */
export function useAllMedicines() {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();

  /**
   * Fetches all medicines from the 'allmeds' table.
   */
  async function fetchAllMedicines(): Promise<AllMedicine[]> {
    if (!supabase) {
      throw new Error('Supabase client is not available.');
    }
    const { data, error } = await supabase.from('allmeds').select('*');

    if (error) {
      console.error('Error fetching all medicines:', error);
      throw new Error(error.message || 'Could not fetch all medicines');
    }
    return data as AllMedicine[];
  }

  // React Query hook for fetching all medicines
  const useFetchAllMedicines = () =>
    useQuery<AllMedicine[], Error>({
      queryKey: allMedicinesQueryKey,
      queryFn: fetchAllMedicines,
      enabled: !!supabase, // Only run query if supabase client is available
    });

  return { useFetchAllMedicines, allMedicinesQueryKey };
} 