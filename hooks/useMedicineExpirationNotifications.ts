import { useMemo } from 'react';
import { Alert } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { differenceInDays, isToday, isTomorrow, format } from 'date-fns';
import { useMedicines, MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useAppAlerts } from '@/hooks/useAppAlerts';

export interface ExpiringMedicine extends MedicineWithRelations {
  daysUntilExpiration: number;
  expirationStatus: 'expired' | 'today' | 'tomorrow' | 'soon' | 'normal';
}

export interface ExpirationNotification {
  type: 'expired' | 'today' | 'tomorrow' | 'soon';
  title: string;
  message: string;
  medicines: ExpiringMedicine[];
  priority: 'high' | 'medium' | 'low';
}

// Configuration for notification periods
export const NOTIFICATION_CONFIG = {
  EXPIRED: { days: 0, priority: 'high' as const },
  TODAY: { days: 0, priority: 'high' as const },
  TOMORROW: { days: 1, priority: 'medium' as const },
  SOON: { days: 3, priority: 'low' as const },
} as const;

export function useMedicineExpirationNotifications() {
  const { useFetchMedicines } = useMedicines();
  const { data: medicines, isLoading, error } = useFetchMedicines();
  const { showError } = useAppAlerts();

  // Process medicines and categorize by expiration status
  const expiringMedicines = useMemo(() => {
    if (!medicines) return [];

    const today = new Date();
    
    return medicines
      .filter(med => med.expiration_date) // Only medicines with expiration dates
      .map(med => {
        const expirationDate = new Date(med.expiration_date!);
        const daysUntilExpiration = differenceInDays(expirationDate, today);
        
        let expirationStatus: ExpiringMedicine['expirationStatus'];
        
        if (daysUntilExpiration < 0) {
          expirationStatus = 'expired';
        } else if (isToday(expirationDate)) {
          expirationStatus = 'today';
        } else if (isTomorrow(expirationDate)) {
          expirationStatus = 'tomorrow';
        } else if (daysUntilExpiration <= 3) {
          expirationStatus = 'soon';
        } else {
          expirationStatus = 'normal';
        }

        return {
          ...med,
          daysUntilExpiration,
          expirationStatus,
        } as ExpiringMedicine;
      })
      .filter(med => med.expirationStatus !== 'normal'); // Only return medicines that need attention
  }, [medicines]);

  // Group medicines by expiration status
  const groupedByStatus = useMemo(() => {
    const groups = {
      expired: [] as ExpiringMedicine[],
      today: [] as ExpiringMedicine[],
      tomorrow: [] as ExpiringMedicine[],
      soon: [] as ExpiringMedicine[],
    };

    expiringMedicines.forEach(med => {
      if (med.expirationStatus !== 'normal') {
        groups[med.expirationStatus].push(med);
      }
    });

    return groups;
  }, [expiringMedicines]);

  // Generate notifications based on grouped medicines
  const notifications = useMemo(() => {
    const notifs: ExpirationNotification[] = [];

    // Expired medicines
    if (groupedByStatus.expired.length > 0) {
      notifs.push({
        type: 'expired',
        title: '⚠️ Expired Medications',
        message: `${groupedByStatus.expired.length} medication${groupedByStatus.expired.length > 1 ? 's have' : ' has'} expired and should be disposed of safely.`,
        medicines: groupedByStatus.expired,
        priority: 'high',
      });
    }

    // Expiring today
    if (groupedByStatus.today.length > 0) {
      notifs.push({
        type: 'today',
        title: '🚨 Expiring Today',
        message: `${groupedByStatus.today.length} medication${groupedByStatus.today.length > 1 ? 's expire' : ' expires'} today. Check if you need refills.`,
        medicines: groupedByStatus.today,
        priority: 'high',
      });
    }

    // Expiring tomorrow
    if (groupedByStatus.tomorrow.length > 0) {
      notifs.push({
        type: 'tomorrow',
        title: '⏰ Expiring Tomorrow',
        message: `${groupedByStatus.tomorrow.length} medication${groupedByStatus.tomorrow.length > 1 ? 's expire' : ' expires'} tomorrow. Consider getting refills.`,
        medicines: groupedByStatus.tomorrow,
        priority: 'medium',
      });
    }

    // Expiring soon (within 3 days)
    if (groupedByStatus.soon.length > 0) {
      notifs.push({
        type: 'soon',
        title: '📅 Expiring Soon',
        message: `${groupedByStatus.soon.length} medication${groupedByStatus.soon.length > 1 ? 's expire' : ' expires'} within the next 3 days.`,
        medicines: groupedByStatus.soon,
        priority: 'low',
      });
    }

    return notifs.sort((a, b) => {
      // Sort by priority: high > medium > low
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [groupedByStatus]);

  // Get total count of medicines needing attention
  const totalExpiringCount = useMemo(() => {
    return expiringMedicines.length;
  }, [expiringMedicines]);

  // Get highest priority notification
  const highestPriorityNotification = useMemo(() => {
    return notifications.find(notif => notif.priority === 'high') || notifications[0] || null;
  }, [notifications]);

  // Function to show alert for a specific notification
  const showNotificationAlert = (notification: ExpirationNotification) => {
    const medicineList = notification.medicines
      .map(med => `• ${med.name}${med.patients ? ` (${med.patients.name})` : ''}`)
      .join('\n');

    Alert.alert(
      notification.title,
      `${notification.message}\n\nMedicines:\n${medicineList}`,
      [
        { text: 'OK', style: 'default' },
        { text: 'View Details', onPress: () => {/* Navigate to notifications tab */} },
      ]
    );
  };

  // Function to show summary alert of all notifications
  const showExpirationSummary = () => {
    if (notifications.length === 0) {
      Alert.alert('✅ All Good!', 'No medications are expiring soon.');
      return;
    }

    const summary = notifications.map(notif => 
      `${notif.title}: ${notif.medicines.length} medicine${notif.medicines.length > 1 ? 's' : ''}`
    ).join('\n');

    Alert.alert(
      '💊 Medication Expiration Summary',
      summary,
      [
        { text: 'OK', style: 'default' },
        { text: 'View Details', onPress: () => {/* Navigate to notifications tab */} },
      ]
    );
  };

  // Function to get notification badge count
  const getNotificationBadgeCount = () => {
    return groupedByStatus.expired.length + groupedByStatus.today.length;
  };

  // Function to get medicines expiring on a specific date
  const getMedicinesExpiringOn = (date: Date) => {
    if (!medicines) return [];
    
    const targetDateString = format(date, 'yyyy-MM-dd');
    
    return medicines.filter(med => {
      if (!med.expiration_date) return false;
      const medExpirationDate = format(new Date(med.expiration_date), 'yyyy-MM-dd');
      return medExpirationDate === targetDateString;
    });
  };

  // Function to check if notifications should be shown (for background tasks)
  const shouldShowNotifications = () => {
    return notifications.some(notif => notif.priority === 'high');
  };

  return {
    // Data
    expiringMedicines,
    groupedByStatus,
    notifications,
    totalExpiringCount,
    highestPriorityNotification,
    
    // Loading states
    isLoading,
    error,
    
    // Functions
    showNotificationAlert,
    showExpirationSummary,
    getNotificationBadgeCount,
    getMedicinesExpiringOn,
    shouldShowNotifications,
    
    // Utility functions
    hasExpiredMedicines: () => groupedByStatus.expired.length > 0,
    hasExpiringToday: () => groupedByStatus.today.length > 0,
    hasExpiringTomorrow: () => groupedByStatus.tomorrow.length > 0,
    hasExpiringSoon: () => groupedByStatus.soon.length > 0,
  };
}
