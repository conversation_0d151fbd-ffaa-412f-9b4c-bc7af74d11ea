import { Alert, Platform } from 'react-native';

export interface ConfirmationOptions {
  title: string;
  message: string;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  style?: 'default' | 'destructive'; // iOS only for button style
}

export function useAppAlerts() {
  const showSuccess = (message: string, title: string = 'Success') => {
    Alert.alert(title, message);
  };

  const showError = (message: string, title: string = 'Error') => {
    Alert.alert(title, message);
  };

  const confirm = ({
    title,
    message,
    onConfirm,
    confirmText = 'OK',
    cancelText = 'Cancel',
    style = 'default',
  }: ConfirmationOptions) => {
    Alert.alert(
      title,
      message,
      [
        {
          text: cancelText,
          style: 'cancel',
        },
        {
          text: confirmText,
          onPress: onConfirm,
          style: style,
        },
      ],
      { cancelable: Platform.OS === 'android' } // On Android, dialogs are cancelable by default by tapping outside
    );
  };

  return { showSuccess, showError, confirm };
} 