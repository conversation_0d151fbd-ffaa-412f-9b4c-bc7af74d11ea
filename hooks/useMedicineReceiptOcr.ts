import { useCallback } from 'react';
import * as FileSystem from 'expo-file-system';
import { useSupabaseClient } from './useSupabaseClient';
import { useOcrStore } from '@/store/ocrStore';

interface ReceiptOcrResponse {
  success: boolean;
  data?: {
    rawText: string;
    extractedMedicines: Array<{
      name: string;
      price?: number;
      description?: string;
    }>;
    confidence: number;
    processingTime: number;
  };
  error?: string;
}

export const useMedicineReceiptOcr = () => {
  const { supabase } = useSupabaseClient();
  const { 
    setProcessing, 
    setOcrError, 
    setExtractedMedicines
  } = useOcrStore();

  const processReceipt = useCallback(async (imageUri: string) => {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    setProcessing(true);
    setOcrError(null);
    
    try {
      // Convert image to base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Call the medicine receipt OCR edge function using Supabase client
      const { data: result, error: invokeError } = await supabase.functions.invoke('medicine-receipt-ocr', {
        body: {
          imageBase64: base64
        }
      });

      if (invokeError) {
        throw new Error(`OCR request failed: ${invokeError.message}`);
      }
      
      if (!result.success) {
        throw new Error(result.error || 'OCR processing failed');
      }

      if (result.data) {
        setExtractedMedicines(result.data.extractedMedicines);
        return result.data;
      } else {
        throw new Error('No data received from OCR');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setOcrError(errorMessage);
      throw error;
    } finally {
      setProcessing(false);
    }
  }, [supabase, setProcessing, setOcrError, setExtractedMedicines]);

  return { processReceipt };
}; 