import { useTranslation as useI18nTranslation } from 'react-i18next';
import { getCurrentLanguage, changeLanguage } from '@/i18n';

/**
 * Custom hook for translations with better type safety and convenience methods
 */
export function useTranslation() {
  const { t, i18n } = useI18nTranslation();

  return {
    // Translation function
    t,
    
    // Current language
    currentLanguage: getCurrentLanguage(),
    
    // Change language function
    changeLanguage,
    
    // Check if current language is specific language
    isEnglish: () => getCurrentLanguage().startsWith('en'),
    isBulgarian: () => getCurrentLanguage().startsWith('bg'),
    
    // i18n instance for advanced usage
    i18n,
  };
}

/**
 * Hook specifically for navigation/screen titles
 */
export function useNavigationTranslation() {
  const { t } = useTranslation();
  
  return {
    prescriptions: t('navigation.prescriptions'),
    medicines: t('navigation.medicines'),
    allMedicines: t('navigation.allMedicines'),
    doctors: t('navigation.doctors'),
    allDoctors: t('navigation.allDoctors'),
    patients: t('navigation.patients'),
    labResults: t('navigation.labResults'),
    notifications: t('navigation.notifications'),
    settings: t('navigation.settings'),
    aiChat: t('navigation.aiChat'),
  };
}

/**
 * Hook for common UI elements
 */
export function useCommonTranslation() {
  const { t } = useTranslation();
  
  return {
    save: t('common.save'),
    cancel: t('common.cancel'),
    loading: t('common.loading'),
    retry: t('common.retry'),
    tryAgain: t('common.tryAgain'),
    back: t('common.back'),
    done: t('common.done'),
    ok: t('common.ok'),
    yes: t('common.yes'),
    no: t('common.no'),
    delete: t('common.delete'),
    edit: t('common.edit'),
    view: t('common.view'),
    add: t('common.add'),
    create: t('common.create'),
    remove: t('common.remove'),
    search: t('common.search'),
    noResults: t('common.noResults'),
    error: t('common.error'),
    success: t('common.success'),
  };
} 