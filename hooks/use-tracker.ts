import { useQuery } from '@tanstack/react-query';
import useSupabaseQuery from './useSupabaseQuery';
import { Tables } from '@/types/database.types';

// Types
type TimeRange = 'week' | 'month' | '3months' | 'year' | 'all';

export interface CostStats {
  totalMedicineCost: number;
  totalDoctorFees: number;
  totalCost: number;
  topMedicines: Array<{
    name: string;
    totalCost: number;
    count: number;
  }>;
}

export interface HealthStats {
  prescriptionsCount: number;
  labResultsCount: number;
  medicationsCount: number;
}

export interface TrackerStats {
  costStats: CostStats;
  healthStats: HealthStats;
}

export interface TrackerData {
  costStats: CostStats;
  healthStats: HealthStats;
  patients: Array<Pick<Tables<'patients'>, 'id' | 'name'>>;
  isLoading: boolean;
  refetch: () => void;
}

// Separate hook for patients data with longer cache time
export function usePatients() {
  const { executeQuery } = useSupabaseQuery();
  
  return useQuery({
    queryKey: ['patients'],
    queryFn: () => executeQuery(async (supabase, userId) => {
      const { data, error } = await supabase
        .from('patients')
        .select('id, name')
        .eq('user_id', userId)
        .order('name');

      if (error) {
        throw new Error(`Error fetching patients: ${error.message}`);
      }

      return data || [];
    }),
    staleTime: 10 * 60 * 1000, // 10 minutes - patients rarely change
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache longer
    refetchOnWindowFocus: false,
  });
}

function getDateFilter(timeRange: TimeRange): string | null {
  const now = new Date();
  
  switch (timeRange) {
    case 'week':
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      return oneWeekAgo.toISOString();
    case 'month':
      const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
      return oneMonthAgo.toISOString();
    case '3months':
      const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
      return threeMonthsAgo.toISOString();
    case 'year':
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
      return oneYearAgo.toISOString();
    case 'all':
    default:
      return null;
  }
}

export function useTrackerData(selectedPatient: string, timeRange: TimeRange): TrackerData {
  const { executeQuery } = useSupabaseQuery();
  const dateFilter = getDateFilter(timeRange);
  const { data: patients } = usePatients();

  const { data, isLoading, refetch } = useQuery<TrackerStats>({
    queryKey: ['tracker-stats', selectedPatient, timeRange],
    queryFn: () => executeQuery(async (supabase, userId) => {
      // Build all queries (excluding patients - fetched separately)
      // Optimize medicine query to reduce data transfer but keep filter fields
      let medicineQuery = supabase
        .from('user_meds')
        .select('name, price, created_at, patient_id')
        .eq('user_id', userId)
        .not('price', 'is', null);

      // Optimize prescription query to only fetch needed fields but keep filter fields
      let prescriptionQuery = supabase
        .from('prescriptions')
        .select('created_at, patient_id, user_doctors!inner(fee)')
        .eq('user_id', userId)
        .not('user_doctors.fee', 'is', null);

      let healthPrescriptionQuery = supabase
        .from('prescriptions')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);

      let labResultsQuery = supabase
        .from('labresults')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);

      let medicationsQuery = supabase
        .from('user_meds')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Apply patient filters
      if (selectedPatient !== 'all') {
        medicineQuery = medicineQuery.eq('patient_id', selectedPatient);
        prescriptionQuery = prescriptionQuery.eq('patient_id', selectedPatient);
        healthPrescriptionQuery = healthPrescriptionQuery.eq('patient_id', selectedPatient);
        labResultsQuery = labResultsQuery.eq('patient_reference_id', selectedPatient);
        medicationsQuery = medicationsQuery.eq('patient_id', selectedPatient);
      }

      // Apply date filters
      if (dateFilter) {
        medicineQuery = medicineQuery.gte('created_at', dateFilter);
        prescriptionQuery = prescriptionQuery.gte('created_at', dateFilter);
        healthPrescriptionQuery = healthPrescriptionQuery.gte('created_at', dateFilter);
        labResultsQuery = labResultsQuery.gte('created_at', dateFilter);
        medicationsQuery = medicationsQuery.gte('created_at', dateFilter);
      }

      // Execute all queries in parallel (excluding patients)
      const [
        { data: medicinesData, error: medicinesError },
        { data: prescriptionsWithFeesData, error: prescriptionsError },
        { count: prescriptionsCount, error: prescCountError },
        { count: labResultsCount, error: labCountError },
        { count: medicationsCount, error: medsCountError }
      ] = await Promise.all([
        medicineQuery,
        prescriptionQuery,
        healthPrescriptionQuery,
        labResultsQuery,
        medicationsQuery
      ]);

      // Check for errors
      if (medicinesError) throw new Error(`Error fetching medicines: ${medicinesError.message}`);
      if (prescriptionsError) throw new Error(`Error fetching prescriptions with fees: ${prescriptionsError.message}`);
      if (prescCountError) throw new Error(`Error counting prescriptions: ${prescCountError.message}`);
      if (labCountError) throw new Error(`Error counting lab results: ${labCountError.message}`);
      if (medsCountError) throw new Error(`Error counting medications: ${medsCountError.message}`);

      // Calculate cost statistics
      const totalMedicineCost = medicinesData?.reduce((sum, med) => sum + (med.price || 0), 0) || 0;
      const totalDoctorFees = prescriptionsWithFeesData?.reduce((sum, presc: any) => 
        sum + (presc.user_doctors?.fee || 0), 0) || 0;

      // Calculate top medicines
      const medicineMap = new Map<string, { totalCost: number; count: number }>();
      medicinesData?.forEach(med => {
        const current = medicineMap.get(med.name) || { totalCost: 0, count: 0 };
        medicineMap.set(med.name, {
          totalCost: current.totalCost + (med.price || 0),
          count: current.count + 1
        });
      });

      const topMedicines = Array.from(medicineMap.entries())
        .map(([name, stats]) => ({ name, ...stats }))
        .sort((a, b) => b.totalCost - a.totalCost)
        .slice(0, 5);

      return {
        costStats: {
          totalMedicineCost,
          totalDoctorFees,
          totalCost: totalMedicineCost + totalDoctorFees,
          topMedicines
        },
        healthStats: {
          prescriptionsCount: prescriptionsCount || 0,
          labResultsCount: labResultsCount || 0,
          medicationsCount: medicationsCount || 0
        }
      } as TrackerStats;
    }),
    staleTime: 2 * 60 * 1000, // 2 minutes - data is relatively stable
    gcTime: 5 * 60 * 1000, // 5 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch when user returns to app
  });

  return {
    costStats: data?.costStats || {
      totalMedicineCost: 0,
      totalDoctorFees: 0,
      totalCost: 0,
      topMedicines: []
    },
    healthStats: data?.healthStats || {
      prescriptionsCount: 0,
      labResultsCount: 0,
      medicationsCount: 0
    },
    patients: patients || [],
    isLoading,
    refetch
  };
}