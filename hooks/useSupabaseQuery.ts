import { useSupabaseClient } from './useSupabaseClient';
import { useAuth } from '@clerk/clerk-expo';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';

/**
 * A utility hook that provides standardized access to Supabase with proper error handling
 * and authentication checks.
 */
export function useSupabaseQuery() {
  const { supabase } = useSupabaseClient();
  const { userId } = useAuth();

  /**
   * Execute a query function with proper error handling
   */
  const executeQuery = async <T>(
    queryFn: (supabase: SupabaseClient<Database>, userId: string) => Promise<T>
  ): Promise<T> => {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    if (!supabase) {
      throw new Error('Supabase client not initialized');
    }

    return queryFn(supabase, userId);
  };

  return {
    executeQuery,
    supabase,
    userId,
  };
}

export default useSupabaseQuery; 