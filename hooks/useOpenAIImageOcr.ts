import { useState } from 'react';
import { useSupabaseClient } from './useSupabaseClient';

export interface OpenAIOcrResults {
  labName?: string;
  resultDate?: string;
  patientName?: string;
  patientId?: string;
  website?: string;
  phoneNumber1?: string;
  phoneNumber2?: string;
  phoneNumber3?: string;
  password?: string;
}

export interface UseOpenAIImageOcrResult {
  performOcr: (
    uri: string
  ) => Promise<{ rawText: string; extractedData: OpenAIOcrResults } | null>;
  isProcessingOcr: boolean;
  ocrError: Error | null;
}

// Convert image URI to base64
const convertImageToBase64 = async (uri: string): Promise<{ data: string; mimeType: string }> => {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64Data = result.split(',')[1]; // Remove data:image/...;base64, prefix
        const mimeType = blob.type || 'image/jpeg';
        resolve({ data: base64Data, mimeType });
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new Error(`Failed to convert image to base64: ${error}`);
  }
};

export function useOpenAIImageOcr(): UseOpenAIImageOcrResult {
  const [isProcessingOcr, setIsProcessingOcr] = useState(false);
  const [ocrError, setOcrError] = useState<Error | null>(null);
  const { supabase } = useSupabaseClient();

  const performOcr = async (
    uri: string
  ): Promise<{ rawText: string; extractedData: OpenAIOcrResults } | null> => {
    setIsProcessingOcr(true);
    setOcrError(null);

    try {
      // Convert image to base64
      const { data: imageData, mimeType } = await convertImageToBase64(uri);

      // Check if Supabase client is available
      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      // Call the edge function using Supabase client
      const { data, error } = await supabase.functions.invoke('image-ocr-openai', {
        body: {
          imageData,
          mimeType,
        },
      });

      if (error) {
        throw new Error(error.message || 'Edge function invocation failed');
      }

      const result = data;

      if (!result.success) {
        throw new Error(result.error || 'OCR processing failed');
      }

      return {
        rawText: result.data.rawText,
        extractedData: result.data.extractedData,
      };

    } catch (error: any) {
      console.error('OpenAI OCR error:', error);
      const ocrError = new Error(error.message || 'OCR processing failed');
      setOcrError(ocrError);
      return null;
    } finally {
      setIsProcessingOcr(false);
    }
  };

  return {
    performOcr,
    isProcessingOcr,
    ocrError,
  };
} 