import { useState, useCallback } from "react";
import { useAuth } from "@clerk/clerk-expo";
import { fetch } from "expo/fetch";

export function useAIChat() {
  const { userId, getToken } = useAuth();
  const [error, setError] = useState<string | null>(null);

  // Send message with streaming
  const sendMessage = useCallback(
    async (sessionId: string, content: string): Promise<ReadableStream> => {
      if (!userId) throw new Error("User not authenticated");

      setError(null);

      try {
        const token = await getToken();
        if (!token) throw new Error("No access token");

        const requestBody = {
          sessionId,
          message: content,
        };

        const response = await fetch(
          `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/chat-universal`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
              Accept: "text/event-stream",
            },
            body: JSON.stringify(requestBody),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Edge function error:", errorText);
          throw new Error(
            `HTTP error! status: ${response.status} - ${errorText}`
          );
        }

        if (!response.body) {
          throw new Error("No response body - streaming not supported");
        }

        return response.body;
      } catch (err) {
        const message =
          err instanceof Error ? err.message : "Failed to send message";
        console.error("sendMessage error:", err);
        setError(message);
        throw new Error(message);
      }
    },
    [userId, getToken]
  );

  return {
    error,
    sendMessage,
  };
} 