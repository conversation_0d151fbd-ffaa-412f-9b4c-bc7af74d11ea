import { useEffect, useRef, useCallback } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform, Alert } from 'react-native';
import { useMedicines } from '@/hooks/entities/useMedicines';
import { addDays, startOfDay, format, differenceInDays, isAfter, isBefore } from 'date-fns';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure how notifications are handled when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface MedicineExpirationNotificationConfig {
  enabled: boolean;
  notifyToday: boolean;
  notifyOneDayBefore: boolean;
  notifyThreeDaysBefore: boolean;
  notificationTime: string; // Format: "HH:mm" (24-hour format)
}

const DEFAULT_CONFIG: MedicineExpirationNotificationConfig = {
  enabled: true,
  notifyToday: true,
  notifyOneDayBefore: true,
  notifyThreeDaysBefore: true,
  notificationTime: "09:00", // 9 AM
};

const STORAGE_KEYS = {
  CONFIG: 'medicineNotificationConfig',
  SCHEDULED_NOTIFICATIONS: 'scheduledMedicineNotifications',
  LAST_SCHEDULED_DATE: 'lastScheduledNotificationDate',
};

export function useMedicineExpirationPushNotifications() {
  const { useFetchMedicines } = useMedicines();
  const { data: medicines, isLoading } = useFetchMedicines();
  const isInitialized = useRef(false);

  // Request notification permissions following Expo best practices
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
          },
        });
        finalStatus = status;
      }

      // Handle iOS provisional status and undetermined status
      const isGranted = finalStatus === 'granted';

      if (!isGranted) {
        Alert.alert(
          'Notification Permission Required',
          'Please enable notifications in your device settings to receive medication expiration alerts.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'OK', style: 'default' },
          ]
        );
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('medicine-expiration', {
          name: 'Medicine Expiration',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#3B82F6',
          description: 'Notifications for medication expiration reminders',
          sound: 'default',
        });
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }, []);

  // Get notification configuration
  const getConfig = useCallback(async (): Promise<MedicineExpirationNotificationConfig> => {
    try {
      const configJson = await AsyncStorage.getItem(STORAGE_KEYS.CONFIG);
      if (configJson) {
        return { ...DEFAULT_CONFIG, ...JSON.parse(configJson) };
      }
      return DEFAULT_CONFIG;
    } catch (error) {
      console.error('Error loading notification config:', error);
      return DEFAULT_CONFIG;
    }
  }, []);

  // Save notification configuration  
  const saveConfig = useCallback(async (config: MedicineExpirationNotificationConfig): Promise<void> => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CONFIG, JSON.stringify(config));
      // Note: Rescheduling will happen automatically via useEffect when medicines data changes
    } catch (error) {
      console.error('Error saving notification config:', error);
    }
  }, []);

  // Cancel all scheduled medicine expiration notifications
  const cancelAllNotifications = useCallback(async (): Promise<void> => {
    try {
      const scheduledIds = await AsyncStorage.getItem(STORAGE_KEYS.SCHEDULED_NOTIFICATIONS);
      if (scheduledIds) {
        const ids: string[] = JSON.parse(scheduledIds);
        await Promise.all(ids.map(id => Notifications.cancelScheduledNotificationAsync(id)));
        await AsyncStorage.removeItem(STORAGE_KEYS.SCHEDULED_NOTIFICATIONS);
        await AsyncStorage.removeItem(STORAGE_KEYS.LAST_SCHEDULED_DATE);
      }
    } catch (error) {
      console.error('Error canceling notifications:', error);
    }
  }, []);

  // Schedule notifications for medicine expiration
  const scheduleNotifications = useCallback(async (): Promise<void> => {
    if (!medicines || medicines.length === 0) return;

    try {
      const config = await getConfig();
      if (!config.enabled) return;

      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      // Cancel existing notifications
      await cancelAllNotifications();

      const now = new Date();
      const scheduledIds: string[] = [];
      const [hours, minutes] = config.notificationTime.split(':').map(Number);

      // Group medicines by expiration date
      const medicinesByExpirationDate = new Map<string, typeof medicines>();
      
      medicines.forEach(medicine => {
        if (medicine.expiration_date) {
          const expirationDateStr = format(new Date(medicine.expiration_date), 'yyyy-MM-dd');
          if (!medicinesByExpirationDate.has(expirationDateStr)) {
            medicinesByExpirationDate.set(expirationDateStr, []);
          }
          medicinesByExpirationDate.get(expirationDateStr)!.push(medicine);
        }
      });

      // Schedule notifications for each expiration date
      for (const [expirationDateStr, meds] of medicinesByExpirationDate) {
        const expirationDate = new Date(expirationDateStr);
        const today = startOfDay(now);
        const daysUntilExpiration = differenceInDays(expirationDate, today);

        // Skip if expiration date is in the past (more than 1 day ago)
        if (daysUntilExpiration < -1) continue;

        const medicineNames = meds.map(m => m.name).join(', ');
        const medicineCount = meds.length;

        // Schedule notifications based on config
        const notificationsToSchedule = [];

        // 3 days before expiration
        if (config.notifyThreeDaysBefore && daysUntilExpiration >= 3) {
          const notificationDate = addDays(expirationDate, -3);
          notificationDate.setHours(hours, minutes, 0, 0);
          
          if (isAfter(notificationDate, now)) {
            notificationsToSchedule.push({
              date: notificationDate,
              title: '📅 Medications Expiring Soon',
              body: `${medicineCount} medication${medicineCount > 1 ? 's' : ''} expire${medicineCount > 1 ? '' : 's'} in 3 days: ${medicineNames}`,
              data: { type: 'expiration', daysUntil: 3, medicines: meds.map(m => m.id) },
              categoryIdentifier: 'medicine-expiration',
            });
          }
        }

        // 1 day before expiration
        if (config.notifyOneDayBefore && daysUntilExpiration >= 1) {
          const notificationDate = addDays(expirationDate, -1);
          notificationDate.setHours(hours, minutes, 0, 0);
          
          if (isAfter(notificationDate, now)) {
            notificationsToSchedule.push({
              date: notificationDate,
              title: '⏰ Medications Expiring Tomorrow',
              body: `${medicineCount} medication${medicineCount > 1 ? 's' : ''} expire${medicineCount > 1 ? '' : 's'} tomorrow: ${medicineNames}`,
              data: { type: 'expiration', daysUntil: 1, medicines: meds.map(m => m.id) },
              categoryIdentifier: 'medicine-expiration',
            });
          }
        }

        // Day of expiration
        if (config.notifyToday && daysUntilExpiration >= 0) {
          const notificationDate = new Date(expirationDate);
          notificationDate.setHours(hours, minutes, 0, 0);
          
          if (isAfter(notificationDate, now)) {
            notificationsToSchedule.push({
              date: notificationDate,
              title: '🚨 Medications Expiring Today',
              body: `${medicineCount} medication${medicineCount > 1 ? 's' : ''} expire${medicineCount > 1 ? '' : 's'} today: ${medicineNames}`,
              data: { type: 'expiration', daysUntil: 0, medicines: meds.map(m => m.id) },
              categoryIdentifier: 'medicine-expiration',
            });
          }
        }

        // Schedule all notifications for this expiration date
        for (const notification of notificationsToSchedule) {
          try {
            const identifier = await Notifications.scheduleNotificationAsync({
              content: {
                title: notification.title,
                body: notification.body,
                data: notification.data,
                categoryIdentifier: notification.categoryIdentifier,
                sound: 'default',
                priority: Notifications.AndroidNotificationPriority.HIGH,
              },
              trigger: {
                type: Notifications.SchedulableTriggerInputTypes.DATE,
                date: notification.date,
              },
            });
            scheduledIds.push(identifier);
          } catch (error) {
            console.error('Error scheduling notification:', error);
          }
        }
      }

      // Save scheduled notification IDs
      await AsyncStorage.setItem(STORAGE_KEYS.SCHEDULED_NOTIFICATIONS, JSON.stringify(scheduledIds));
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SCHEDULED_DATE, now.toISOString());

      console.log(`Scheduled ${scheduledIds.length} medicine expiration notifications`);
    } catch (error) {
      console.error('Error scheduling notifications:', error);
    }
  }, [medicines, getConfig, requestPermissions, cancelAllNotifications]);

  // Check if notifications need to be rescheduled (called on app start)
  const checkAndRescheduleIfNeeded = useCallback(async (): Promise<void> => {
    try {
      const lastScheduledDate = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SCHEDULED_DATE);
      const now = new Date();
      
      // If no last scheduled date or it's been more than 24 hours, reschedule
      if (!lastScheduledDate || differenceInDays(now, new Date(lastScheduledDate)) >= 1) {
        await scheduleNotifications();
      }
    } catch (error) {
      console.error('Error checking notification schedule:', error);
    }
  }, [scheduleNotifications]);

  // Get all pending scheduled notifications
  const getPendingNotifications = useCallback(async () => {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync();
      return notifications.filter(n => 
        n.content.data?.type === 'expiration' || 
        n.content.categoryIdentifier === 'medicine-expiration'
      );
    } catch (error) {
      console.error('Error getting pending notifications:', error);
      return [];
    }
  }, []);

  // Initialize notifications on app start
  useEffect(() => {
    if (!isInitialized.current && !isLoading) {
      isInitialized.current = true;
      checkAndRescheduleIfNeeded();
    }
  }, [isLoading, checkAndRescheduleIfNeeded]);

  // Reschedule when medicines data changes
  useEffect(() => {
    if (medicines && !isLoading) {
      // Debounce rescheduling to avoid too frequent updates
      const timeoutId = setTimeout(() => {
        scheduleNotifications();
      }, 2000);

      return () => clearTimeout(timeoutId);
    }
  }, [medicines, isLoading, scheduleNotifications]);

  // Utility functions
  const isEnabled = useCallback(async () => {
    const config = await getConfig();
    return config.enabled;
  }, [getConfig]);
  
  const getScheduledNotificationCount = useCallback(async () => {
    const pending = await getPendingNotifications();
    return pending.length;
  }, [getPendingNotifications]);

  return {
    // Configuration management
    getConfig,
    saveConfig,
    
    // Notification management
    scheduleNotifications,
    cancelAllNotifications,
    checkAndRescheduleIfNeeded,
    getPendingNotifications,
    
    // Permission management
    requestPermissions,
    
    // Utility
    isEnabled,
    
    // For debugging
    getScheduledNotificationCount,
  };
}
