import { useQuery } from '@tanstack/react-query';
import { useSupabaseClient } from './useSupabaseClient'; // Adjust path if needed

interface UseSupabaseStorageUrlParams {
  bucketId: string | null | undefined;
  path: string | null | undefined;
  options?: {
    expiresIn?: number; // Expiry time in seconds
    // Add other Supabase storage options like 'transform' if needed later
  };
}

/**
 * Custom hook to get a signed URL for a Supabase storage object.
 * Handles loading and error states.
 */
export function useSupabaseStorageUrl({
  bucketId,
  path,
  options = { expiresIn: 3600 }, // Default expiry: 1 hour
}: UseSupabaseStorageUrlParams) {
  const { supabase } = useSupabaseClient();

  const queryKey = ['signedStorageUrl', bucketId, path, options.expiresIn];

  const fetchSignedUrlFn = async () => {
    if (!supabase || !bucketId || !path) {
      // Return null or throw an error based on how useQuery should handle it.
      // Returning null will result in `data: null` if query succeeds with these conditions.
      // Throwing an error will put the query in an `error` state.
      // For this use case, returning null might be preferable if the component can handle a null URL.
      console.warn('useSupabaseStorageUrl: Missing supabase client, bucketId, or path. Cannot fetch URL.');
      return null;
    }
    console.log(
      `useSupabaseStorageUrl: Fetching new signed URL for path: ${path} in bucket: ${bucketId}`
    );
    const { data, error: signedUrlError } = await supabase.storage
      .from(bucketId)
      .createSignedUrl(path, options.expiresIn || 3600);

    if (signedUrlError) {
      console.error('useSupabaseStorageUrl: Error getting signed URL:', signedUrlError);
      throw signedUrlError; // Let useQuery handle the error
    }

    if (data?.signedUrl) {
      console.log('useSupabaseStorageUrl: Got signed URL:', data.signedUrl);
      return data.signedUrl;
    }

    console.warn('useSupabaseStorageUrl: No signed URL data returned for path:', path);
    // Depending on strictness, either return null or throw an error.
    // Returning null if no URL is found but no explicit error from Supabase.
    return null;
  };

  const {
    data: url,
    isLoading,
    error,
    isError,
  } = useQuery<string | null, Error>({
    queryKey: queryKey,
    queryFn: fetchSignedUrlFn,
    enabled: !!supabase && !!bucketId && !!path, // Only run query if all dependencies are present
    // staleTime can be set here. For signed URLs, it's often good to set it slightly less than expiresIn.
    // e.g., staleTime: (options.expiresIn || 3600 - 300) * 1000, // 5 minutes before actual expiry
    // Or, if URLs are very stable and path/bucketId changes trigger refetch, staleTime: Infinity is also an option.
    // For now, let's use a staleTime that is 5 minutes less than the expiry time to allow proactive refetching.
    staleTime: Math.max(0, (options.expiresIn || 3600) - 300) * 1000, // 5 mins before expiry, ensure non-negative
    gcTime: (options.expiresIn || 3600) * 1000, // Cache for the duration of the URL's validity
    refetchOnWindowFocus: false, // Signed URLs usually don't need to refetch on window focus
  });

  return {
    url: url ?? null, // Ensure null is returned if data is undefined
    isLoading,
    error: isError ? error : null, // Provide error object only if isError is true
  };
}

export default useSupabaseStorageUrl;