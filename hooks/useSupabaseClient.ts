import { useMemo } from 'react';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/clerk-expo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-url-polyfill/auto'; // Needed for Supabase JS library
import { Database } from '@/types/database.types';

// Store client instance to avoid recreation on every hook call
// Note: This simple approach might not be ideal for all scenarios (e.g., token expiry edge cases)
// A more robust solution might involve context or a dedicated state management library.
let supabase: SupabaseClient<Database> | null = null; // Apply Database generic

export function useSupabaseClient() {
  const { getToken, isSignedIn } = useAuth();

  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    // Consider throwing an error or handling this more gracefully
    console.error('Supabase URL or Anon Key is missing from environment variables.');
    // Return a non-functional client or null
    return { supabase: null };
  }

  // Memoize the client creation based on URL, Key, and isSignedIn status
  // We re-memoize if isSignedIn changes to potentially re-evaluate accessToken
  const memoizedSupabase = useMemo(() => {
    // Only create client if one doesn't exist OR if the user just signed in
    // (This check might be overly cautious, depending on how accessToken handles null tokens)
    if (!supabase) {
      supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          storage: AsyncStorage,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
          flowType: 'pkce', // Recommended for mobile
        },
        global: {
          // Use a custom fetch wrapper to dynamically insert the Clerk token
          fetch: async (input, init) => {
            // Get the Clerk token
            const token = await getToken();

            // Clone headers if they exist, or create new ones
            const headers = new Headers(init?.headers);

            if (token) {
              // Add the Authorization header if token exists
              headers.set('Authorization', `Bearer ${token}`);
            }
            
            // Use default fetch with potentially modified headers
            // Use globalThis.fetch for broader compatibility (React Native vs Web)
            return globalThis.fetch(input, { ...init, headers });
          },
        },
      });
    }
    return supabase;
    // Include getToken in dependency array to ensure the latest version is used if the hook re-renders
  }, [supabaseUrl, supabaseAnonKey, getToken]); 

  // If not signed in, we might want to return null or a non-functional client explicitly
  // although the accessToken function handles returning null when not signed in.
  // For consistency, let's return the memoized client regardless.
  return { supabase: memoizedSupabase }; 
} 